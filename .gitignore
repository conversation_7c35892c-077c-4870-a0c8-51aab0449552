# See https://help.github.com/articles/ignoring-files for more about ignoring files.
#
# If you find yourself ignoring temporary files generated by your text editor
# or operating system, you probably want to add a global ignore instead:
#   git config --global core.excludesfile '~/.gitignore_global'

# Ignore bundler config.
/.bundle
/bundle

# Ignore all logfiles and tempfiles.
/log/*
/tmp/*
!/log/.keep
!/tmp/.keep
.DS_Store

# Ignore all local test artifacts
coverage/*
junit.xml
rspec.xml
spec/examples.txt
loans-*.csv

# Ignore pidfiles, but keep the directory.
/tmp/pids/*
!/tmp/pids/
!/tmp/pids/.keep

# Ignore uploaded files in development.
/storage/*
!/storage/.keep

/public/assets
.byebug_history

# Ignore master key for decrypting credentials and more.
/config/master.key

/public/packs
/public/packs-test
/node_modules
/vendor/bundle
/yarn-error.log
yarn-debug.log*
.yarn-integrity
.idea
.env
.env*local

# Ignore local authentication artifacts.
/public.key
/private.key

# Ignore VIM temp files
*.swp
*.swo

/docker-compose.override.yml
todo.txt
CSV_full_download_state_ca.csv

# Volumes that we mount with docker
/docker/minio

/app/assets/builds/*
!/app/assets/builds/.keep
