inherit_from: .rubocop_todo.yml

AllCops:
  Exclude:
    - 'bin/**/*'
    - 'vendor/**/*'
    - 'db/**/*'
    - 'node_modules/**/*'
    - 'lib/extensions/**/*'
    - 'bundle/**/*'
    - 'tmp/**/*'

Metrics/BlockLength:
  Enabled: false

Style/Documentation:
  Enabled: false

Metrics/MethodLength:
  Max: 32
  AllowedMethods:
    - 'build_hash'
    - 'setup_fixture_tempfile'
    - 'engine_condition'

Metrics/ClassLength:
  Max: 150

Metrics/AbcSize:
  Enabled: false

Metrics/ParameterLists:
  Enabled: false

Metrics/CyclomaticComplexity:
  Max: 12

Metrics/PerceivedComplexity:
  Max: 12

Naming/VariableNumber:
  CheckSymbols: false

Style/HashSyntax:
  EnforcedShorthandSyntax: either

Layout/EmptyLineBetweenDefs:
  Exclude:
    - 'lib/exceptions.rb'

Style/RegexpLiteral:
  EnforcedStyle: mixed

# It has a lot of false-positives with construction like `expect...and not_change { Model.count }`
Lint/AmbiguousBlockAssociation:
  Exclude:
    - 'spec/**/*_spec.rb'

# Excludes service classes which make use of a base Application Service pattern as recommended in 
# https://github.com/rubocop/ruby-style-guide/issues/809#issuecomment-*********
Lint/MissingSuper:
  Exclude:
    - 'app/services/**/*'
