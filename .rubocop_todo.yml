# This configuration was generated by
# `rubocop --auto-gen-config`
# on 2021-12-03 17:14:59 UTC using RuboCop version 1.23.0.
# The point is for the user to remove these configuration records
# one by one as the offenses are removed from the code base.
# Note that changes in the inspected code, or installation of new
# versions of RuboCop, may require this file to be generated again.

# Offense count: 1
# Configuration parameters: CountComments, CountAsOne, ExcludedMethods, IgnoredMethods.
# IgnoredMethods: refine
Metrics/BlockLength:
  Max: 57

# Offense count: 1
# Configuration parameters: IgnoredMethods.
Metrics/CyclomaticComplexity:
  Max: 9

# Offense count: 2
# Configuration parameters: CountComments, CountAsOne, ExcludedMethods, IgnoredMethods.
Metrics/MethodLength:
  Max: 12

# Offense count: 1
# Configuration parameters: IgnoredMethods.
Metrics/PerceivedComplexity:
  Max: 9

# Offense count: 3
# Configuration parameters: AllowedConstants.
Style/Documentation:
  Exclude:
    - 'spec/**/*'
    - 'test/**/*'
    - 'app/helpers/application_helper.rb'
    - 'app/models/application_record.rb'
    - 'config/application.rb'

# Offense count: 1
# Cop supports --auto-correct.
Style/IfUnlessModifier:
  Exclude:
    - 'bin/bundle'

# Offense count: 1
# Cop supports --auto-correct.
# Configuration parameters: AllowHeredoc, AllowURI, URISchemes, IgnoreCopDirectives, IgnoredPatterns.
# URISchemes: http, https
Layout/LineLength:
  Max: 198
