# SAMPLE File from Bitwarden: "Dash .env.development.local"
# This is used to connect to the docker environment locally and overrides the
# .env file.  When developing, this file must be **commented out**.
#
# cp .sample.env.local .env.local
# cp .sample.env.local .env.test.local

# Docker DB Settings
DOCKER_DB_USER=db_user
DOCKER_DB_PW=password
MYSQL_HOST=127.0.0.1

DB_USER_DASH=$DOCKER_DB_USER
DB_PASS_DASH=$DOCKER_DB_PW
AL_DB_USER=$DOCKER_DB_USER
AL_DB_PW=$DOCKER_DB_PW

GDS_SQL_HOST=$MYSQL_HOST
GDS_SQL_DATABASE=gds
GDS_SQL_USERNAME=$DOCKER_DB_USER
GDS_SQL_PASSWORD=$DOCKER_DB_PW

LP_DB_HOST=$MYSQL_HOST
LP_DB_NAME=loan_pro
LP_DB_USER=$DOCKER_DB_USER
LP_DB_PW=$DOCKER_DB_PW

# Dash Postgres currently uses it's own port
RDS_PORT=5433
