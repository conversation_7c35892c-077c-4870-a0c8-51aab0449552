FROM ruby:3.3.7-bullseye

ENV PATH /root/.yarn/bin:$PATH
ARG build_without
ARG RAILS_ENV
ARG GITHUB_TOKEN
ARG BUNDLE_GEMS__CONTRIBSYS__COM

RUN mkdir /rails_terraform_docker
COPY . /rails_terraform_docker
WORKDIR /rails_terraform_docker

# Install Node & JS dependencies
ENV PATH=/usr/local/node/bin:$PATH
RUN NODE_VERSION=$(cat /rails_terraform_docker/.nvmrc) && \
    curl -sL https://github.com/nodenv/node-build/archive/master.tar.gz | tar xz -C /tmp/ && \
    /tmp/node-build-master/bin/node-build "${NODE_VERSION}" /usr/local/node && \
    npm install -g yarn@1 && \
    rm -rf /tmp/node-build-master && \
    yarn install --immutable

RUN sh -c 'echo "deb http://apt.postgresql.org/pub/repos/apt bullseye-pgdg main" > /etc/apt/sources.list.d/pgdg.list'
RUN wget --quiet -O - https://www.postgresql.org/media/keys/ACCC4CF8.asc | apt-key add -

RUN apt -y update && apt install -y binutils curl git gnupg cmake python python-dev postgresql-client-14 \
  default-mysql-client supervisor tar tzdata apt-transport-https apt-utils libjemalloc2
# Detect architecture and symlink the 86_64 file path for M1 laptops
RUN ARCH=$(uname -m) && \
  if [ "$ARCH" = "aarch64" ]; then \
  mkdir -p /usr/lib/x86_64-linux-gnu && \
  ln -sf /usr/lib/aarch64-linux-gnu/libjemalloc.so.2 /usr/lib/x86_64-linux-gnu/libjemalloc.so.2; \
  fi
ENV LD_PRELOAD=/usr/lib/x86_64-linux-gnu/libjemalloc.so.2

COPY Gemfile Gemfile.lock package.json ./
RUN gem install bundler:$(cat Gemfile.lock | tail -1 | tr -d " ") && \
  bundle config --local deployment 'true' && \
  bundle config --local path 'vendor/bundle' && \
  bundle config github.com $GITHUB_TOKEN && \
  bundle config gems.contribsys.com $BUNDLE_GEMS__CONTRIBSYS__COM && \
  (bundle check || bundle install -j$((`nproc`-1)))
RUN yarn install --check-files

RUN chmod a+x bin/rails bin/rake bin/bundle
RUN bundle exec rails assets:precompile

RUN chmod +x docker/entrypoint.sh
ENTRYPOINT [ "docker/entrypoint.sh" ]

# Start the main process.
CMD ["start-web"]

EXPOSE 3001
