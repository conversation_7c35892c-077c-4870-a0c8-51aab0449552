# frozen_string_literal: true

source 'https://rubygems.org'

ruby '3.3.7'

gem 'aasm'
gem 'bootsnap'
gem 'cancancan'
gem 'composite_primary_keys'
gem 'git'
gem 'jbuilder'
gem 'jsbundling-rails'
gem 'jwt'
gem 'lookup_by'
gem 'money-rails'
gem 'mysql2'
gem 'paper_trail'
gem 'pg'
gem 'pghero'
gem 'puma'
gem 'rails'
gem 'rails-pg-extras'
gem 'rails_semantic_logger'
gem 'rswag-api'
gem 'rswag-ui'
gem 'sass-rails'
gem 'sendgrid-actionmailer'
gem 'slack-ruby-client'

gem 'activeadmin_reorderable'
gem 'acts_as_list'
gem 'aws-sdk-rails'
gem 'aws-sdk-s3'
gem 'aws-sdk-secretsmanager', require: false
gem 'browser'
gem 'chartkick'
gem 'datadog', require: 'datadog/auto_instrument'
gem 'dogstatsd-ruby'
gem 'fugit'
gem 'hiredis'
gem 'holidays'
gem 'logstop'
gem 'redis'
gem 'redis-clustering'
gem 'redis-namespace'
gem 'request_store'
gem 'rexml'
gem 'rubyzip', require: 'zip'
gem 'scenic'
gem 'store_model'
gem 'uglifier'

gem 'bcrypt_pbkdf'
gem 'ed25519'
gem 'faraday'
gem 'faraday-multipart'
gem 'net-ssh-gateway'
gem 'sidekiq'
gem 'sidekiq-cron'
gem 'sidekiq-unique-jobs'

source 'https://gems.contribsys.com/' do
  gem 'sidekiq-pro'
end

gem 'activeadmin'
gem 'arctic_admin'
gem 'devise'
gem 'valid_email2'

gem 'activerecord-import'
gem 'carrierwave'
gem 'date_validator'
gem 'flipper'
gem 'flipper-notifications'
gem 'flipper-redis'
gem 'flipper-ui'
gem 'fog-aws'
gem 'wongi-engine'

group :development, :test do
  gem 'amazing_print'
  gem 'brakeman'
  gem 'byebug', platforms: %i[mri mingw x64_mingw]
  gem 'dotenv-rails'
  gem 'factory_bot_rails'
  gem 'faker'
  gem 'parallel_tests'
  gem 'pry'
  gem 'rswag-specs'
  gem 'rubocop', require: false
  gem 'rubocop-rails', require: false
  gem 'rubocop-rspec', require: false
  gem 'ruby-prof', require: false
  gem 'vcr'
end

group :development do
  gem 'annotate'
  gem 'letter_opener_web'
  gem 'listen'
  gem 'rack-mini-profiler'
  gem 'spring'
end

group :test do
  gem 'capybara'
  gem 'database_cleaner-active_record'
  gem 'launchy'
  gem 'rspec_junit_formatter'
  gem 'rspec-rails'
  gem 'rspec-sidekiq'
  gem 'selenium-webdriver'
  gem 'shoulda-matchers'
  gem 'simplecov'
  gem 'site_prism'
  gem 'timecop'
  gem 'webmock'
end

group :development, :test, :staging do
  gem 'foreman'
end
