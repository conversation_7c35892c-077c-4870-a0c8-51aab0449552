GEM
  remote: https://gems.contribsys.com/
  specs:
    sidekiq-pro (7.2.1)
      base64
      sidekiq (>= 7.2.0, < 8)

GEM
  remote: https://rubygems.org/
  specs:
    aasm (5.5.0)
      concurrent-ruby (~> 1.0)
    actioncable (*******)
      actionpack (= *******)
      activesupport (= *******)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
    actionmailbox (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      mail (>= 2.7.1)
      net-imap
      net-pop
      net-smtp
    actionmailer (*******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      activesupport (= *******)
      mail (~> 2.5, >= 2.5.4)
      net-imap
      net-pop
      net-smtp
      rails-dom-testing (~> 2.0)
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      rack (~> 2.0, >= 2.2.4)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.0, >= 1.2.0)
    actiontext (*******)
      actionpack (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.4)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.1, >= 1.2.0)
    activeadmin (3.3.0)
      arbre (~> 1.2, >= 1.2.1)
      csv
      formtastic (>= 3.1)
      formtastic_i18n (>= 0.4)
      inherited_resources (~> 1.7)
      jquery-rails (>= 4.2)
      kaminari (>= 1.2.1)
      railties (>= 6.1)
      ransack (>= 4.0)
    activeadmin_reorderable (0.3.4)
    activejob (*******)
      activesupport (= *******)
      globalid (>= 0.3.6)
    activemodel (*******)
      activesupport (= *******)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
    activerecord-import (2.1.0)
      activerecord (>= 4.2)
    activestorage (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activesupport (= *******)
      marcel (~> 1.0)
      mini_mime (>= 1.1.0)
    activesupport (*******)
      concurrent-ruby (~> 1.0, >= 1.0.2)
      i18n (>= 1.6, < 2)
      minitest (>= 5.1)
      tzinfo (~> 2.0)
    acts_as_list (1.1.0)
      activerecord (>= 4.2)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    amazing_print (1.6.0)
    annotate (3.2.0)
      activerecord (>= 3.2, < 8.0)
      rake (>= 10.4, < 14.0)
    arbre (1.7.0)
      activesupport (>= 3.0.0)
      ruby2_keywords (>= 0.0.2)
    arctic_admin (4.2.8)
      activeadmin (>= 1.1.0, < 4.0)
      font-awesome-sass (~> 6.0)
    ast (2.4.3)
    aws-eventstream (1.4.0)
    aws-partitions (1.1121.0)
    aws-record (2.14.0)
      aws-sdk-dynamodb (~> 1, >= 1.85.0)
    aws-sdk-core (3.226.1)
      aws-eventstream (~> 1, >= 1.3.0)
      aws-partitions (~> 1, >= 1.992.0)
      aws-sigv4 (~> 1.9)
      base64
      jmespath (~> 1, >= 1.6.1)
      logger
    aws-sdk-dynamodb (1.145.0)
      aws-sdk-core (~> 3, >= 3.225.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-kms (1.101.0)
      aws-sdk-core (~> 3, >= 3.216.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-rails (4.1.0)
      actionmailbox (>= 7.0.0)
      aws-record (~> 2)
      aws-sdk-s3 (~> 1, >= 1.123.0)
      aws-sdk-ses (~> 1, >= 1.50.0)
      aws-sdk-sesv2 (~> 1, >= 1.34.0)
      aws-sdk-sns (~> 1, >= 1.61.0)
      aws-sdk-sqs (~> 1, >= 1.56.0)
      aws-sessionstore-dynamodb (~> 2)
      concurrent-ruby (~> 1.3, >= 1.3.1)
      railties (>= 7.0.0)
    aws-sdk-s3 (1.186.1)
      aws-sdk-core (~> 3, >= 3.216.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.5)
    aws-sdk-secretsmanager (1.113.0)
      aws-sdk-core (~> 3, >= 3.216.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-ses (1.85.0)
      aws-sdk-core (~> 3, >= 3.225.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-sesv2 (1.77.0)
      aws-sdk-core (~> 3, >= 3.225.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-sns (1.100.0)
      aws-sdk-core (~> 3, >= 3.225.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-sqs (1.96.0)
      aws-sdk-core (~> 3, >= 3.225.0)
      aws-sigv4 (~> 1.5)
    aws-sessionstore-dynamodb (2.2.0)
      aws-sdk-dynamodb (~> 1, >= 1.85.0)
      rack (>= 2, < 4)
      rack-session (>= 1, < 3)
    aws-sigv4 (1.12.1)
      aws-eventstream (~> 1, >= 1.0.2)
    base64 (0.3.0)
    bcrypt (3.1.20)
    bcrypt_pbkdf (1.1.1)
    bigdecimal (3.1.9)
    bootsnap (1.18.6)
      msgpack (~> 1.2)
    brakeman (6.1.2)
      racc
    browser (6.2.0)
    builder (3.3.0)
    byebug (11.1.3)
    cancancan (3.6.1)
    capybara (3.40.0)
      addressable
      matrix
      mini_mime (>= 0.1.3)
      nokogiri (~> 1.11)
      rack (>= 1.6.0)
      rack-test (>= 0.6.3)
      regexp_parser (>= 1.5, < 3.0)
      xpath (~> 3.2)
    carrierwave (3.0.7)
      activemodel (>= 6.0.0)
      activesupport (>= 6.0.0)
      addressable (~> 2.6)
      image_processing (~> 1.1)
      marcel (~> 1.0.0)
      ssrf_filter (~> 1.0)
    chartkick (5.0.7)
    childprocess (5.1.0)
      logger (~> 1.5)
    coderay (1.1.3)
    composite_primary_keys (14.0.10)
      activerecord (~> 7.0.2)
    concurrent-ruby (1.3.5)
    connection_pool (2.5.3)
    crack (1.0.0)
      bigdecimal
      rexml
    crass (1.0.6)
    cronex (0.15.0)
      tzinfo
      unicode (>= *******)
    csv (3.3.4)
    database_cleaner-active_record (2.2.1)
      activerecord (>= 5.a)
      database_cleaner-core (~> 2.0.0)
    database_cleaner-core (2.0.1)
    datadog (2.17.0)
      datadog-ruby_core_source (~> 3.4, >= 3.4.1)
      libdatadog (~> ********.0)
      libddwaf (~> ********.2)
      logger
      msgpack
    datadog-ruby_core_source (3.4.1)
    date (3.4.1)
    date_validator (0.12.0)
      activemodel (>= 3)
      activesupport (>= 3)
    devise (4.9.4)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0)
      responders
      warden (~> 1.2.3)
    diff-lcs (1.5.1)
    docile (1.4.0)
    dogstatsd-ruby (5.6.5)
    dotenv (3.1.8)
    dotenv-rails (3.1.8)
      dotenv (= 3.1.8)
      railties (>= 6.1)
    ed25519 (1.4.0)
    erubi (1.13.1)
    et-orbi (1.2.11)
      tzinfo
    excon (0.110.0)
    execjs (2.10.0)
    factory_bot (6.4.6)
      activesupport (>= 5.0.0)
    factory_bot_rails (6.4.3)
      factory_bot (~> 6.4)
      railties (>= 5.0.0)
    faker (3.5.1)
      i18n (>= 1.8.11, < 2)
    faraday (2.9.0)
      faraday-net_http (>= 2.0, < 3.2)
    faraday-mashify (1.0.0)
      faraday (~> 2.0)
      hashie
    faraday-multipart (1.1.1)
      multipart-post (~> 2.0)
    faraday-net_http (3.1.1)
      net-http
    ffi (1.17.2)
    flipper (1.3.5)
      concurrent-ruby (< 2)
    flipper-notifications (0.1.7)
      activesupport (>= 7, < 8.1)
      flipper (>= 0.24, < 2.0)
      httparty (~> 0.17)
    flipper-redis (1.3.0)
      flipper (~> 1.3.0)
      redis (>= 3.0, < 6)
    flipper-ui (1.3.4)
      erubi (>= 1.0.0, < 2.0.0)
      flipper (~> 1.3.4)
      rack (>= 1.4, < 4)
      rack-protection (>= 1.5.3, < 5.0.0)
      rack-session (>= 1.0.2, < 3.0.0)
      sanitize (< 8)
    fog-aws (3.22.0)
      fog-core (~> 2.1)
      fog-json (~> 1.1)
      fog-xml (~> 0.1)
    fog-core (2.4.0)
      builder
      excon (~> 0.71)
      formatador (>= 0.2, < 2.0)
      mime-types
    fog-json (1.2.0)
      fog-core
      multi_json (~> 1.10)
    fog-xml (0.1.4)
      fog-core
      nokogiri (>= 1.5.11, < 2.0.0)
    font-awesome-sass (6.7.2)
      sassc (~> 2.0)
    foreman (0.88.1)
    formatador (1.1.0)
    formtastic (5.0.0)
      actionpack (>= 6.0.0)
    formtastic_i18n (0.7.0)
    fugit (1.11.1)
      et-orbi (~> 1, >= 1.2.11)
      raabro (~> 1.4)
    git (1.19.1)
      addressable (~> 2.8)
      rchardet (~> 1.8)
    gli (2.22.2)
      ostruct
    globalid (1.2.1)
      activesupport (>= 6.1)
    has_scope (0.8.2)
      actionpack (>= 5.2)
      activesupport (>= 5.2)
    hashdiff (1.1.0)
    hashie (5.0.0)
    hiredis (0.6.3)
    holidays (8.7.1)
    httparty (0.23.1)
      csv
      mini_mime (>= 1.0.0)
      multi_xml (>= 0.5.2)
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    image_processing (1.12.2)
      mini_magick (>= 4.9.5, < 5)
      ruby-vips (>= 2.0.17, < 3)
    inherited_resources (1.14.0)
      actionpack (>= 6.0)
      has_scope (>= 0.6)
      railties (>= 6.0)
      responders (>= 2)
    jbuilder (2.13.0)
      actionview (>= 5.0.0)
      activesupport (>= 5.0.0)
    jmespath (1.6.2)
    jquery-rails (4.6.0)
      rails-dom-testing (>= 1, < 3)
      railties (>= 4.2.0)
      thor (>= 0.14, < 2.0)
    jsbundling-rails (1.3.0)
      railties (>= 6.0.0)
    json (2.11.3)
    json-schema (5.1.1)
      addressable (~> 2.8)
      bigdecimal (~> 3.1)
    jwt (2.10.1)
      base64
    kaminari (1.2.2)
      activesupport (>= 4.1.0)
      kaminari-actionview (= 1.2.2)
      kaminari-activerecord (= 1.2.2)
      kaminari-core (= 1.2.2)
    kaminari-actionview (1.2.2)
      actionview
      kaminari-core (= 1.2.2)
    kaminari-activerecord (1.2.2)
      activerecord
      kaminari-core (= 1.2.2)
    kaminari-core (1.2.2)
    language_server-protocol (********)
    launchy (3.0.1)
      addressable (~> 2.8)
      childprocess (~> 5.0)
    letter_opener (1.10.0)
      launchy (>= 2.2, < 4)
    letter_opener_web (3.0.0)
      actionmailer (>= 6.1)
      letter_opener (~> 1.9)
      railties (>= 6.1)
      rexml
    libdatadog (********.0)
    libddwaf (********.2)
      ffi (~> 1.0)
    lint_roller (1.1.0)
    listen (3.9.0)
      rb-fsevent (~> 0.10, >= 0.10.3)
      rb-inotify (~> 0.9, >= 0.9.10)
    logger (1.7.0)
    logstop (0.4.1)
      logger
    loofah (2.24.1)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    lookup_by (0.12.0)
      activerecord (>= 6.0.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    matrix (0.4.2)
    method_source (1.1.0)
    mime-types (3.5.2)
      mime-types-data (~> 3.2015)
    mime-types-data (3.2024.0305)
    mini_magick (4.12.0)
    mini_mime (1.1.5)
    mini_portile2 (2.8.9)
    minitest (5.25.5)
    monetize (1.13.0)
      money (~> 6.12)
    money (6.19.0)
      i18n (>= 0.6.4, <= 2)
    money-rails (1.15.0)
      activesupport (>= 3.0)
      monetize (~> 1.9)
      money (~> 6.13)
      railties (>= 3.0)
    msgpack (1.8.0)
    multi_json (1.15.0)
    multi_xml (0.7.2)
      bigdecimal (~> 3.1)
    multipart-post (2.4.1)
    mysql2 (0.5.6)
    net-http (0.6.0)
      uri
    net-imap (0.5.9)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-smtp (0.5.1)
      net-protocol
    net-ssh (7.2.3)
    net-ssh-gateway (2.0.0)
      net-ssh (>= 4.0.0)
    nio4r (2.7.4)
    nokogiri (1.18.8)
      mini_portile2 (~> 2.8.2)
      racc (~> 1.4)
    orm_adapter (0.5.0)
    ostruct (0.6.1)
    paper_trail (15.1.0)
      activerecord (>= 6.1)
      request_store (~> 1.4)
    parallel (1.27.0)
    parallel_tests (4.7.1)
      parallel
    parser (*******)
      ast (~> 2.4.1)
      racc
    pg (1.5.6)
    pghero (3.6.1)
      activerecord (>= 6.1)
    prism (1.4.0)
    pry (0.15.2)
      coderay (~> 1.1)
      method_source (~> 1.0)
    public_suffix (6.0.2)
    puma (6.6.0)
      nio4r (~> 2.0)
    raabro (1.4.0)
    racc (1.8.1)
    rack (2.2.17)
    rack-mini-profiler (3.3.1)
      rack (>= 1.2.0)
    rack-protection (3.2.0)
      base64 (>= 0.1.0)
      rack (~> 2.2, >= 2.2.4)
    rack-session (1.0.2)
      rack (< 3)
    rack-test (2.2.0)
      rack (>= 1.3)
    rails (*******)
      actioncable (= *******)
      actionmailbox (= *******)
      actionmailer (= *******)
      actionpack (= *******)
      actiontext (= *******)
      actionview (= *******)
      activejob (= *******)
      activemodel (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      bundler (>= 1.15.0)
      railties (= *******)
    rails-dom-testing (2.3.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    rails-pg-extras (5.6.10)
      rails
      ruby-pg-extras (= 5.6.10)
    rails_semantic_logger (4.17.0)
      rack
      railties (>= 5.1)
      semantic_logger (~> 4.16)
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      method_source
      rake (>= 12.2)
      thor (~> 1.0)
      zeitwerk (~> 2.5)
    rainbow (3.1.1)
    rake (13.3.0)
    ransack (4.3.0)
      activerecord (>= 6.1.5)
      activesupport (>= 6.1.5)
      i18n
    rb-fsevent (0.11.2)
    rb-inotify (0.10.1)
      ffi (~> 1.0)
    rchardet (1.8.0)
    redis (5.4.0)
      redis-client (>= 0.22.0)
    redis-client (0.24.0)
      connection_pool
    redis-cluster-client (0.13.4)
      redis-client (~> 0.24)
    redis-clustering (5.4.0)
      redis (= 5.4.0)
      redis-cluster-client (>= 0.10.0)
    redis-namespace (1.11.0)
      redis (>= 4)
    regexp_parser (2.10.0)
    request_store (1.7.0)
      rack (>= 1.4)
    responders (3.1.1)
      actionpack (>= 5.2)
      railties (>= 5.2)
    rexml (3.4.1)
    rspec-core (3.13.3)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.0)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-mocks (3.13.0)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-rails (6.1.2)
      actionpack (>= 6.1)
      activesupport (>= 6.1)
      railties (>= 6.1)
      rspec-core (~> 3.13)
      rspec-expectations (~> 3.13)
      rspec-mocks (~> 3.13)
      rspec-support (~> 3.13)
    rspec-sidekiq (4.2.0)
      rspec-core (~> 3.0)
      rspec-expectations (~> 3.0)
      rspec-mocks (~> 3.0)
      sidekiq (>= 5, < 8)
    rspec-support (3.13.2)
    rspec_junit_formatter (0.6.0)
      rspec-core (>= 2, < 4, != 2.12.0)
    rswag-api (2.16.0)
      activesupport (>= 5.2, < 8.1)
      railties (>= 5.2, < 8.1)
    rswag-specs (2.16.0)
      activesupport (>= 5.2, < 8.1)
      json-schema (>= 2.2, < 6.0)
      railties (>= 5.2, < 8.1)
      rspec-core (>= 2.14)
    rswag-ui (2.16.0)
      actionpack (>= 5.2, < 8.1)
      railties (>= 5.2, < 8.1)
    rubocop (1.75.3)
      json (~> 2.3)
      language_server-protocol (~> 3.17.0.2)
      lint_roller (~> 1.1.0)
      parallel (~> 1.10)
      parser (>= 3.3.0.2)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.9.3, < 3.0)
      rubocop-ast (>= 1.44.0, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 4.0)
    rubocop-ast (1.44.1)
      parser (>= *******)
      prism (~> 1.4)
    rubocop-rails (2.31.0)
      activesupport (>= 4.2.0)
      lint_roller (~> 1.1)
      rack (>= 1.1)
      rubocop (>= 1.75.0, < 2.0)
      rubocop-ast (>= 1.38.0, < 2.0)
    rubocop-rspec (3.6.0)
      lint_roller (~> 1.1)
      rubocop (~> 1.72, >= 1.72.1)
    ruby-pg-extras (5.6.10)
      pg
      terminal-table
    ruby-prof (1.7.0)
    ruby-progressbar (1.13.0)
    ruby-vips (2.2.1)
      ffi (~> 1.12)
    ruby2_keywords (0.0.5)
    ruby_http_client (3.5.5)
    rubyzip (2.4.1)
    sanitize (7.0.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.16.8)
    sass-rails (6.0.0)
      sassc-rails (~> 2.1, >= 2.1.1)
    sassc (2.4.0)
      ffi (~> 1.9)
    sassc-rails (2.1.2)
      railties (>= 4.0.0)
      sassc (>= 2.0)
      sprockets (> 3.0)
      sprockets-rails
      tilt
    scenic (1.8.0)
      activerecord (>= 4.0.0)
      railties (>= 4.0.0)
    selenium-webdriver (4.31.0)
      base64 (~> 0.2)
      logger (~> 1.4)
      rexml (~> 3.2, >= 3.2.5)
      rubyzip (>= 1.2.2, < 3.0)
      websocket (~> 1.0)
    semantic_logger (4.16.1)
      concurrent-ruby (~> 1.0)
    sendgrid-actionmailer (3.2.0)
      mail (~> 2.7)
      sendgrid-ruby (~> 6.4)
    sendgrid-ruby (6.7.0)
      ruby_http_client (~> 3.4)
    shoulda-matchers (6.5.0)
      activesupport (>= 5.2.0)
    sidekiq (7.3.9)
      base64
      connection_pool (>= 2.3.0)
      logger
      rack (>= 2.2.4)
      redis-client (>= 0.22.2)
    sidekiq-cron (2.2.0)
      cronex (>= 0.13.0)
      fugit (~> 1.8, >= 1.11.1)
      globalid (>= 1.0.1)
      sidekiq (>= 6.5.0)
    sidekiq-unique-jobs (8.0.11)
      concurrent-ruby (~> 1.0, >= 1.0.5)
      sidekiq (>= 7.0.0, < 9.0.0)
      thor (>= 1.0, < 3.0)
    simplecov (0.22.0)
      docile (~> 1.1)
      simplecov-html (~> 0.11)
      simplecov_json_formatter (~> 0.1)
    simplecov-html (0.12.3)
    simplecov_json_formatter (0.1.4)
    site_prism (5.1)
      addressable (~> 2.8, >= 2.8.4)
      capybara (~> 3.34)
      site_prism-all_there (> 3, < 5)
    site_prism-all_there (3.0.6)
    slack-ruby-client (2.6.0)
      faraday (>= 2.0)
      faraday-mashify
      faraday-multipart
      gli
      hashie
      logger
    spring (4.2.1)
    sprockets (4.2.1)
      concurrent-ruby (~> 1.0)
      rack (>= 2.2.4, < 4)
    sprockets-rails (3.4.2)
      actionpack (>= 5.2)
      activesupport (>= 5.2)
      sprockets (>= 3.0.0)
    ssrf_filter (1.1.2)
    store_model (4.2.1)
      activerecord (>= 7.0)
    terminal-table (4.0.0)
      unicode-display_width (>= 1.1.1, < 4)
    thor (1.3.2)
    tilt (2.3.0)
    timecop (0.9.8)
    timeout (0.4.3)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    uglifier (4.2.1)
      execjs (>= 0.3.0, < 3)
    unicode (*******)
    unicode-display_width (3.1.4)
      unicode-emoji (~> 4.0, >= 4.0.4)
    unicode-emoji (4.0.4)
    uri (1.0.3)
    valid_email2 (7.0.12)
      activemodel (>= 6.0)
      mail (~> 2.5)
    vcr (6.2.0)
    warden (1.2.9)
      rack (>= 2.0.9)
    webmock (3.23.0)
      addressable (>= 2.8.0)
      crack (>= 0.3.2)
      hashdiff (>= 0.4.0, < 2.0.0)
    websocket (1.2.11)
    websocket-driver (0.7.7)
      base64
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    wongi-engine (0.4.4)
    xpath (3.2.0)
      nokogiri (~> 1.8)
    zeitwerk (2.7.3)

PLATFORMS
  ruby

DEPENDENCIES
  aasm
  activeadmin
  activeadmin_reorderable
  activerecord-import
  acts_as_list
  amazing_print
  annotate
  arctic_admin
  aws-sdk-rails
  aws-sdk-s3
  aws-sdk-secretsmanager
  bcrypt_pbkdf
  bootsnap
  brakeman
  browser
  byebug
  cancancan
  capybara
  carrierwave
  chartkick
  composite_primary_keys
  database_cleaner-active_record
  datadog
  date_validator
  devise
  dogstatsd-ruby
  dotenv-rails
  ed25519
  factory_bot_rails
  faker
  faraday
  faraday-multipart
  flipper
  flipper-notifications
  flipper-redis
  flipper-ui
  fog-aws
  foreman
  fugit
  git
  hiredis
  holidays
  jbuilder
  jsbundling-rails
  jwt
  launchy
  letter_opener_web
  listen
  logstop
  lookup_by
  money-rails
  mysql2
  net-ssh-gateway
  paper_trail
  parallel_tests
  pg
  pghero
  pry
  puma
  rack-mini-profiler
  rails
  rails-pg-extras
  rails_semantic_logger
  redis
  redis-clustering
  redis-namespace
  request_store
  rexml
  rspec-rails
  rspec-sidekiq
  rspec_junit_formatter
  rswag-api
  rswag-specs
  rswag-ui
  rubocop
  rubocop-rails
  rubocop-rspec
  ruby-prof
  rubyzip
  sass-rails
  scenic
  selenium-webdriver
  sendgrid-actionmailer
  shoulda-matchers
  sidekiq
  sidekiq-cron
  sidekiq-pro!
  sidekiq-unique-jobs
  simplecov
  site_prism
  slack-ruby-client
  spring
  store_model
  timecop
  uglifier
  valid_email2
  vcr
  webmock
  wongi-engine

RUBY VERSION
   ruby 3.3.7p123

BUNDLED WITH
   2.3.12
