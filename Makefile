SERVICE ?= web

bash: # Bash prompt on running container
	docker compose exec $(SERVICE) bash

console: # Rails console for running web container
	docker compose exec $(SERVICE) bundle exec rails c

lint: # Rubocop linting for all changed files
	docker compose exec $(SERVICE) bash -c "(git diff --name-only --diff-filter=d; git ls-files --others --exclude-standard) | grep '\.rb$ ' | xargs bundle exec rubocop"

lint.fix: # Rubocop linting for all changed files
	docker compose exec $(SERVICE) bash -c "(git diff --name-only --diff-filter=d; git ls-files --others --exclude-standard) | grep '\.rb$ ' | xargs bundle exec rubocop -a"

lint.fix.all: # Rubocop linting for all files
	docker compose exec $(SERVICE) bundle exec rubocop -a .

logs: # Tail the service container's logs
	docker compose exec $(SERVICE) tail -f log/development.log


## BUNDLE COMMANDS ##

bundle.install: # Install gems
	docker compose exec $(SERVICE) bundle install


## RAILS COMMANDS ##

rails.migrate: # Run migrations
	docker compose exec $(SERVICE) bundle exec rails db:migrate


## TEST COMMANDS ##

test.all: # Run the full rspec test suite
	docker compose exec $(SERVICE) bundle exec rspec

test.changed: # Run the rspec test for all changed spec files
	docker compose exec $(SERVICE) bash -c "git diff --name-only --diff-filter=d HEAD | grep -E '^spec/.+\.rb$$' | xargs bundle exec rspec"

test.coverage: # Open the code coverage webpage
	open coverage/index.html


## DOCKER COMMANDS ##

docker.build: # Build containers
	docker compose build

docker.attach: # Attach to a running container
	docker attach $(SERVICE)

docker.ps: # Show running processes
	docker compose ps

docker.restart.web: # Restart the web container
	docker compose restart web

docker.restart.sidekiq: # Restart the sidekiq container
	docker compose restart sidekiq

docker.stop: # Stop running containers
	docker compose stop

docker.start: # Start stopped containers
	docker compose start

docker.up: # Start containers
	docker compose up -d

docker.up.web: # Start containers
	docker compose up web -d

docker.up.dependencies: # Start postgres redis mysql
	docker compose up -d redis mysql postgres
docker.up.sys:	docker.up.dependencies

docker.build.up: # Build and start containers
	docker compose up --build -d

docker.down: # Bring down the service
	docker compose down

docker.prune: # Prune and free up system file space
	docker system prune --all --force

docker.prune.all: # Prune All (including volumes) and free up system file space
	docker system prune --all --force --volumes


## SETUP COMMANDS ##

setup.reset: # Completely resets your dash setup
	docker compose down
	docker system prune --all --force --volumes
	docker compose up -d redis mysql postgres
	docker compose up -d runner
	docker/bin/run bundle exec rails db:prepare
	docker compose up -d web


## HELP COMMANDS ##

# Show help topics
help:
	@grep -E '^[a-zA-Z0-9_.-]+:.*?# .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?# "}; {printf "\033[36m%-30s\033[0m %s\n", $$1, $$2}'
