<a href="https://codeclimate.com/repos/61aa4c0c1b407c014d003984/maintainability"><img src="https://api.codeclimate.com/v1/badges/b959196f1d2e336b39ad/maintainability" /></a>
<a href="https://codeclimate.com/repos/61aa4c0c1b407c014d003984/test_coverage"><img src="https://api.codeclimate.com/v1/badges/b959196f1d2e336b39ad/test_coverage" /></a>
[GH Actions CI](https://github.com/Above-Lending/dash/actions/workflows/code-quality.yml)

# README

Back office admin tool for Above Lending

# Developer Environment Setup

## Prerequistes

1. Clone the repo:
    ```
    <NAME_EMAIL>:Above-Lending/dash.git
    cd dash
    ```
1. Setup your dotenv files:
    * Local development and testing environment variables are stored in the `.env` environment file. Staging and sandbox environment variables are stored in the `.env.staging.local`, and `.env.sandbox.local` files. All files are maintained in Bitwarden. Copy fields from the following Bitwarden entries and store them in `.env`, `.env.staging.local`, and `.env.sandbox.local`.
        * `.env` - [Dash Development Environment Variables: .env](https://vault.bitwarden.com/#/vault?itemId=b82feb24-69cb-4653-8bce-af110130e32a)
        * `.env.staging.local` - [Dash Development Environment Variables: .env.staging.local](https://vault.bitwarden.com/#/vault?itemId=c2e915db-76be-4f19-b346-af11013126e3)
        * `.env.sandbox.local` - [Dash Development Environment Variables: .env.sandbox.local](https://vault.bitwarden.com/#/vault?itemId=ed2c11e4-9bd0-4fb6-a38c-af810189af86)
    * Review the contents of the `.env` file and adjust the commented out sections to be appropriate for the setup of your local environment (e.g. depending upon where you are running your dependent applications, etc).
        * Locally you do *NOT* want `CLUSTERED_REDIS_URI` or `CLUSTERED_REDIS_PREFIX` set.  If these are set, remove them.
        * If you are running your dependent applications directly on your machine, you may not need to uncomment any of the sections.
1. Setup your Github access token:
    * Internal gems are pulled from Github's package management service, you will need to generate and configure a personal access token to permit you to load these gems within your local environment.
    * Setup your [Github.com private access token](https://docs.github.com/en/authentication/keeping-your-account-and-data-secure/creating-a-personal-access-token).
        * Github's "Fine-grained tokens" are in beta but these do not support Github Package Registry access. __DO NOT USE THIS TYPE OF TOKEN__.
        * Assign a relatively long expiration but do not use the "No Expiration" option.
        * Grant this access token the `write:packages` permission.
    * Configure this access token in the appropriate manner for your local environment:
        * To run the application directly on your machine, configure Bundler with your access token with the following command:
            ```
            bundle config github.com "[YOUR_USERNAME]:[YOUR_ACCESS_TOKEN]"
            ```
        * To run the application in Docker, add a `GITHUB_TOKEN` variable to your `.env` file to have this token used by Docker:
            ```
            GITHUB_TOKEN=[YOUR_ACCESS_TOKEN]
            ```
1. You will need to set `BUNDLE_GEMS__CONTRIBSYS__COM` as well. You can get the value for it from BitWarden. Then, add it to your shell config as well:
`export BUNDLE_GEMS__CONTRIBSYS__COM=value-from-bitwarden`

### Local App Setup [PREFERRED]

To setup your local machine to run dash, use the following steps.
1. Install dependent applications:
    * If you use the [asdf](https://asdf-vm.com/) package manager, run the following command:
        ```
        asdf plugin-add ruby; asdf plugin-add postgres; asdf install
        ```
    * If you run dependent applications directly on your machine:
        if you did not install it via asdf or plan to run it via Docker:
        * Install postgres directly,
            ```
            brew install postgresql@15
            brew services start postgresql@15
            ```
        * Install MySQL:
            ```
            brew install mysql
            brew services start mysql
            ```
        * Install Redis:
            ```
            brew install redis
            brew services start redis
            ```
1. Launch your local application:
    * To run the application directly on your machine:
        ```
        bin/setup
        bundle exec foreman start
        echo "Dash running at http://localhost:3000/"
        ```
      * Note: If you get this error when running `bin/setup`:
        ```
        An error occurred while installing mysql2 (0.5.5), and Bundler cannot continue.
        Make sure that `gem install mysql2 -v '0.5.5' --source 'https://rubygems.org/'` succeeds before bundling.
        ```
        you may need to run the following:
        ```
        brew install openssl (or "brew reinstall openssl@3" if openssl is already installed)
        gem install mysql2 -v '0.5.6' -- --with-opt-dir=$(brew --prefix openssl) --with-ldflags=-L/opt/homebrew/opt/zstd/lib
        bundle config --global build.mysql2 "--with-opt-dir=$(brew --prefix openssl) --with-ldflags=-L/opt/homebrew/opt/zstd/lib"
        ```

### Docker Setup

To setup your local machine to run dash, use the following steps.

1. Install dependencies:
    * Using the link go and download [docker desktop](https://www.docker.com/products/docker-desktop/)
1. Inside of the dash directory. Run the following command:
    ```
    $ make setup.reset
    ```
1. Profit! Your server should be up at: http://localhost:3001/

## External Database Schemas
Dash currently has connections to three external databases. AboveLending, LoanPro and GDS. The GDS and LoanPro databases are maintained via migrations (for better or worse). Fields that are observed in developer documentation or Snowflake (direct database viewer) for these systems will need to be added in a migration before they can be utilized in Dash. The connection to the Above Lending database is handled using a schema dump and will need to be updated whenever attributes added through the Application Management System are needed here in Dash. The process for grabbing and loading the schema is described below.

### Updating the Above Lending Schema
Previously this was done by connecting to the staging environment but due to access restrictions we've revised our methodology.

1. Have a copy of the latest Application Management System on your local machine
1. Add the following line to `config/application.rb` in AMS (AMS uses schemas, Dash uses structures so we have to update `application.rb` to output structure files)
```
module ApplicationManagementSystem
  class Application < Rails::Application
    config.load_defaults 7.0

    # Add this line
    config.active_record.schema_format = :sql
  end
end
```
1. Dump the schema as a `structure.sql` file
```
bin/rails db:schema:dump
```
1. Copy the resulting file to `db/abovelending_structure.sql` in Dash
```
mv db/structure.sql ../dash/db/abovelending_structure.sql
```
1. Load the schema in dash for development and test (it's possible that you only need to load the schemas, but drop/create has 100% success rate)
```
bin/rails db:drop:abovelending
bin/rails db:create:abovelending
bin/rails db:schema:load:abovelending

bin/rails db:drop:abovelending RAILS_ENV=test
bin/rails db:create:abovelending RAILS_ENV=test
bin/rails db:schema:load:abovelending RAILS_ENV=test
```
1. Force update the model annotations
```
annotate
```
1. Revert/reset the changes made to AMS

## Running the Service in Development

Dash uses the [jsbundling-rails](https://github.com/rails/jsbundling-rails/) gem to compile javascript assets.  In order to compile assets in real-time for the development environment, we delegate to webpack via the `build` script in [package.json](./package.json).  Orchestrating the execution of our local rails server and asset compilation is handled via Foreman, and can be started via the [dev start script](./bin/dev):

```sh
bin/dev
```


## Optional

### SendGrid
To test SendGrid mailers from the development environment, set the following in your `.env` file:

```
DEVELOPMENT_USE_SENDGRID=1
SENDGRID_API_KEY=YOURKEY
```

### Test ACH Transactions
To test ACH transactions in local while connected to the LoanPro staging databases, use the below test CheckCommerce account setup script:
```
rails check_commerce:create_test_accounts
```

### AWS Setup & Integration

The following operations rely on resources within AWS:

* __Loan Allocation:__ Loan allocation files uploaded via the Dash admin are recorded within an S3 bucket.
* __Automated Reports:__ A backup of each automated report (e.g. the Unallocated Loans Report, etc) is recorded within an S3 bucket for historical review.
* __CRB Onboarding:__ A backup of each payload delivered to the Arix API as part of the CRB onboarding process is recorded within an S3 bucket for debugging and offline review.

In our deployment environments (e.g. production, staging, etc), the credentials for this integration are automatically supplied through a [Kubernetes service account](https://kubernetes.io/docs/tasks/configure-pod-container/configure-service-account/).

In local development environments, you will need to generate an AWS STS token and make this available to the application through the standard set of AWS environment variables (i.e. `AWS_ACCESS_KEY_ID`, `AWS_SECRET_ACCESS_KEY`, `AWS_SESSION_TOKEN`, and `AWS_REGION`). This can be done manually according to the instructions in the [Using the AWS CLI Confluence page](https://abovelending.atlassian.net/wiki/spaces/PROD/pages/**********/Using+the+AWS+CLI) and the resulting values copied into the `.env` file. Alternatively there are other tools available to automate various portions of this process.

You will also want to define the following variables in your `.env` file to support these featuse:

```
AWS_DEFAULT_REGION=us-east-1
AWS_LA_BUCKET=above-lending-dash-dev
AWS_LOAN_REPORTS_BUCKET=unallocated-loan-reports
ARIX_PAYLOADS_BACKUP_S3_BUCKET_NAME=TBD
```

### Local AWS Resource

Optionally, you may prefer to interact with a local s3 object store.  We use [minio](https://min.io) in our [docker-compose.yml](./docker-compose.yml) (the `minio_fake_s3` service) to provide a local AWS S3 compatible object storage.

To tell your local environment to use this service, ensure that it has been brought up via `docker compose`, and then initialize your rails server with the `USE_MINIO_FAKE_S3` flag set to 'true.'

> USE_MINIO_FAKE_S3=true bin/rails s

You'll still need to create any buckets that your server is expecting to interact with. Fortunately Minio provides an admin interface via [http://localhost:9001](http://localhost:9001). The username and password can be found in the environment variables given to the `minio_fake_s3` service in docker-compose.yml.

## Testing Staging and Production Locally
**Warning:** Here be dragons, be careful to setup and reset before doing local
development.

### Setting up Staging and Production Locally

`bin/testdata` has been added to manage the data environment used with rails
commands. The syntax is `bin/testdata <environment> <command>`.

1. Connect to the proper vpn for your test environment
2. `bin/testdata <environment> <command>`

Examples:
```
bin/testdata staging rails c
bin/testdata staging rails s
```

Staging environment variables are managed in Bitwarden and are
stored in the following directories:

* `.env` Development & Test
* `.env.staging.local` Staging


### Sidekiq

To run jobs using Sidekiq, you will need to start up the Redis server first:
```
redis-server
```
Then start up Sidekiq with your respective environment, example:
```
bundle exec bin/testdata staging sidekiq start
```

### Paper Trail
Dash uses the PaperTrail gem to track changes to models, most notably AchTransaction. Change records can be found on the AchTransaction show page. For more information, the PaperTrail documentation is available [here](https://github.com/paper-trail-gem/paper_trail)


# Environment Variables
Dash **does not** maintain its own environment variables in our various [deployed environments](#deployment). Instead, Above Lending's DevOps team maintains variables in a separate infrastructure and links them in the build and deployment of the application. Bitwarden will have a copy of these environment variables.

When adding a new variable, modifying the value of an existing variable, or removing a variable, we must:

* Update **each** of the Bitwarden notes to include/remove/update the value of the variable in question:
    - `.env` [Dash Development Environment Variables: .env](https://vault.bitwarden.com/#/vault?itemId=b82feb24-69cb-4653-8bce-af110130e32a)
    - `.env.staging.local` [Dash Development Environment Variables: .env.staging.local](https://vault.bitwarden.com/#/vault?itemId=c2e915db-76be-4f19-b346-af11013126e3)
    - [Dash - Sandbox Environment Variables](https://vault.bitwarden.com/#/vault?itemId=48b3223a-e1f3-4639-806a-aeed0157aa63)
    - [Dash - Staging Environment Variables](https://vault.bitwarden.com/#/vault?itemId=a22241e1-00b7-4c16-8dc3-aeed0156e437)

# Dev Workflow
In an effort to keep things simple, we use a basic trunk-based work flow and always work off the `main` branch. Below is a high level summary of the steps to get code in to production

1. Copy the git checkout command off the Jira ticket - under `Development -> Create Branch`. This ensures all branches follow the same naming convention.
```
git checkout main
git pull origin main
git checkout -b DASH-1-this-is-a-sample-branch-name
```
2. Add new code changes
```
git add <files>
git commit -m 'what this commit contains; bucket similar changes together'
git push origin DASH-1-this-is-a-sample-branch-name
```

3. Open GitHub
4. Create a PR for your new branch
5. Fill out the PR template
6. Submit as either a draft PR (if dev is still in progress but you want to share) or a PR ready for review
7. Once the PR is all green (all checks pass), move the Jira ticket to 'READY FOR CR'
8. Once code review is complete, move the Jira ticket to 'IN QA'

🧪 **Automated tests are required!** We keep a high standard for code coverage. Devs submit new code with unit tests (line coverage) and feature/system tests if possible. QA Engineers will help build out the feature/system tests as well.

# Developing in Docker

A few friendlier commands are at your disposal when using docker:

- `docker/bin/start` - run the development server containerized as a daemon.
- `docker/bin/dev` - the same as above but not detached (daemonized) and showing logs on stdout.
- `docker/bin/stop` - stop the docker environment if it is daemonized.
- `docker/bin/test` - run the tests on the `runner` container.
- `docker/bin/console` - starting a console in the `runner` container.
- `docker/bin/seed` - seed the development database.
- `docker/bin/run` - run a task on the `runner` container. (this only needs to be run after the first start).

## Starting the environment

You have two options:

1. Start the docker environment daemonized. Run `docker/bin/start`.
2. Start the docker environment undaemonized in the current process. Run `docker/bin/dev`.

## Runner

The docker setup has a container for running tasks, called `runner`. This container is for one-off/one-shot scripts. You can execute any script by using the `docker/bin/run` command (e.g. `docker/bin/run bin/rails routes`)

## Rebuilding the docker environment

To rebuild the docker environment:

If you are running daemonized:

1. Run `docker/bin/stop`
2. Run `docker/bin/start`

Otherwise:

1. Press Ctrl-c in the running docker process.
2. Run `docker/bin/dev`

## Tests

The tests are run on the `runner` container. You can run the tests with the `docker/bin/test` command.
All arguments are piped to `bin/rspec` in the container, so you can pass all arguments to this command (e.g. `docker/bin/test --tag type:feature`).
The feature specs are run on the remote `selenium` container. You can connect with vnc to this container by connecting to `vnc://localhost:5900`. The admin panel for selenium hub is available on `http://localhost:4444`.

Set the environment variable `HEADLESS` if you want to run in a headless browser.

## Console

You can run a console with the `docker/bin/console` command.
All arguments are piped to `bin/rails c` in the container, so you can pass all arguments to this command (e.g. `docker/bin/console -e test`).

## Resetting the database

The database dirs are mounted under `tmp/db`. There is one folder for `mysql` and one for `postgres`.
To delete the database data and start over, delete the `tmp/db` directory or run the `docker/bin/reset-db` command.
Restart the docker environment to initialize the databases.

## Gotcha's

It is not possible to use the `DATABASE_URL` env var when developing in docker.
We are relying on that the database will be switched by the `RAILS_ENV` environment variable (for example when running tests).
This does not work when setting `DATABASE_URL`.

# Monitoring & Alerting

We use Datadog to manage the monitors and associated alerts that help maintain the health of this service.

# Release Management

## Deployment

This application is be hosted at the following domain for each environment:

* Sandbox: https://dash-sandbox.abovelending.com
* Staging: https://dash-stage.abovelending.com
* Production https://dash.abovelending.com

### Sandbox and Staging

**NOTE** _please use these steps to deploy `main` branch to Staging environment after merging code changes._

Deployment for Sandbox and Staging environments is done manually via Github Actions. In order to trigger a deployment, navigate to the repository in Github, to the "Actions" pane using Github's top-navigation, and then select "Manual Deployment Workflow." Or try [following this link](https://github.com/Above-Lending/dash/actions/workflows/manual-deployment.yaml).

From there:

1. Near the top-right, open the "Run Workflow" menu.
1. This opens a drop-down allowing for the selection of a branch or tag, as well as the "Destination Environment."
1. Select the branch or tag, e.g., `main`.
1. Then select the "Destination Environment," e.g., `sandbox` or `stage`.  **Do not** select `prod` as part of this process; if you need to deploy to production, instead follow the steps [outlined below](#production).
1. Click "Run Workflow." This triggers the deployment workflow, which may be monitored by refreshing the page and then selecting the now-running "Manual Deployment Workflow" from the middle table.

### Production

Deployment of the Production environment is done manually via Github Actions by a small group of Product Owners. Developers should not be pushing to production as outlined by the SDLC. It uses the same "[Manual Deployment Workflow](https://github.com/Above-Lending/dash/actions/workflows/manual-deployment.yaml)" as Sandbox and Staging deployments, but with a few extra steps, as follows:

1. From your local environment run the following command to generate and push a tag to Github
```
 bin/rails tag
```
1. Follow the link from the command or visit the "[Tags](https://github.com/Above-Lending/dash/tags)" section of Github for the repository.
1. Click "Create release from tag" near the top right.
1. Near the release description, click "Generate release notes." This will automatically fill-in the description of the released with a list of commits indicating the changes contained in the release **compared to the latest release tag**.
    1. **NOTE**:  The "generate release notes" button does not take into consideration situations where there are _additional_ `pre-release`s that are waiting to be deployed.
    1.  When there is a prior `pre-release` release, the newly-drafted release will "hide" changes that have yet to be deployed (because they already appear in the previous un-deployed release), but will include them in upon deployment and may inadvertantly result in the deployment of unexpected functionality.
    1. When multiple `pre-release` releases are present, we must ensure that they are **deployed in order**, one at a time, so that the changes to production are always only those that we expect, and are isolated from one release to another.
    1. We may minimize the chance of stacking multiple `pre-release` releases by swiftly executing a candidate release when we're able to do so.
1. Next, **mark** "Set as a pre-release" and ensure that "Set as the latest release" is **unmarked**, then hit "Publish release." Announce the release in our team's slack channel with a link back to the github release that was just created.
1. Announce the intention to deploy in our team's slack channel first, with a link to the release.
1. At this point you should coordinate with the Product Owners to ask them to release the proposed changes. The steps below should not be run by engineers but are present for informational purposes.
1. As in Staging and Sandbox, we make use of the "[Manual Deployment Workflow](https://github.com/Above-Lending/dash/actions/workflows/manual-deployment.yaml)":
    1. Near the top-right, open the "Run Workflow" menu.
    1. This opens a drop-down allowing for the selection of a branch or tag, as well as the "Destination Environment."
    1. Select the tag that represents the release, e.g., `2145.11.30.09.45`
    1. Then select the `prod` "Destination Environment."
    1. Click "Run Workflow." This triggers the deployment workflow, which may be monitored by refreshing the page and then selecting the now-running "Manual Deployment Workflow" from the middle table.
1. After deployment is completed, return to the release in github. Update it, **unmarking** "Set as a pre-release", and **marking** "Set as the latest release."
1. Finally, back in Jira, move the cards in the "Accepted" Column that correspond to the release over to "Released."
