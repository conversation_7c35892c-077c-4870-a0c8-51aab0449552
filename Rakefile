# frozen_string_literal: true

# Add your own tasks in files placed in lib/tasks ending in .rake,
# for example lib/tasks/capistrano.rake, and they will automatically be available to Rake.

require_relative 'config/application'

Rails.application.load_tasks

desc 'Generates a tag based on the lastest version of main and pushes it'
task :tag do
  g = Git.open('.')
  puts 'Switching branch to main...'
  g.checkout('main')
  puts 'Pulling latest from main...'
  g.pull('origin', 'main')
  puts 'Creating tag...'
  now = Time.now.getutc
  release_version = now.strftime('%Y.%m.%d.%H.%M')
  message = "Tag generated for release by #{g.config('user.email')}"
  tag = g.add_tag(release_version, { a: true, m: message }) # Fully annotated tag
  g.push('origin', "refs/tags/#{tag.name}")
  puts "Tag #{release_version} created successfully"
  puts "Create a release for #{release_version} on Github by visiting:"
  puts "  https://github.com/Above-Lending/dash/releases/tag/#{release_version}"
end
