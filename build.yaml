version: 0.2
env:
  git-credential-helper: yes
phases:
  install: # Install AWS cli, kubectl (needed for He<PERSON>) and Helm
    commands:
      -  ./deployment/scripts/config-env.sh
  build: # Build and push Docker image and deploy to EKS
    commands:
      - ./deployment/scripts/build.sh
  post_build:
    commands:
      # Example tag: 2021.***********
      - git tag -a  $(date +%Y).$(date +%m).$(date +%d).$(date +%H).$(date +%M) -m 'auto-generated post build tag'
      - git push origin --tags
