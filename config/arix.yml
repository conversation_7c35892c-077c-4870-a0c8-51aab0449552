default: &default
  api_base_url: <%= ENV.fetch('ARIX_API_BASE_URL', nil) %>
  auth_base_url: <%= ENV.fetch('ARIX_AUTH_BASE_URL', nil) %>
  client_id: <%= ENV.fetch('ARIX_CLIENT_ID', nil) %>
  client_secret: <%= ENV.fetch('ARIX_CLIENT_SECRET', nil) %>

development:
  <<: *default

test:
  <<: *default
  api_base_url: https://arixapisandbox.crbnj.net
  auth_base_url: https://oauthtest.crbnj.net
  client_id: testClient
  client_secret: 'test-secret'

sandbox:
  <<: *default

staging:
  <<: *default

production:
  <<: *default

