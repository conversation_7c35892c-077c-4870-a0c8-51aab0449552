version: "3.3"
services:
  postgres:
    image: postgres:14.7
    volumes:
      - ./db:/postgres
      - ./docker/db_setup_postgres.sh:/docker-entrypoint-initdb.d/db_setup_postgres.sh
      - ./tmp/db/dash_postgres:/var/lib/postgresql/data
    environment:
      POSTGRES_MULTIPLE_DATABASES: dash,abovelending
      POSTGRES_USER: db_user
      POSTGRES_PASSWORD: password
    ports:
      - 5433:5432
    healthcheck:
      test: ["CMD", "psql", "--user=db_user", "-c\\q"]
      interval: 5s
      timeout: 10s
      retries: 20

  redis:
    image: redis:alpine
    ports:
      - 6379:6379

  mysql:
    image: mysql/mysql-server:8.0.32
    volumes:
      - ./db:/mysql
      - ./docker/db_setup_mysql.sh:/docker-entrypoint-initdb.d/db_setup_mysql.sh
      - ./tmp/db/mysql:/var/lib/mysql
    environment:
      MYSQL_MULTIPLE_DATABASES: loan_pro,gds
      MYSQL_ROOT_PASSWORD: password
      MYSQL_USER: db_user
      MYSQL_PASSWORD: password
    ports:
      - 3306:3306
    healthcheck:
      test: ["CMD", "mysql", "--user=db_user", "--password=password", "-e exit"]
      interval: 5s
      timeout: 10s
      retries: 20

  web: &web
    image: dash:0.0.1
    build:
      context: .
      dockerfile: Dockerfile
      args:
        GITHUB_TOKEN: "${GITHUB_TOKEN}"
        BUNDLE_GEMS__CONTRIBSYS__COM: "${BUNDLE_GEMS__CONTRIBSYS__COM}"
    links:
      - postgres
      - redis
      - mysql
      - minio_fake_s3
    depends_on:
      postgres:
        condition: service_healthy
      mysql:
        condition: service_healthy
    volumes:
      - .:/rails_terraform_docker:cached
      - bundle-cache:/rails_terraform_docker/vendor/bundle
      - ./docker/entrypoint.sh:/rails_terraform_docker/docker/entrypoint.sh
    command: ["start-dev"]
    environment:
      DB_NAME_DASH: dash
      RDS_HOST: postgres
      RDS_PORT: 5432
      DB_USER_DASH: db_user
      DB_PASS_DASH: password
      AL_DB_NAME: abovelending
      AL_DB_USER: db_user
      AL_DB_PW: password
      LP_DB_NAME: loan_pro
      LP_DB_HOST: mysql
      LP_DB_USER: db_user
      LP_DB_PW: password
      GDS_SQL_DATABASE: gds
      GDS_SQL_HOST: mysql
      GDS_SQL_USERNAME: db_user
      GDS_SQL_PASSWORD: password
      ARIX_PAYLOADS_BACKUP_S3_BUCKET_NAME: sandbox-arix-payload
      DEDICATED_REDIS_URI: redis://redis:6379/0
    ports:
      - 3001:3001
    env_file: .env

  sidekiq:
    <<: *web
    command: ["start-sidekiq"]
    ports: []

  runner:
    <<: *web
    stdin_open: true
    tty: true
    command: ["runner"]
    ports: []
    environment:
      DB_NAME_DASH: dash
      RDS_HOST: postgres
      RDS_PORT: 5432
      DB_USER_DASH: db_user
      DB_PASS_DASH: password
      AL_DB_NAME: abovelending
      AL_DB_USER: db_user
      AL_DB_PW: password
      LP_DB_NAME: loan_pro
      LP_DB_HOST: mysql
      LP_DB_USER: db_user
      LP_DB_PW: password
      GDS_SQL_DATABASE: gds
      GDS_SQL_HOST: mysql
      GDS_SQL_USERNAME: db_user
      GDS_SQL_PASSWORD: password
      ARIX_PAYLOADS_BACKUP_S3_BUCKET_NAME: sandbox-arix-payload
      SELENIUM_HOST: selenium

  selenium:
    image: selenium/standalone-chromium:4.31.0 # should match selenium-webdriver version in Gemfile
    ports:
      - 4444:4444
      - 5900:5900
    volumes:
      - .:/rails_terraform_docker # for uploads/downloads and file fixtures need to be available to the tests

  minio_fake_s3:
    image: minio/minio
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - ./docker/minio:/data
    environment:
      MINIO_ROOT_USER: aboveUser
      MINIO_ROOT_PASSWORD: password
    command: server --console-address ":9001" /data

volumes:
  bundle-cache:
