# This YAML file serves as a living declaration of Environment Variables for this Application.
# It provides a common interface between Application Developers and DevOps.
# https://www.notion.so/Environment-Variables-bcbb3a0c9e1d40b1bc3f19e2d4337c8c
# https://abovelending.atlassian.net/wiki/spaces/PROD/pages/1642692650/Environment+Variables

global:
  - name: DASH
    path: "/global/DASH_CONFIG"
    source: AWS Secrets Manager
    env_vars:
    - name: ACTIVE_RECORD_ENCRYPTION_PRIMARY_KEY
      description: he key or lists of keys used to derive root data-encryption keys
      type: string
    - name: ACTIVE_RECORD_ENCRYPTION_DETERMINISTIC_KEY
      description: The key or list of keys used for deterministic encryption
      type: string
    - name: ACTIVE_RECORD_ENCRYPTION_KEY_DERIVATION_SALT
      description: The salt used when deriving keys
      type: string
    - name: AMS_PUBLIC_KEY_PATH
      description: Path to the public key file that matches the key used by AMS to generate JWTs
      type: string
    - name: SIDEKIQ_DEADSET_MAX
      description: Thereshold for dead jobs in Sidekiq to trigger an alert
      type: integer
    - name: FLIPPER_SLACK_HOOK
      description: Slack hook for Flipper notifications
      type: string
environmental:
  - name: DASH
    path: "/$ENV/DASH_CONFIG"
    source: AWS Secrets Manager
    env_vars:
    - name: AL_DB_PW
      description: Above Lending database password
      type: string
    - name: AL_DB_USER
      description: Above Lending database user
      type: string
    - name: CLUSTERED_REDIS_URI
      description: |
        The Redis connection URI for a clustered Redis instance, which is intended to support this service's
        application caching needs. This should always be configured alongside a CLUSTERED_REDIS_PREFIX environment
        variable that defines the prefix within this cluster allocated specifically to this application.
      type: string
    - name: CLUSTERED_REDIS_PREFIX
      description: The namespace within the clustered Redis instance that is allocated to this application.
      type: string
    - name: COMMUNICATIONS_SERVICE_BASE_URL
      description: Base URL for communicating with the Communications Service
      type: string
    - name: DATABASE_URL
      description: Dash database connection string
      type: string
    - name: DB_NAME
      description: Dash database name
      type: string
    - name: DB_PASS_DASH
      description: Dash database password
      type: string
    - name: DB_PORT
      description: Dash database port
      type: string
    - name: DB_USER_DASH
      description: Dash database user
      type: string
    - name: DASH_ACCESS_TOKEN
      description: Dash access token
      type: string
    - name: AMS_API_BASE_URL
      description: Base URL used for all requests delivered to the AMS API
      type: string
    - name: AMS_CLIENT_ID
      description: Client ID used to authenticate with the AMS
      type: string
    - name: AMS_CLIENT_SECRET
      description: Client secret used to authenticate with the AMS
      type: string
    - name: DEDICATED_REDIS_URI
      description: |
        The Redis connection URI for a dedicated Redis instance, which is intended to serve as the data store for this
        service's Sidekiq server and workers.
      type: string
    - name: ARIX_PAYLOADS_BACKUP_S3_BUCKET_NAME
      description: Arix payload backup bucket name
      type: string
    - name: CRB_ATTACHMENT_UPLOADER_S3_BUCKET_NAME
      description: Arix payload backup bucket name for AMS
      type: string
    - name: AWS_LA_BUCKET
      description: Loan allocation bucket name
      type: string
    - name: OFFER_EXPIRATION_DAYS
      description: Offer expiration length
      type: string
    - name: RAILS_ENV
      description: Rails Environment
      type: string
    - name: SECRET_KEY_BASE
      description: secret input for the applications key_generator method
      type: string
    - name: RAILS_SERVE_STATIC_FILES
      description: Rails flag to serve static files
      type: string
    - name: SENDGRID_API_KEY
      description: Sendgrid API key
      type: string
    - name: AGENT_REPRESENTMENT_UPDATED_SCRIPT
      description: Date that agents started reading new script to allow for representments on agent initiated recurring payments
      type: string
    - name: LOAN_PRO_WEBHOOK_TOKEN
      description: Static bearer token configured to authenticate Loan Pro webhook requests
      type: string
  - name: GDS
    path: "/$ENV/GDS_CONFIG"
    source: AWS Secrets Manager
    env_vars:
    - name: GDS_AUTH_BASE_URL
      description: A base URL used for authentication requests to generate the access token for communicating with the GDS API
      type: string
    - name: GDS_BASE_URL
      description: A base URL used for all HTTP requests sent to the GDS API
      type: string
    - name: GDS_CLIENT_ID
      description: The client ID value to be used in our authentication requests for GDS
      type: string
    - name: GDS_SECRET
      description: The client secret value to be used in our authentication requests for GDS
      type: string
  - name: Arix
    path: '/$ENV/ARIX_CONFIG'
    source: AWS Secrets Manager
    env_vars:
    - name: ARIX_API_BASE_URL
      description: The base URL used for all non-authentication requests related to the Arix platform.
      type: string
    - name: ARIX_AUTH_BASE_URL
      description: The base URL used for all authentication requests related to the Arix platform.
      type: string
    - name: ARIX_CLIENT_ID
      description: The Client ID value used to authenticate with the Arix API's OAuth endpoint and generate an access token.
      type: string
    - name: ARIX_CLIENT_SECRET
      description: The Client Secret value used to authenticate with the Arix API's OAuth endpoint and generate an access token.
      type: string
  - name: PLAID_CONFIG
    path: '/$ENV/PLAID_CONFIG'
    source: AWS Secrets Manager
    env_vars:
      - name: PLAID_BASE_URL
        description: Base URL used for all requests made to the Plaid API
        type: string
      - name: PLAID_CLIENT_ID
        description: Client ID value used to authenticate all requests sent to the Plaid API
        type: string
      - name: PLAID_SECRET
        description: Secret value used to authenticate all requests sent to the Plaid API
        type: string
vendor:
  - name: AURORA
    path: '/$ENV/AURORA_CONFIG'
    source: AWS Secrets Manager
    env_vars:
      - name: AURORA_HOST
        description: Aurora database host
        type: string
      - name: AURORA_PORT
        description: AURORA database port
        type: string
