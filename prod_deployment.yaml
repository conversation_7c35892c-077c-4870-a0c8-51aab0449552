version: 0.2
env:
  git-credential-helper: yes
phases:
  install: # Install AWS cli, kubectl (needed for <PERSON><PERSON>) and Helm
    commands:
      -  ./deployment/scripts/config-env.sh
  build:
    commands:
      - ./deployment/scripts/prod_deploy.sh
#  post_build:
#    commands:
#      - TODO: add a post back to GitHub to update the release with a "Deployed at: " note.?
#      - TODO: add a post back to GitHub to send "release deployed" to code climate?
