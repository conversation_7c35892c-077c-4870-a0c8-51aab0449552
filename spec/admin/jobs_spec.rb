# frozen_string_literal: true

require 'rails_helper'

RSpec.feature 'Jobs features: ' do
  let!(:admin) { create_and_login_admin }
  let(:controller) { Admin::JobsController }

  describe 'index page' do
    before do
      allow_any_instance_of(controller).to receive(:all_jobs).and_return(%w[<PERSON><PERSON><PERSON>ob SamplePeriodic].to_set)
    end

    scenario 'non-admins cannot access' do
      admin.update!(roles: [])
      visit(admin_jobs_path)
      expect(page).to have_current_path('/admin')
    end

    scenario 'displays tabs and tables' do
      visit(admin_jobs_path)

      expect(page).to have_css('#page_title', text: 'Jobs')

      within('#main_content') do
        expect(page).to have_link('SampleJob', href: github_tree_url('app/jobs/sample_job.rb'))
        expect(page).to have_link('Schedule', href: admin_jobs_schedule_path(klass: 'SampleJob'))

        expect(page).to have_link('SamplePeriodic', href: github_tree_url('app/jobs/sample_periodic.rb'))
        expect(page).to have_link('Schedule', href: admin_jobs_schedule_path(klass: 'SamplePeriodic'))
      end
    end
  end

  describe 'schedule page' do
    context 'any other class' do
      before do
        allow(Sidekiq).to receive(:redis).and_return([])
        visit(admin_jobs_schedule_path(klass: 'AboveLending::Loan'))
      end

      scenario 'redirects to index page' do
        expect(page).to have_current_path('/admin/jobs')
        expect(page).to have_content('Job not found!')
      end
    end

    context 'SendPaymentScheduledEmailJob' do
      before { visit(admin_jobs_schedule_path(klass: 'SendPaymentScheduledEmailJob')) }

      scenario 'displays form' do
        expect(page).to have_content('Schedule SendPaymentScheduledEmailJob')

        within('fieldset.inputs') do
          expect(page).to have_css('label', text: 'Loan pro loan*')
          expect(page).to have_css('label', text: 'Payment profile*')
          expect(page).to have_css('label', text: 'Payment amount*')
          expect(page).to have_css('label', text: 'Payment date*')
          expect(page).to have_css('label', text: 'Wait')
          expect(page).to have_content('How many minutes to wait before running the job')

          expect(page).to have_css('input[name="job[loan_pro_loan_id]"]')
          expect(page).to have_css('input[name="job[payment_profile_id]"]')
          expect(page).to have_css('input[name="job[payment_amount]"]')
          expect(page).to have_css('input[name="job[payment_date]"]')
          expect(page).to have_css('input[name="job[wait]"]')
        end
      end
    end

    context 'FakeJob with customized fields' do
      let(:klass) { double('FakeJob', name: 'FakeJob') }

      before do
        allow(ActiveSupport::Inflector).to receive(:constantize).and_call_original
        allow(ActiveSupport::Inflector).to receive(:constantize).with('FakeJob').and_return(klass)

        klass.define_singleton_method(:form_fields) do
          [{ name: :url, as: :url, label: 'URL', hint: 'URL to ping' }]
        end

        allow_any_instance_of(controller).to receive(:all_jobs).and_return(%w[FakeJob].to_set)
        visit(admin_jobs_schedule_path(klass: 'FakeJob'))
      end

      scenario 'displays form with fully customized fields' do
        expect(page).to have_content('Schedule FakeJob')

        within('fieldset.inputs') do
          expect(page).to have_css('label', text: 'URL')
          expect(page).to have_css('label', text: 'Wait')

          expect(page).to have_content('URL to ping')

          expect(page).to have_css('input[type=url][name="job[url]"]')
          expect(page).to have_css('input[name="job[wait]"]')
        end
      end
    end
  end

  describe 'perform action' do
    context 'PingJob' do
      let(:job) { PingJob }

      before do
        allow_any_instance_of(controller).to receive(:all_jobs).and_return([job.name].to_set)
        visit(admin_jobs_schedule_path(klass: job.name))
      end

      scenario 'simple schedule' do
        expect(PingJob).to receive(:perform_async).and_return('123')

        click_on 'Submit Job'

        expect(page).to have_current_path('/admin/jobs')
        expect(page).to have_content('PingJob successfully scheduled (JID #123)')
      end

      scenario 'schedule with wait' do
        expect(PingJob).to receive(:set).with(wait: 20.minutes).and_return(PingJob)
        expect(PingJob).to receive(:perform_async).and_return('456')

        fill_in 'Wait', with: '20'
        click_on 'Submit Job'

        expect(page).to have_current_path('/admin/jobs')
        expect(page).to have_content('PingJob successfully scheduled (JID #456)')
      end
    end

    context 'SendReturnedPaymentEmailJob' do
      let(:job) { SendReturnedPaymentEmailJob }
      let(:uuid) { SecureRandom.uuid }

      before do
        allow_any_instance_of(controller).to receive(:all_jobs).and_return([job.name].to_set)
        visit(admin_jobs_schedule_path(klass: job.name))
      end

      scenario 'schedule the job with provided parameters' do
        expect(job).to receive(:perform_async).with(uuid).and_return('123')

        fill_in 'Ach transaction*', with: uuid
        click_on 'Submit Job'

        expect(page).to have_current_path('/admin/jobs')
        expect(page).to have_content("#{job.name} successfully scheduled (JID #123)")
      end
    end

    context 'CrbOnboarding::ValidationJob' do
      let(:job) { CrbOnboarding::ValidationJob }
      let(:loan) { create(:above_lending_loan) }

      before do
        allow_any_instance_of(controller).to receive(:all_jobs).and_return([job.name].to_set)
        visit(admin_jobs_schedule_path(klass: job.name))
      end

      scenario 'cannot proceed with missing loan' do
        fill_in 'Loan', with: '1010'
        click_on 'Submit Job'

        expect(page).to have_content("Invalid arguments: loan_id: Couldn't find AboveLending::Loan")
      end
    end

    context 'QueueRepresentmentBankBalances' do
      let(:job) { QueueRepresentmentBankBalancesJob }

      before do
        allow_any_instance_of(controller).to receive(:all_jobs).and_return([job.name].to_set)
        visit(admin_jobs_schedule_path(klass: job.name))
      end

      scenario 'schedule the job' do
        expect(job).to receive(:perform_async).with(no_args).and_return('123')

        visit(admin_jobs_schedule_path(klass: job.name))

        click_on 'Submit Job'

        expect(page).to have_current_path('/admin/jobs')
        expect(page).to have_content("#{job.name} successfully scheduled (JID #123)")
      end
    end
  end
end
