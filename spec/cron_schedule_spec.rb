# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Cron Schedule' do
  let(:schedule_loader) { Sidekiq::Cron::ScheduleLoader.new }
  let(:all_jobs) { Sidekiq::Cron::Job.all }

  it 'has a schedule file' do
    expect(schedule_loader).to have_schedule_file
  end

  it 'does not return any errors' do
    expect(schedule_loader.load_schedule).to be_empty
  end

  it 'adds the expected set of jobs' do
    schedule_loader.load_schedule
    expect(all_jobs.size).to eq 14
    expect(all_jobs.map(&:klass).sort).to eq([
                                               'CacheAllConcentrationValues',
                                               'CreateAllocationsDryRunJob',
                                               'DailyDeadsetMonitorJob',
                                               'DashReports::SendSummaryReport',
                                               'IncrementalStatusFetch',
                                               'PeriodicDeadsetMonitorJob',
                                               'PingJob',
                                               'QueueAchFundingTransactions',
                                               'QueueAchTransactions',
                                               'SendFailedLoanProPayments',
                                               'SendUnallocatedLoanReport',
                                               'SyncGdsData',
                                               'TestGdsConnection',
                                               'TestLoanProConnection'
                                             ])
  end

  it 'has a valid class for each added job' do
    schedule_loader.load_schedule
    job_classes = all_jobs.map { |job| job.klass.constantize }
    expect(job_classes).to all(respond_to(:perform_async))
  end
end
