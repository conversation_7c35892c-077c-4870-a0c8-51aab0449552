# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ExceptionLogger do
  let(:error) { StandardError.new('boom!') }
  let(:backtrace) { ['source.rb:3', 'source.rb:2', 'source.rb:1'] }

  before do
    allow_any_instance_of(StandardError).to receive(:backtrace).and_return(backtrace)
  end

  it 'logs with the Rails logger' do
    expect(Rails.logger).to receive(:error).with('Error raised', exception: error)
    ExceptionLogger.error(error)
  end

  it 'sets the error on the datadog span' do
    expect(DatadogSpanTagger).to receive(:error).with(error)
    ExceptionLogger.error(error)
  end
end
