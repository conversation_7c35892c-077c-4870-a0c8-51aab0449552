# frozen_string_literal: true

require 'database_cleaner-active_record'
require 'simplecov'
require 'spec_helper'

SimpleCov.start 'rails' do
  minimum_coverage 100.0
  add_filter '/spec'

  # NOTE: This may be removed if these models start getting their own helper methods, validators, etc
  add_filter 'app/models/above_lending'

  # rails-generated superclass files
  add_filter '/app/jobs/application_job.rb'
  add_filter '/app/mailers/application_mailer.rb'
  add_filter '/app/models/application_record.rb'
  add_filter '/app/models/above_lending_record.rb'

  # Ignore ActiveAdmin default files
  add_filter '/app/admin/dashboard.rb'
  add_filter '/app/admin/admin_users.rb'

  # ActiveAdmin Concerns are not automatically covered
  add_filter '/app/admin/concerns'

  # Ignore check commerce env vars
  add_filter 'app/services/check_commerce_service/configuration.rb'

  add_filter '/lib'
end

SimpleCov.coverage_dir("./coverage_results/#{ENV['TEST_ENV_NUMBER']}") if ENV['PARALLEL_TEST_GROUPS']

ENV['RAILS_ENV'] ||= 'test'

require File.expand_path('../config/environment', __dir__)
# Prevent database truncation if the environment is production
abort('The Rails environment is running in production mode!') if Rails.env.production?
require 'rspec/rails'
require 'paper_trail/frameworks/rspec'
require 'vcr'

require_relative 'support/dash_scenario'
Dir[Rails.root.join('spec/support/**/*.rb')].each { |file| require file }

VCR.configure do |config|
  config.ignore_localhost = true
  config.ignore_host 'runner', 'selenium'
  config.cassette_library_dir = 'spec/cassettes'
  config.configure_rspec_metadata!
  config.allow_http_connections_when_no_cassette = false
  config.hook_into :webmock

  # Use `VCR_RECORD=new_episodes` if you'd like to record new interactions in an existing cassette
  config.default_cassette_options = {
    record: ENV.fetch('VCR_RECORD', 'once').to_sym, drop_unused_requests: true
  }

  config.filter_sensitive_data('<Authorization-REDACTED>') do |interaction|
    interaction.request.headers['Authorization'].try(:first)
  end
end

begin
  ActiveRecord::Migration.maintain_test_schema!
rescue ActiveRecord::PendingMigrationError => e
  puts e.to_s.strip
  exit 1
end

# Need to handle each database transactions for DatabaseCleaner
DASH_DATABASES = {
  primary: ApplicationRecord,
  abovelending: AboveLendingRecord,
  loan_pro: LoanProRecord,
  gds: GdsRecord
}.freeze

RSpec.configure do |config|
  FactoryBot.definition_file_paths = %w[abovelending custom_factories_directory]
  FactoryBot.find_definitions
  config.include FactoryBot::Syntax::Methods
  config.include Helpers::Login, type: :feature

  config.fixture_path = "#{::Rails.root}/spec/fixtures"
  config.infer_spec_type_from_file_location!
  config.filter_rails_from_backtrace!

  ActionDispatch::TestResponse.include ResponseHelper

  config.include Shoulda::Matchers::ActiveModel, type: :action
  config.include Shoulda::Matchers::ActiveModel, type: :validator

  config.use_transactional_fixtures = false

  config.before(:suite) do
    DASH_DATABASES.each_value { |model| DatabaseCleaner[:active_record, db: model].clean_with(:truncation) }
  end

  config.before(:each) do |example|
    clean_strategy = example.metadata[:type] == :feature ? :truncation : :transaction
    DASH_DATABASES.each_value do |model|
      DatabaseCleaner[:active_record, db: model].strategy = clean_strategy
      DatabaseCleaner[:active_record, db: model].start
    end
  end

  config.after(:each) do
    DASH_DATABASES.each_value do |model|
      DatabaseCleaner[:active_record, db: model].clean
    end
  end
end

RSpec::Matchers.define_negated_matcher :not_change, :change

Shoulda::Matchers.configure do |config|
  config.integrate do |with|
    with.test_framework :rspec
    with.library :rails
  end
end
