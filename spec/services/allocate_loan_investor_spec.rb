# frozen_string_literal: true

require 'rails_helper'

RSpec.describe AllocateLoanInvestor do
  let(:above_lending_investor) { create(:above_lending_investor) }
  let(:above_lending_loan) { create(:above_lending_loan, :unallocated, :loanpro, originating_party: 'CRB') }
  let!(:loan) { create(:loan, loan_number: above_lending_loan.unified_id) }

  describe '#call' do
    subject { described_class.call(loan: above_lending_loan, investor: above_lending_investor) }

    context 'when above_lending_loan is not present' do
      let(:above_lending_loan) { nil }
      let!(:loan) { nil }

      it 'should raise error for Unified ID' do
        expect { subject }.to raise_error(/Unified ID not found/)
      end
    end

    context 'when loanpro loan is not present' do
      let(:above_lending_loan) { create(:above_lending_loan, :unallocated) }
      it 'should raise error for Loanpro Loan Entity' do
        expect { subject }.to raise_error(/Active Loanpro Loan Entity not found/)
      end
    end

    context 'when loanpro loan entity is marked deleted' do
      let!(:above_lending_loan) { create(:above_lending_loan, :unallocated) }
      let!(:loan_entity) { create(:loan_entity, deleted: true, display_id: above_lending_loan.unified_id) }

      it 'should raise error for Loanpro Loan Entity' do
        expect { subject }.to raise_error(/Active Loanpro Loan Entity not found/)
      end
    end

    context 'when more than one loanpro loan entities are present, with duplicate created timestamps' do
      let!(:above_lending_loan) { create(:above_lending_loan, :unallocated, :loanpro) }
      let!(:loan_pro_loan_entity2) do
        create(:loan_entity, above_lending_loan:, display_id: above_lending_loan.unified_id,
                             created: above_lending_loan.loan_pro_loan_entity.created, lastUpdated: above_lending_loan.loan_pro_loan_entity.lastUpdated + 1.second)
      end

      before { above_lending_loan.reload }

      it 'should update investor for the last created loan entity' do
        expect(LoanProService::LoanManagementSystem::UpdateLoanSubportfolio).to receive(:call).with(subportfolio_id: above_lending_investor.subportfolio_id,
                                                                                                    loan_id: loan_pro_loan_entity2.id).and_return(true)
        expect(LoanProService::LoanManagementSystem::UpdateLoanSettings).to receive(:call).with(hash_including(loan_id: loan_pro_loan_entity2.id))
        subject
      end
    end

    context 'when above_lending_investor is not present' do
      let(:above_lending_investor) { nil }
      it 'should raise error for investor' do
        expect { subject }.to raise_error(/Investor not found/)
      end
    end

    context 'when loanpro update fails' do
      before do
        allow(LoanProService::LoanManagementSystem::UpdateLoanSubportfolio).to receive(:call).and_raise(LoanProService::Exceptions::LoanManagementSystemError, 'Boom!')
      end

      it 'should raise the error without suppressing it' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, 'Boom!')
      end

      it 'should revert loan investor assignment in AL DB' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, 'Boom!')
        expect(above_lending_loan.reload.investor).to eq(nil)
      end
    end

    context 'when loanpro update succeeds' do
      before do
        expect(LoanProService::LoanManagementSystem::UpdateLoanSubportfolio).to receive(:call)
          .with(loan_id: above_lending_loan.loan_pro_loan_entity.id, subportfolio_id: above_lending_investor.subportfolio_id).and_return({})
        expect(LoanProService::LoanManagementSystem::UpdateLoanSettings).to receive(:call).with(hash_including(loan_id: above_lending_loan.loan_pro_loan_entity.id))
      end

      it 'should not raise error' do
        expect { subject }.to_not raise_error
      end

      it 'should assign investor to loan' do
        subject
        expect(above_lending_loan.reload.investor).to eq(above_lending_investor)
      end

      context 'when loan record exists' do
        it 'should update the investor on the loan' do
          subject
          expect(loan.reload.investor.detail_info).to have_attributes(name: above_lending_investor.name, title: above_lending_investor.title)
        end

        it 'should create an investor if none exist' do
          expect do
            subject
          end.to change(Investor, :count).by(1)

          expect(loan.reload.investor).to be_a(Investor)
          expect(loan.reload.investor.detail_info).to have_attributes(name: above_lending_investor.name, title: above_lending_investor.title)
        end

        it 'should use an existing investor if present' do
          investor = create(:investor, detail_info: { name: above_lending_investor.name, title: above_lending_investor.title })

          expect do
            subject
          end.not_to change(Investor, :count)

          expect(loan.reload.investor).to eq(investor)
        end
      end

      context 'when loan record doesnt exist' do
        let!(:loan) { nil }

        # TODO: this is something we should revisit later
        # in the future we should probably fail here.
        it 'should rescue from ActiveRecord::RecordNotFound and log to info' do
          expect(Rails.logger).to receive(:info).with(/#{above_lending_loan.unified_id}/)

          expect do
            subject
          end.not_to change(Investor, :count)
        end
      end

      context 'when multiple loan records exist' do
        before do
          create(:loan, loan_number: above_lending_loan.unified_id)
        end

        it 'should rescue, log and re-raise ActiveRecord::SoleRecordExceeded' do
          expect(Rails.logger).to receive(:error).with(/#{above_lending_loan.unified_id}/)
          expect_any_instance_of(described_class).to receive(:log_exception)

          expect { subject }.to raise_error(ActiveRecord::SoleRecordExceeded)
        end
      end
    end

    context 'purchase date updates' do
      let(:contract_date) { Date.parse('2022-10-06') } # date with a long weekend coming up
      before do
        allow(LoanProService::LoanManagementSystem::UpdateLoanSubportfolio).to receive(:call)
          .with(loan_id: above_lending_loan.loan_pro_loan_entity.id, subportfolio_id: above_lending_investor.subportfolio_id).and_return({})
      end

      context 'when originating party is AL' do
        let(:above_lending_loan) { create(:above_lending_loan, :unallocated, :loanpro, originating_party: 'DIRECT_LICENSES', originator_title: 'Above Funding Trust', contract_date: contract_date) }

        context 'when investor is AFT' do
          let(:above_lending_investor) { create(:above_lending_investor, name: 'above_funding_trust') }

          it 'should not update loanpro loan settings' do
            expect(LoanProService::LoanManagementSystem::UpdateLoanSettings).to_not receive(:call)
            subject
          end
        end

        context 'when investor is not AFT' do
          let(:purchase_date) { Date.parse('2022-10-12') }

          it 'should update purchase date to +3 business days' do
            expect(LoanProService::LoanManagementSystem::UpdateLoanSettings).to receive(:call).with(
              loan_id: above_lending_loan.loan_pro_loan_entity.id,
              loan_settings_id: above_lending_loan.loan_pro_loan_entity.loan_settings_entity.id,
              custom_fields: { 103 => purchase_date }
            )
            subject
          end
        end
      end

      context 'when originating party is CRB' do
        let(:above_lending_loan) { create(:above_lending_loan, :unallocated, :loanpro, contract_date: contract_date) }
        let(:purchase_date) { Date.parse('2022-10-14') }

        it 'should update purchase date to +5 business days' do
          expect(LoanProService::LoanManagementSystem::UpdateLoanSettings).to receive(:call).with(
            loan_id: above_lending_loan.loan_pro_loan_entity.id,
            loan_settings_id: above_lending_loan.loan_pro_loan_entity.loan_settings_entity.id,
            custom_fields: { 103 => purchase_date }
          )
          subject
        end
      end
    end
  end
end
