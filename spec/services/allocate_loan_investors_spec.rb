# frozen_string_literal: true

require 'rails_helper'

RSpec.describe AllocateLoanInvestors do
  include NotifierHelper

  let!(:investor) { create(:above_lending_investor) }
  let!(:loan) { create(:above_lending_loan, :unallocated, :loanpro) }

  let(:loan_investors) { { loan.unified_id => investor.name.upcase } }
  subject { described_class.new(loan_investors) }

  describe '#initialize' do
    it { is_expected.to have_attributes(loan_investors: loan_investors) }
  end

  describe '#call' do
    subject { described_class.new(loan_investors).call }
    before do
      allow(ActiveSupport::Notifications).to receive(:instrument).and_call_original
      allow(AllocateLoanInvestor).to receive(:call).with(loan:, investor:).and_return(nil)
    end

    it 'returns true' do
      expect(subject).to be_truthy
    end

    it 'creates NewLoanAllocation records' do
      subject
      expect(NewLoanAllocation.all.count).to eq(1)
    end

    it 'notifies run summary with updated IDs' do
      subject

      expect_to_notify('allocate_loan_investors', success: true, meta: { status: 'completed', updated_loan_ids: [loan.unified_id], failed_loan_ids: {} })
    end

    it 'calls AllocateLoanInvestor with loan and investor objects' do
      expect(AllocateLoanInvestor).to receive(:call).with(loan:, investor:)
      subject
    end

    context 'when an exception occurs' do
      before do
        allow(AllocateLoanInvestor).to receive(:call).with(loan:, investor:).and_raise('Boom!')
      end

      it 'returns true' do
        expect(subject).to be_truthy
      end

      it 'notifies run summary with failed ID details' do
        subject
        expect_to_notify('allocate_loan_investors', success: true, meta: { status: 'completed', updated_loan_ids: [], failed_loan_ids: { loan.unified_id => 'Boom!' } })
      end

      it 'notifies individual error detail' do
        subject
        expect_to_notify('allocate_loan_investor', success: false, fail_reason: 'Boom!', meta: { loan_id: loan.unified_id })
      end
    end
  end

  describe '#failed_error_messages' do
    before do
      allow(AllocateLoanInvestor).to receive(:call).with(loan:, investor:).and_raise('Boom!')
    end

    it 'provides an array of failed loan error messages' do
      subject.call
      expect(subject.failed_error_messages).to eq(["Unified ID #{loan.unified_id}: Boom!"])
    end
  end
end
