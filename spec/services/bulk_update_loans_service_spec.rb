# frozen_string_literal: true

require 'rails_helper'
Sidekiq::Testing.fake!

RSpec.describe BulkUpdateLoansService do
  include NotifierHelper

  let!(:loan1) { create(:above_lending_loan, :unallocated, :loanpro) }
  let!(:loan2) { create(:above_lending_loan, :unallocated, :loanpro) }
  let!(:loan3) { create(:above_lending_loan, :unallocated, :loanpro, exclude_from_allocation: true) }
  let!(:loan4) { create(:above_lending_loan, :unallocated, :loanpro, exclude_from_allocation: true) }
  let!(:beneficial_owner) { create(:sub_portfolio_entity, :beneficial_owner) }
  let(:loan_transfer_date) { Faker::Date.between(from: 1.year.ago, to: Date.today) }
  let(:financed_date) { Faker::Date.between(from: 1.year.ago, to: Date.today) }
  let(:purchase_date) { Faker::Date.between(from: 1.year.ago, to: Date.today) }
  let(:cspv_repurchase_date) { Faker::Date.between(from: 1.year.ago, to: Date.today) }

  let!(:bulk_update_run) do
    create(:bulk_update_run,
           unified_ids: { loan1.unified_id => true, loan2.unified_id => true },
           financed_date: financed_date,
           loan_transfer_date: loan_transfer_date,
           exclude_from_allocation: true,
           beneficial_owner_id: beneficial_owner.id,
           purchase_date: purchase_date,
           cspv_repurchase_date: cspv_repurchase_date)
  end

  let!(:bulk_run_details) do
    {
      financed_date:,
      loan_purchase_date: purchase_date,
      cspv_repurchase_date:,
      loan_transfer_date:
    }
  end

  describe '#initialize' do
    it 'has attributes' do
      expect(described_class.new(bulk_update_run.id)).to have_attributes(bulk_update_run: bulk_update_run)
    end

    it 'finds the bulk update run' do
      expect(BulkUpdateRun).to receive(:find).with(bulk_update_run.id).and_return(bulk_update_run)
      described_class.new(bulk_update_run.id)
    end

    it 'should rescue from ActiveRecord::RecordNotFound' do
      expect do
        described_class.new(id: bulk_update_run.id)
      end.to raise_error(ActiveRecord::RecordNotFound)
    end
  end

  describe '#call' do
    subject { described_class.call(bulk_update_run.id) }

    it 'Calls the appropriate LP API for each loan and attribute to be updated' do
      expect(LoanProService::LoanManagementSystem::UpdateLoanSubportfolio).to receive(:call)
        .with(loan_id: loan1.loan_pro_loan_entity.id,
              portfolio_id: LoanPro::SubPortfolioEntity::PARENT_IDS[:beneficial_owner],
              subportfolio_id: beneficial_owner.id, check_investor: false).and_return({})

      expect(LoanProService::LoanManagementSystem::UpdateLoanSubportfolio).to receive(:call)
        .with(loan_id: loan2.loan_pro_loan_entity.id,
              portfolio_id: LoanPro::SubPortfolioEntity::PARENT_IDS[:beneficial_owner],
              subportfolio_id: beneficial_owner.id, check_investor: false).and_return({})

      expect(UpdateLoanProData).to receive(:call)
        .with(loan: loan1, **bulk_run_details).and_return(true)

      expect(UpdateLoanProData).to receive(:call)
        .with(loan: loan2, **bulk_run_details).and_return(true)

      subject
    end

    let!(:bulk_update_run2) do
      create(:bulk_update_run,
             unified_ids: { loan1.unified_id => true, loan2.unified_id => true },
             financed_date: nil,
             loan_transfer_date: nil,
             exclude_from_allocation: true)
    end

    it 'updates the excluded from allocation attribute for 2 loans' do
      expect(loan1.exclude_from_allocation).to eq false
      expect(loan2.exclude_from_allocation).to eq false

      described_class.call(bulk_update_run2.id)

      loan1.reload
      loan2.reload

      expect(loan1.exclude_from_allocation).to eq true
      expect(loan2.exclude_from_allocation).to eq true
    end

    let!(:bulk_update_run3) do
      create(:bulk_update_run,
             unified_ids: { loan1.unified_id => true },
             financed_date: nil,
             loan_transfer_date: nil,
             exclude_from_allocation: true)
    end

    it 'updates the excluded from allocation attribute for 1 loan' do
      expect(loan1.exclude_from_allocation).to eq false
      expect(loan2.exclude_from_allocation).to eq false

      described_class.call(bulk_update_run3.id)

      loan1.reload
      loan2.reload

      expect(loan1.exclude_from_allocation).to eq true
      expect(loan2.exclude_from_allocation).to eq false
    end

    let!(:bulk_update_run4) do
      create(:bulk_update_run,
             unified_ids: { loan3.unified_id => true, loan4.unified_id => true },
             financed_date: nil,
             loan_transfer_date: nil,
             exclude_from_allocation: false)
    end

    it 'updates the excluded from allocation attribute for 1 loan' do
      expect(loan3.exclude_from_allocation).to eq true
      expect(loan4.exclude_from_allocation).to eq true

      described_class.call(bulk_update_run4.id)

      loan3.reload
      loan4.reload

      expect(loan3.exclude_from_allocation).to eq false
      expect(loan4.exclude_from_allocation).to eq false
    end
  end

  describe 'errors' do
    context 'when an exception occurs outside of normal processing' do
      before do
        allow(ActiveSupport::Notifications).to receive(:instrument).and_call_original
        allow(UpdateLoanProData).to receive(:call).and_raise('Boom!')
        allow(LoanProService::LoanManagementSystem::UpdateLoanSubportfolio).to receive(:call).and_return({})
        allow(LoanProService::LoanManagementSystem::UpdateLoanSettings).to receive(:call).and_return({})
      end

      let!(:bulk_update_run5) do
        create(:bulk_update_run,
               unified_ids: { loan1.unified_id => false, loan2.unified_id => false },
               financed_date: financed_date,
               loan_transfer_date: loan_transfer_date,
               exclude_from_allocation: true,
               beneficial_owner_id: beneficial_owner.id,
               purchase_date: purchase_date,
               cspv_repurchase_date: cspv_repurchase_date)
      end

      it 'rescues from ActiveRecord::RecordNotFound' do
        expect do
          described_class.call(id: bulk_update_run.id)
        end.to raise_error(ActiveRecord::RecordNotFound)
      end

      it 'notifies instrumentation and updates the errors hash' do
        described_class.call(bulk_update_run.id)
        expect_to_notify('bulk_update_loans_service', success: false, fail_reason: 'Boom!')
        expect(bulk_update_run.reload.update_errors).to eq({ loan1.unified_id.to_s => 'Boom!', loan2.unified_id.to_s => 'Boom!' })
      end
    end
  end
end
