# frozen_string_literal: true

require 'rails_helper'
Sidekiq::Testing.fake!

RSpec.describe BulkUpdatePaymentsService do
  include NotifierHelper

  let!(:loan1) { create(:above_lending_loan, :unallocated, :loanpro) }
  let!(:loan2) { create(:above_lending_loan, :unallocated, :loanpro) }
  let!(:loan_autopay_entity1) { create(:loan_autopay_entity, loan_entity: loan1.loan_pro_loan_entity) }
  let!(:loan_autopay_entity2) { create(:loan_autopay_entity, loan_entity: loan2.loan_pro_loan_entity) }
  let(:apply_date) { Date.tomorrow.iso8601 }
  let(:process_date) { Date.yesterday.iso8601 }
  let(:expected_autopay_params) do
    { processDateTime: "#{process_date} 19:00:00 UTC", applyDate: apply_date }
  end

  describe '#initialize' do
    subject { described_class.new(ids: [loan_autopay_entity1.id, loan_autopay_entity2.id], apply_date: apply_date, process_datetime: process_date) }
    it { is_expected.to have_attributes(autopay_ids: [loan_autopay_entity1.id, loan_autopay_entity2.id], apply_date: apply_date, process_datetime: process_date) }
  end

  before do
    allow(ActiveSupport::Notifications).to receive(:instrument).and_call_original
  end

  describe '#call' do
    subject { described_class.call(ids: [loan_autopay_entity1.id, loan_autopay_entity2.id], apply_date: apply_date, process_datetime: process_date) }

    it 'Calls the appropriate LP API for each payment and attributes to be updated' do
      expect(LoanProService::LoanManagementSystem::EditAutopay)
        .to receive(:call).with(autopay_id: loan_autopay_entity1.id, loan_id: loan_autopay_entity1.loan_entity.id, attributes: expected_autopay_params).and_return(true)
      expect(LoanProService::LoanManagementSystem::EditAutopay)
        .to receive(:call).with(autopay_id: loan_autopay_entity2.id, loan_id: loan_autopay_entity2.loan_entity.id, attributes: expected_autopay_params).and_return(true)

      subject
    end

    it 'notifies instrumentation and logs error with an invalid ids' do
      described_class.call(ids: ['not_an_id'], apply_date: apply_date, process_datetime: process_date)
      expect_to_notify('edit_autopay', success: false, fail_reason: 'Autopay not_an_id not found')
    end

    context 'when an exception occurs outside of normal processing' do
      before do
        allow_any_instance_of(LoanManagement::EditAutopay).to receive(:call).and_raise('Boom!')
      end

      it 'notifies instrumentation and updates the errors hash' do
        described_class.call(ids: [loan_autopay_entity1.id, loan_autopay_entity2.id], apply_date: apply_date, process_datetime: process_date)
        expect_to_notify('bulk_update_payments_service', success: false, fail_reason: 'Boom!')
      end
    end
  end
end
