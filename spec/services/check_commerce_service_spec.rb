# frozen_string_literal: true

require 'rails_helper'

RSpec.describe CheckCommerceService do
  describe '.configuration' do
    it 'creates a new instance of Configuration' do
      expect(CheckCommerceService::Configuration).to receive(:new)
      described_class.reset
      described_class.configuration
    end
  end

  describe '.reset' do
    it 'creates and assigns a new instance of Configuration' do
      configuration = described_class.configuration
      expect(described_class.reset).not_to eq configuration
    end
  end

  describe '.configure' do
    let(:configuration_params) do
      { login: Faker::Internet.username,
        password: Faker::Internet.password,
        base_url: Faker::Internet.url,
        timeout: Faker::Number.number(digits: 2) }
    end

    it 'creates a new instance of Configuration from block' do
      described_class.configure do |config|
        config.login = configuration_params[:login]
        config.password = configuration_params[:password]
        config.base_url = configuration_params[:base_url]
        config.timeout = configuration_params[:timeout]
      end

      expect(CheckCommerceService.configuration).not_to be_nil
    end
  end
end
