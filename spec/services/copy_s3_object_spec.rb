# frozen_string_literal: true

require 'rails_helper'

RSpec.describe CopyS3Object do
  include NotifierHelper

  let(:key) { 'test/test.csv' }
  let(:source_bucket) { 'source-bucket' }
  let(:target_bucket) { 'target-bucket' }
  let(:s3_mock) { double(copy_object: nil) }

  subject { described_class.call(source_bucket:, target_bucket:, key:) }

  before do
    expect(Aws::S3::Client).to receive(:new).and_return(s3_mock)
  end

  describe 'self.call' do
    before do
      allow(ActiveSupport::Notifications).to receive(:instrument).and_call_original
    end

    it 'returns should true and call copy object' do
      expect(s3_mock).to receive(:copy_object).with(bucket: target_bucket,
                                                    copy_source: "/#{source_bucket}/#{key}",
                                                    key:)
      expect(subject).to be true
    end

    context 'failure' do
      before do
        allow(ExceptionLogger).to receive(:error).and_call_original
        allow(s3_mock).to receive(:copy_object).and_raise('test error')
      end

      it 'raises and log error' do
        expect(subject).to be false
        expect_to_notify('copy_s3_object', success: false, fail_reason: 'test error', meta: { key:, source_bucket:, target_bucket: })
      end
    end
  end
end
