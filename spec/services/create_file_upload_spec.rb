# frozen_string_literal: true

require 'rails_helper'

RSpec.describe CreateFileUpload do
  include NotifierHelper

  let(:file_data) { double(original_filename: 'fake_name') }
  let(:category) { 'fake_category' }
  let(:bucket) { 'test-bucket' }

  describe '#initialize' do
    subject { described_class.new(file_data: file_data, category: category, bucket: bucket) }

    it { is_expected.to have_attributes(file_data: file_data) }
    it { is_expected.to have_attributes(category: category) }
    it { is_expected.to have_attributes(bucket: bucket) }
  end

  describe '#call' do
    before do
      allow(ActiveSupport::Notifications).to receive(:instrument).and_call_original
    end

    subject { described_class.new(file_data: file_data, category: category, bucket: bucket) }

    context 'success' do
      before do
        allow_any_instance_of(FileUpload).to receive(:save!).and_return(true)
      end

      it 'returns true and logs success' do
        expect(subject.call).to be true
        meta = { file_data: file_data, file_bucket: bucket, file_category: category, file_upload: subject.file }
        expect_to_notify('create_file_upload', success: true, meta:)
      end
    end

    context 'failure' do
      before do
        allow_any_instance_of(FileUpload).to receive(:save).and_return(false)
      end

      it 'returns false and logs failure' do
        expect(subject.call).to be false
        meta = { file_data: file_data, file_bucket: bucket, file_category: category, file_upload: subject.file }
        expect_to_notify('create_file_upload', fail_reason: 'File not saved', success: false, meta:)
      end
    end

    context 'error' do
      let(:error) { StandardError.new('Boom!') }

      before do
        allow(ExceptionLogger).to receive(:error).and_call_original
        allow(ActiveSupport::Notifications).to receive(:instrument).and_call_original
        allow(FileUpload).to receive(:find_or_initialize_by).and_raise(error)
      end

      it 'raises and log error' do
        expect(subject.call).to be false
        meta = { file_data: file_data, file_bucket: bucket, file_category: category, file_upload: subject.file }
        expect_to_notify('create_file_upload', fail_reason: 'Boom!', success: false, meta:)
      end
    end
  end
end
