# frozen_string_literal: true

require 'rails_helper'

RSpec.describe DeallocateLoanInvestor do
  let(:investor) { create :above_lending_investor }
  let(:loan) { create :above_lending_loan, :purchased, investor: investor }

  describe '#initialize' do
    subject { described_class.new(loan: loan) }
    it { is_expected.to have_attributes(above_lending_loan: loan) }
  end

  describe '#call' do
    subject { described_class.call(loan: loan) }

    context 'when loanpro loan is not present' do
      let(:loan) { create(:above_lending_loan) }
      it 'should raise error for Loanpro Loan Entity' do
        expect { subject }.to raise_error(/Active Loanpro Loan Entity not found/)
      end
    end

    context 'when investor data is not present on loanpro' do
      let(:loan) { create :above_lending_loan, :unallocated, :loanpro }
      it 'should not raise error' do
        expect { subject }.to_not raise_error
      end

      it 'should update AL investor to nil' do
        subject
        expect(loan.reload.investor).to be_nil
      end
    end

    context 'when investor data is present' do
      it 'should callLoanPro with the correct data and remove AL investor' do
        expect(LoanProService::LoanManagementSystem::UpdateLoanSubportfolio).to receive(:call)
          .with(loan_id: loan.loan_pro_loan_entity.id, subportfolio_id: loan.loan_pro_loan_entity.investor.id, destroy: true, check_investor: false).and_return({})
        subject
        expect(loan.reload.investor).to be_nil
      end
    end

    context 'when a dash loan is present' do
      it 'removes the corresponding investor' do
        expect(LoanProService::LoanManagementSystem::UpdateLoanSubportfolio).to receive(:call)
          .with(loan_id: loan.loan_pro_loan_entity.id, subportfolio_id: loan.loan_pro_loan_entity.investor.id, destroy: true, check_investor: false).and_return({})

        dash_loan = create(:loan, investor: create(:investor), loan_number: loan.unified_id)

        subject

        expect(dash_loan.reload.investor).to be_nil
      end
    end
  end
end
