# frozen_string_literal: true

require 'rails_helper'

RSpec.describe DownloadS3Object do
  include NotifierHelper

  let(:file_name) { 'test.csv' }
  let(:bucket_name) { 'test-bucket' }
  let(:path) { Rails.root.join('tmp', file_name).to_s }

  let(:get_object_response) { instance_double(Aws::S3::Types::GetObjectOutput, body: StringIO.new('read_test')) }
  let(:s3_client) { instance_double(Aws::S3::Client, get_object: get_object_response) }

  describe '#initialize' do
    subject { described_class.new(bucket: bucket_name, key: file_name) }

    it { is_expected.to have_attributes(bucket: bucket_name) }
    it { is_expected.to have_attributes(key: file_name) }
  end

  describe '#to_file' do
    subject { described_class.new(bucket: bucket_name, key: file_name) }

    before do
      allow(ActiveSupport::Notifications).to receive(:instrument).and_call_original
      allow(subject).to receive(:s3_client).and_return(s3_client)
    end

    it 'returns true and logs success' do
      expect(subject.to_file(path:)).to be true
      meta = { response_target: path, bucket: subject.bucket, key: subject.key }
      expect_to_notify('download_s3_object', success: true, meta:)
      FileUtils.rm_f("#{Rails.root}/#{file_name}")
    end

    context 'failure' do
      before do
        allow(ExceptionLogger).to receive(:error).and_call_original
        allow(ActiveSupport::Notifications).to receive(:instrument).and_call_original
        allow(subject).to receive(:object_downloaded?).and_return(false)
      end

      it 'raises and log error' do
        expect(subject.to_file(path:)).to be false
        meta = { response_target: path, bucket: subject.bucket, key: subject.key }
        expect_to_notify('download_s3_object', success: false, fail_reason: 'Object not downloaded', meta:)
      end
    end

    context 'error' do
      let(:error) { Aws::S3::Errors::NoSuchKey.new('test', 'test') }
      let(:client_with_error) do
        Aws::S3::Client.new(stub_responses: { get_object: error })
      end

      before do
        allow(ExceptionLogger).to receive(:error).and_call_original
        allow(ActiveSupport::Notifications).to receive(:instrument).and_call_original
        allow(subject).to receive(:s3_client).and_return(client_with_error)
      end

      it 'raises and log error' do
        expect(subject.to_file(path:)).to be false
        meta = { response_target: path, bucket: subject.bucket, key: subject.key }
        expect_to_notify('download_s3_object', success: false, fail_reason: 'test', meta:)
      end
    end
  end

  describe '#in_memory' do
    subject { described_class.new(bucket: bucket_name, key: file_name) }

    before do
      allow(ActiveSupport::Notifications).to receive(:instrument).and_call_original
      allow(subject).to receive(:s3_client).and_return(s3_client)
    end

    it 'returns true and logs success' do
      retrieved_object = subject.in_memory
      expect(retrieved_object).to be_a(StringIO)
      expect(retrieved_object.read).to eq('read_test')
      meta = { response_target: nil, bucket: subject.bucket, key: subject.key }
      expect_to_notify('download_s3_object', success: true, meta:)
    end

    context 'failure' do
      before do
        allow(ExceptionLogger).to receive(:error).and_call_original
        allow(ActiveSupport::Notifications).to receive(:instrument).and_call_original
        allow(s3_client).to receive(:get_object).and_raise(Aws::S3::Errors::NoSuchKey.new(subject.bucket, subject.key))
      end

      it 'raises and log error' do
        expect(subject.in_memory).to be_nil
        meta = { response_target: nil, bucket: subject.bucket, key: subject.key }
        expect_to_notify('download_s3_object', success: false, fail_reason: 'test.csv', meta:)
      end
    end
  end
end
