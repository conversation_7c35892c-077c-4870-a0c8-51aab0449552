# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanAllocation::AcceptEngineRun do
  let(:engine_run) { create(:loan_allocation_engine_run) }
  let(:candidate_loan) { engine_run.candidate_loans.first['loan_id'] }

  before do
    allow(Slack::DashBot).to receive(:call)
    engine_run.update!(candidate_loans: [{ 'unified_id' => candidate_loan }])
  end

  subject { described_class.call(engine_run) }

  describe '#call' do
    before { create(:category, category: 'loan_allocation') }

    context 'when there are no unallocated loans' do
      let(:run_path) { Rails.application.routes.url_helpers.admin_loan_allocation_engine_run_path(engine_run) }
      let(:final_totals) { { all_originated: { 'crb' => '2000', 'aft' => '3000' } } }
      let(:allocations) { { candidate_loan => 'above_funding_trust' } }

      before { engine_run.update!(allocations:, final_totals:) }

      it 'creates a file upload and process allocations' do
        expect(ProcessLoanAllocation).to receive(:perform_async).with(allocations)
        expect { subject }.to change(FileUpload.all, :count).by(1)
      end

      it 'sends a proper slack message' do
        expect(Slack::DashBot).to receive(:call) do |message:, **|
          expect(message).to include("Engine Run ##{engine_run.id} accepted!")
          expect(message).to include(run_path)
          expect(message).to include('* crb - 40% ($ 2,000.00)')
          expect(message).to include('* aft - 60% ($ 3,000.00)')
        end

        subject
      end
    end

    context 'when the engine run is not from today' do
      before { engine_run.update!(created_at: 1.day.ago) }

      it 'raises an InvalidError' do
        expect { subject }.to raise_error(described_class::InvalidError, /Only today's engine runs can be accepted/)
      end
    end

    context 'when there are unallocated loans' do
      before { engine_run.update!(allocations: {}) }

      it 'raises an InvalidError' do
        expect { subject }.to raise_error(described_class::InvalidError, /The following loans were not allocated/)
      end
    end
  end
end
