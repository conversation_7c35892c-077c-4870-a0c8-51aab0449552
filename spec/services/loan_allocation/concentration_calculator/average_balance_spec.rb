# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanAllocation::ConcentrationCalculator::AverageBalance do
  let(:investor) { create(:above_lending_investor, name: 'comvest') }
  let(:concentration) { create(:concentration, investor: investor, category: :average_balance, balance_type: balance_type) }
  let(:balance_type) { 'upb' }
  let!(:loan1) { create(:above_lending_loan, :purchased, investor: investor) }
  let!(:loan2) { create(:above_lending_loan, :purchased, investor: investor) }
  let!(:offer1) { create(:above_lending_offer, loan: loan1) }
  let!(:offer2) { create(:above_lending_offer, loan: loan2) }
  let!(:repaid_loan) { create(:above_lending_loan, :purchased, investor: investor) }
  let!(:repaid_loan_offer) { create(:above_lending_offer, loan: repaid_loan) }
  let!(:archive1) { create(:loan_reverse_status_archive, loan_entity: loan1.loan_pro_loan_entity) }
  let!(:archive2) { create(:loan_reverse_status_archive, loan_entity: loan2.loan_pro_loan_entity) }
  let!(:repaid_loan_archive) { create(:loan_reverse_status_archive, loan_entity: repaid_loan.loan_pro_loan_entity, principal_balance: 0) }
  let(:upb_sum) { archive1.principal_balance + archive2.principal_balance }

  describe '#loans' do
    context 'when balance_type is upb' do
      it 'should not include repaid loans' do
        expect(concentration.loans).to_not include(repaid_loan.loan_pro_loan_entity)
      end
    end
    context 'when balance_type is ipb' do
      let(:balance_type) { 'ipb' }
      it 'should include repaid loans' do
        expect(concentration.loans).to include(repaid_loan.loan_pro_loan_entity)
      end
    end
  end

  describe '#calculate' do
    context 'when balance_type is UPB' do
      let(:all_loans) { [loan1, loan2] }
      let(:test_average_balance) { (upb_sum / all_loans.count).round(2) }
      let(:balance_type) { 'upb' }

      it 'should calculate the average balance for the investor, based on UPB' do
        (value, metadata) = concentration.calculate

        expect(value).to eq(test_average_balance)
        expect(metadata.values).to include(upb_sum)
        expect(metadata.values).to include(all_loans.count)
      end
    end

    context 'when balance_type is IPB' do
      let(:ipb_sum) { offer1.amount + offer2.amount + repaid_loan_offer.amount }
      let(:all_loans) { [loan1, loan2, repaid_loan] }
      let(:test_average_balance) { (ipb_sum / all_loans.count).round(2) }
      let(:balance_type) { 'ipb' }

      it 'should calculate the average balance for the investor, based on IPB' do
        (value, metadata) = concentration.calculate

        expect(value).to eq(test_average_balance)
        expect(metadata.values).to include(ipb_sum)
        expect(metadata.values).to include(all_loans.count)
      end
    end
  end

  describe '#prerequisite' do
    it 'should be applicable for all loans' do
      expect(concentration.prerequisite({})).to be_truthy
    end
  end

  describe '#supported_balance_types' do
    subject { concentration.supported_balance_types }
    it { should eq(%i[upb ipb]) }
  end

  describe '#update_metadata' do
    let(:loan_details) { create_unallocated_loan_record(loan_amount: Faker::Number.decimal) }
    it 'should update the total and count of loans' do
      (_value, metadata) = concentration.calculate

      concentration.update_metadata(loan_details)
      expect(metadata[:total_upb]).to eq(upb_sum + loan_details[:loan_amount])
      expect(metadata[:loans_count]).to eq(3)
    end
  end
end
