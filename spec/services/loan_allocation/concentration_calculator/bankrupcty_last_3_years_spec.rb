# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanAllocation::ConcentrationCalculator::BankruptcyLast3Years do
  let(:investor) { create(:above_lending_investor, name: 'comvest') }
  let(:concentration) { create(:concentration, investor: investor, category: :bankruptcy_last_3_years, balance_type: balance_type) }
  let(:balance_type) { 'upb' }
  let!(:loan1) { create(:above_lending_loan, :purchased, investor: investor, bankruptcy_filed_date: 1.year.ago.to_date) }
  let!(:loan2) { create(:above_lending_loan, :purchased, investor: investor, bankruptcy_filed_date: 4.years.ago.to_date) }
  let!(:loan3) { create(:above_lending_loan, :purchased, investor: investor, bankruptcy_filed_date: nil) }
  let!(:archive1) { create(:loan_reverse_status_archive, loan_entity: loan1.loan_pro_loan_entity) }
  let!(:archive2) { create(:loan_reverse_status_archive, loan_entity: loan2.loan_pro_loan_entity) }
  let!(:archive3) { create(:loan_reverse_status_archive, loan_entity: loan3.loan_pro_loan_entity) }
  let(:upb_sum) { archive1.principal_balance + archive2.principal_balance + archive3.principal_balance }

  describe '#filtered_loans' do
    it 'should include loans with bankruptcy date < 3 years' do
      expect(concentration.filtered_loans).to include(loan1.loan_pro_loan_entity)
    end

    it 'should not include loans with older bankrupt dates or nil dates' do
      expect(concentration.filtered_loans).to_not include(loan2.loan_pro_loan_entity)
      expect(concentration.filtered_loans).to_not include(loan3.loan_pro_loan_entity)
    end
  end

  describe '#calculate' do
    let(:test_percentage) { (archive1.principal_balance * 100) / upb_sum }

    it 'should retrieve UPB sums for valid loans' do
      value, metadata = concentration.calculate
      expect(value).to eq(test_percentage.round(5))
      expect(metadata.values).to include(archive1.principal_balance)
      expect(metadata.values).to include(upb_sum)
    end
  end

  describe '#prerequisite' do
    subject { concentration.prerequisite(bankruptcy_filed_date: bankruptcy_filed_date, contract_date: Date.today.to_s) }

    context 'when loan bankruptcy date is within 3 years' do
      let(:bankruptcy_filed_date) { 1.year.ago.to_date.to_s }
      it { should be_truthy }

      context 'when the date is not a string' do
        let(:bankruptcy_filed_date) { 1.years.ago.to_date }
        it { should be_truthy }
      end
    end

    context 'when loan bankruptcy date is more than 3 years' do
      let(:bankruptcy_filed_date) { 4.years.ago.to_date.to_s }
      it { should be_falsey }

      context 'when the date is not a string' do
        let(:bankruptcy_filed_date) { 4.years.ago.to_date }
        it { should be_falsey }
      end
    end

    context 'when loan bankruptcy date is nil' do
      let(:bankruptcy_filed_date) { nil }
      it { should be_falsey }
    end
  end

  describe '#update_metadata' do
    it 'should update percentage metadata' do
      expect(concentration).to receive(:update_percentage_metadata)
      concentration.update_metadata({})
    end
  end

  describe '#supported_balance_types' do
    subject { concentration.supported_balance_types }
    it { should eq(%i[upb]) }
  end
end
