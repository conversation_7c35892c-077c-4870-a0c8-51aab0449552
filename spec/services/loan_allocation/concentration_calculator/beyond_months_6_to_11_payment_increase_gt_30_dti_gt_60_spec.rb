# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanAllocation::ConcentrationCalculator::BeyondMonths6To11PaymentIncreaseGt30DtiGt60 do
  let(:investor) { create(:above_lending_investor, name: 'comvest') }
  let(:concentration) { create(:concentration, investor: investor, category: :beyond_months_6_to_11_payment_increase_gt_30_dti_gt_60, balance_type: :upb) }

  describe '#prerequisite' do
    let(:valid_loan_details) { { months_at_beyond: 6, payment_increase_percentage: 31, external_dti: 61 } }
    subject { concentration.prerequisite(loan_details) }

    context 'when the loan meets the criteria' do
      let(:loan_details) { create_unallocated_loan_record(valid_loan_details) }
      it { should be_truthy }
    end

    %i[payment_increase_percentage external_dti months_at_beyond].each do |key|
      context "when the loan is missing #{key}" do
        let(:loan_details) { create_unallocated_loan_record(valid_loan_details.merge(key => nil)) }
        it { should be_truthy }
      end
    end

    context 'when months at beyond < 6' do
      let(:loan_details) { create_unallocated_loan_record(valid_loan_details.merge(months_at_beyond: 5)) }
      it { should be_falsey }
    end

    context 'when months at beyond > 11' do
      let(:loan_details) { create_unallocated_loan_record(valid_loan_details.merge(months_at_beyond: 12)) }
      it { should be_falsey }
    end

    context 'when payment_increase_percentage <= 30' do
      let(:loan_details) { create_unallocated_loan_record(valid_loan_details.merge(payment_increase_percentage: 30)) }
      it { should be_falsey }
    end

    context 'when DTI <= 60' do
      let(:loan_details) { create_unallocated_loan_record(valid_loan_details.merge(external_dti: 60)) }
      it { should be_falsey }
    end
  end

  describe '#filtered_loans' do
    let!(:loan) { create(:above_lending_loan, :purchased, investor: investor, months_since_enrollment: months, dti: dti) }
    let(:months) { 6 }
    let(:dti) { 61 }
    let(:beyond_payment_amount) { Faker::Number.decimal(l_digits: 3) }
    let(:payment_amount) { beyond_payment_amount * 1.31 }
    let!(:offer) { create(:above_lending_offer, loan: loan, selected: true, initial_term_payment: payment_amount) }
    before do
      loan.loan_payment_detail.update(beyond_payment_amount: beyond_payment_amount)
    end

    subject { concentration.filtered_loans }

    context 'when a loan meets the criteria' do
      it { should include(loan.loan_pro_loan_entity) }
    end

    context 'when a loan has <6 months at beyond but otherwise valid' do
      let(:months) { 5 }
      it { should_not include(loan.loan_pro_loan_entity) }
    end

    context 'when a loan has >11 months at beyond but otherwise valid' do
      let(:months) { 12 }
      it { should_not include(loan.loan_pro_loan_entity) }
    end

    context 'when a loan has payment increase <=30% but otherwise valid' do
      let(:payment_amount) { beyond_payment_amount * 1.3 }
      it { should_not include(loan.loan_pro_loan_entity) }
    end

    context 'when a loan has DTI <=60% but otherwise valid' do
      let(:dti) { 0.60 }
      it { should_not include(loan.loan_pro_loan_entity) }
    end
  end

  describe '#supported_balance_types' do
    subject { concentration.supported_balance_types }
    it { should eq(%i[upb]) }
  end

  describe '#update_metadata' do
    it 'should update percentage metadata' do
      expect(concentration).to receive(:update_percentage_metadata)
      concentration.update_metadata({})
    end
  end

  describe '#calculate' do
    it 'should delegate to balance_percentage' do
      expect(concentration).to receive(:balance_percentage).and_call_original
      concentration.calculate
    end
  end
end
