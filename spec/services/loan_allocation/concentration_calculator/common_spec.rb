# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanAllocation::ConcentrationCalculator::Common do
  let!(:al_loan) { create(:above_lending_loan, :purchased, investor: investor, product_type: 'DM', originating_party: 'CRB') }
  let!(:excluded_al_loan) { create(:above_lending_loan, :purchased, investor: investor, product_type: 'DM', originating_party: 'CRB', exclude_from_allocation: true) }
  let!(:loan_entity) { al_loan.loan_pro_loan_entity }
  let!(:excluded_loan_entity) { excluded_al_loan.loan_pro_loan_entity }
  let(:investor) { create(:above_lending_investor, name: 'above_funding_trust') }
  let(:concentration) { create(:concentration, investor: investor, category: :wa_interest_rate) }

  describe 'loan excluded from concentration' do
    it 'does not include loans flagged for exclusion' do
      expect(concentration.loans.include?(excluded_loan_entity)).to eq(false)
    end
  end

  describe '#ipb_sum' do
    let(:concentration) { create(:concentration, investor: investor, category: :non_agl, balance_type: :ipb) }
    let!(:offer) { create(:above_lending_offer, loan: al_loan) }
    subject { concentration.ipb_sum([loan_entity]) }

    it 'should return IPB sum for the given loans' do
      expect(subject).to eq(offer.amount)
    end
  end

  describe '#upb_sum' do
    let!(:loan1_new_status) { create :loan_reverse_status_archive, loan_entity: loan_entity, date: Date.today, principal_balance: Faker::Number.between(from: 100, to: 999) }

    subject { concentration.upb_sum([loan_entity]) }

    context 'for no date' do
      it 'should return UPB sum for today\'s date' do
        expect(subject).to eq(loan1_new_status.principal_balance)
      end
    end
  end

  describe 'metadata update methods' do
    let(:loan_details) { create_unallocated_loan_record(state: 'CA', loan_amount: Faker::Number.number(digits: 3), interest_rate: Faker::Number.decimal(l_digits: 2)) }
    before do
      allow(concentration).to receive(:metadata).and_return(metadata)
    end

    describe '#update_state_metadata' do
      let(:ca_total) { Faker::Number.number(digits: 5) }
      let(:az_total) { Faker::Number.number(digits: 5) }
      let(:metadata) { { 'CA' => ca_total, 'AZ' => az_total } }

      before { concentration.update_state_metadata(loan_details) }

      it 'should update state metadata' do
        expect(metadata['CA']).to eq(ca_total + loan_details[:loan_amount])
        expect(metadata['AZ']).to eq(az_total)
      end

      context 'when state does not already exist in the metadata' do
        let(:loan_details) { create_unallocated_loan_record(state: 'IL', loan_amount: Faker::Number.number(digits: 3)) }
        it 'should update state metadata' do
          expect(metadata['IL']).to eq(loan_details[:loan_amount])
        end
      end
    end

    describe '#update_percentage_metadata' do
      let(:numerator_key) { Faker::Superhero.name.to_sym }
      let(:denominator_key) { Faker::Superhero.name.to_sym }
      let(:numerator_total) { Faker::Number.number(digits: 5) }
      let(:denominator_total) { Faker::Number.number(digits: 7) }
      let(:metadata) { { numerator_key => numerator_total, denominator_key => denominator_total } }

      context 'when loan meets the prerequisite for the concentration' do
        before do
          allow(concentration).to receive(:prerequisite).with(loan_details).and_return(true)
          concentration.update_percentage_metadata(numerator_key, denominator_key, loan_details)
        end

        it 'should update the numerator' do
          expect(metadata[numerator_key]).to eq(numerator_total + loan_details[:loan_amount])
        end
      end

      context 'when loan does not meet the prerequisite for the concentration' do
        before do
          allow(concentration).to receive(:prerequisite).with(loan_details).and_return(false)
          concentration.update_percentage_metadata(numerator_key, denominator_key, loan_details)
        end

        it 'should not update the numerator' do
          expect(metadata[numerator_key]).to eq(numerator_total)
        end
      end

      it 'should update the denominator' do
        concentration.update_percentage_metadata(numerator_key, denominator_key, loan_details)
        expect(metadata[denominator_key]).to eq(denominator_total + loan_details[:loan_amount])
      end
    end

    describe '#update_weighted_metadata' do
      let(:numerator_key) { Faker::Superhero.name.to_sym }
      let(:denominator_key) { Faker::Superhero.name.to_sym }
      let(:numerator_total) { Faker::Number.number(digits: 5) }
      let(:denominator_total) { Faker::Number.number(digits: 7) }
      let(:metadata) { { numerator_key => numerator_total, denominator_key => denominator_total } }

      context 'when loan weight is absent' do
        before do
          allow(concentration).to receive(:candidate_weight).with(loan_details).and_return(nil)
          concentration.update_weighted_metadata(numerator_key, denominator_key, loan_details)
        end

        it 'should not update the numerator' do
          expect(metadata[numerator_key]).to eq(numerator_total)
        end
      end

      context 'when loan weight is present' do
        before do
          concentration.update_weighted_metadata(numerator_key, denominator_key, loan_details)
        end

        it 'should update the numerator' do
          numerator = numerator_total + loan_details[:loan_amount] * loan_details[:interest_rate]
          expect(metadata[numerator_key]).to be_within(0.01).of(numerator)
        end

        it 'should update the denominator' do
          expect(metadata[denominator_key]).to eq(denominator_total + loan_details[:loan_amount])
        end
      end
    end
  end
end
