# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanAllocation::ConcentrationCalculator::CrbOriginatedAglContractSince do
  let(:investor) { create(:above_lending_investor, name: 'comvest') }
  let(:investor2) { create(:above_lending_investor, name: 'above_funding_trust_2') }
  let(:concentration) { create(:concentration, investor: investor, category: :crb_originated_agl_contract_since, parameter: 1.year.ago.to_s, balance_type: :ipb) }
  let!(:old_loan) { create(:above_lending_loan, :purchased, product_type: 'IPL', investor: investor2, amount: 10_000, contract_date: 1.year.ago - 1.day) }
  let!(:loan1) { create(:above_lending_loan, :purchased, product_type: 'IPL', investor: investor, amount: 10_000) }
  let!(:loan2) { create(:above_lending_loan, :purchased, product_type: 'IPL', investor: investor2, amount: 50_000) }
  let!(:loan3) { create(:above_lending_loan, :purchased, product_type: 'IPL', investor: investor2, amount: 20_000) }
  let!(:loan4) { create(:above_lending_loan, :purchased, product_type: 'IPL', investor: investor2, amount: 30_000) }
  let!(:loan5) { create(:above_lending_loan, :purchased, product_type: 'DM', investor: investor, amount: 30_000) }
  let!(:above_originated_loan) { create(:above_lending_loan, :purchased, investor: investor, amount: 10_000, originator_title: 'Above Lending', originating_party: 'Above Lending') }
  let!(:loan1_offer) { create(:above_lending_offer, loan: loan1) }
  let!(:loan2_offer) { create(:above_lending_offer, loan: loan2) }
  let!(:loan3_offer) { create(:above_lending_offer, loan: loan3) }
  let!(:loan4_offer) { create(:above_lending_offer, loan: loan4) }
  let!(:above_originated_loan_offer) { create(:above_lending_offer, loan: above_originated_loan) }
  let(:crb_originated_loans) { [loan1, loan2, loan3, loan4] }
  let(:old_crb_loans) { [loan1, loan2, loan3, loan4] }

  describe '#loans' do
    it 'should retrieve all CRB originated loans with contract date since parameter date' do
      expect(concentration.loans).to include(*crb_originated_loans.map(&:loan_pro_loan_entity))
    end

    it 'should not retrieve CRB originated loans with contract date before parameter date' do
      expect(concentration.loans).to_not include(old_loan.loan_pro_loan_entity)
    end

    it 'should not retrieve Above originated loans' do
      expect(concentration.loans).to_not include(above_originated_loan.loan_pro_loan_entity)
    end

    it 'should not retrieve CRB originated DM loans' do
      expect(concentration.loans).to_not include(loan5.loan_pro_loan_entity)
    end
  end

  describe '#filtered_loans' do
    it 'should retrieve CRB originated loans purchased by investor' do
      expect(concentration.filtered_loans).to include(loan1.loan_pro_loan_entity)
    end

    it 'should not retrieve Above originated loans purchased by investor' do
      expect(concentration.filtered_loans).to_not include(above_originated_loan.loan_pro_loan_entity)
    end

    it 'should not retrieve CRB originated loans purchased by another investor' do
      expect(concentration.filtered_loans).to_not include(loan4.loan_pro_loan_entity)
    end
  end

  describe '#calculate' do
    context 'when the balance_type is ipb' do
      it 'should calculate the IPB percentage for the investor across all CRB originated loans' do
        test_value = (loan1.selected_offer.amount * 100 / crb_originated_loans.map(&:selected_offer).map(&:amount).sum).round(5)
        (value,) = concentration.calculate
        expect(value).to eq(test_value)
      end
    end
  end

  describe '#prerequisite' do
    subject { concentration.prerequisite(originator: originator) }

    context 'when loan originator is CRB' do
      let(:originator) { 'CRB' }
      it { should be_truthy }
    end

    context 'when loan originator is ABOVE' do
      let(:originator) { 'ABOVE' }
      it { should be_falsey }
    end
  end

  describe '#supported_balance_types' do
    subject { concentration.supported_balance_types }
    it { should eq(%i[ipb]) }
  end

  describe '#update_metadata' do
    it 'should update percentage metadata' do
      expect(concentration).to receive(:update_percentage_metadata)
      concentration.update_metadata({})
    end
  end

  describe '#update_metadata_for_other_allocations' do
    let(:amount) { Faker::Number.number(digits: 4) }
    let(:investor_ipb) { concentration.metadata[:"#{investor.name}_ipb"] }
    let(:total_ipb) { concentration.metadata[:total_ipb] }

    context 'when originator is CRB' do
      let(:originator) { 'CRB' }
      it 'updates only the total IPB' do
        concentration.calculate
        # load the following ipb data
        investor_ipb
        total_ipb
        concentration.update_metadata_for_other_allocations(
          loan_amount: amount,
          originator: originator
        )
        expect(concentration.metadata[:total_ipb]).to eq(total_ipb + amount)
        expect(concentration.metadata[:"#{investor.name}_ipb"]).to eq(investor_ipb)
      end
    end
  end
end
