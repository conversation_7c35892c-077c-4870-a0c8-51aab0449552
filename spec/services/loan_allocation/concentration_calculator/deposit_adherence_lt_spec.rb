# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanAllocation::ConcentrationCalculator::DepositAdherenceLt do
  let(:investor) { create(:above_lending_investor, name: 'above_funding_trust') }
  let(:deposit_adherence) { Faker::Number.decimal(l_digits: 0, r_digits: 1) + 0.01 }
  let(:concentration) { create(:concentration, investor: investor, category: :deposit_adherence_lt, parameter: deposit_adherence, balance_type: :upb) }
  let!(:loan1) { create(:above_lending_loan, :purchased, investor: investor, payment_adherence_ratio_3_months: deposit_adherence, months_since_enrollment: 2) }
  let!(:loan2) { create(:above_lending_loan, :purchased, investor: investor, payment_adherence_ratio_6_months: deposit_adherence - 0.01, months_since_enrollment: 12) }
  let!(:loan3) { create(:above_lending_loan, :purchased, investor: investor, payment_adherence_ratio_6_months: deposit_adherence + 0.01, months_since_enrollment: 12) }
  let!(:loan4) do
    create(:above_lending_loan, :purchased,
           investor: investor, payment_adherence_ratio_3_months: deposit_adherence - 0.01,
           payment_adherence_ratio_6_months: deposit_adherence + 0.01, months_since_enrollment: 6)
  end

  describe '#filtered_loans' do
    it 'should include lower deposit adherence loan' do
      expect(concentration.filtered_loans).to include(loan1.loan_pro_loan_entity)
      expect(concentration.filtered_loans).to include(loan2.loan_pro_loan_entity)
    end

    it 'should not include higher deposit adherence loan loan' do
      expect(concentration.filtered_loans).to_not include(loan3.loan_pro_loan_entity)
      expect(concentration.filtered_loans).to_not include(loan4.loan_pro_loan_entity)
    end
  end

  describe '#calculate' do
    context 'when balance_type is UPB' do
      let(:balance_type) { 'upb' }
      let!(:archive1) { create(:loan_reverse_status_archive, loan_entity: loan1.loan_pro_loan_entity) }
      let!(:archive2) { create(:loan_reverse_status_archive, loan_entity: loan2.loan_pro_loan_entity) }
      let!(:archive3) { create(:loan_reverse_status_archive, loan_entity: loan3.loan_pro_loan_entity) }

      it 'should correctly calculate the UPB scores' do
        numerator = archive1.principal_balance + archive2.principal_balance
        denominator = [archive1, archive2, archive3].map(&:principal_balance).sum
        (value, metadata) = concentration.calculate
        expect(value).to eq((numerator / denominator * 100.0).round(5))
        expect(metadata.values).to include(numerator)
        expect(metadata.values).to include(denominator)
      end
    end
  end

  describe '#prerequisite' do
    subject do
      concentration.prerequisite(months_at_beyond: beyond_months,
                                 payment_adherence_ratio_3_months: payment_adherence_ratio_3_months,
                                 payment_adherence_ratio_6_months: payment_adherence_ratio_6_months)
    end

    context 'when months at beyond is less than parameter' do
      let(:beyond_months) { 2 }
      let(:payment_adherence_ratio_3_months) { Faker::Number.number(digits: 2) }
      let(:payment_adherence_ratio_6_months) { Faker::Number.number(digits: 2) }
      it { should be_truthy }
    end

    context 'when months at beyond is >=6' do
      let(:beyond_months) { 6 }
      let(:payment_adherence_ratio_3_months) { deposit_adherence - 0.1 }

      context 'when 6mo adherence is < parameter' do
        let(:payment_adherence_ratio_6_months) { deposit_adherence - 0.01 }
        it { should be_truthy }
      end
      context 'when 6mo adherence is >= parameter' do
        let(:payment_adherence_ratio_6_months) { deposit_adherence }
        it { should be_falsey }
      end
    end

    context 'when months at beyond is >=3 and <6' do
      let(:beyond_months) { 3 }
      let(:payment_adherence_ratio_6_months) { nil }

      context 'when 3mo adherence is < parameter' do
        let(:payment_adherence_ratio_3_months) { deposit_adherence - 0.1 }
        it { should be_truthy }
      end
      context 'when 3mo adherence is >= parameter' do
        let(:payment_adherence_ratio_3_months) { deposit_adherence }
        it { should be_falsey }
      end
    end

    context 'when months at beyond is nil' do
      let(:beyond_months) { nil }
      let(:payment_adherence_ratio_3_months) { deposit_adherence }
      let(:payment_adherence_ratio_6_months) { deposit_adherence }

      it { should be_truthy }
    end
  end

  describe '#supported_balance_types' do
    subject { concentration.supported_balance_types }
    it { should eq(%i[upb]) }
  end

  describe '#update_metadata' do
    it 'should update percentage metadata' do
      expect(concentration).to receive(:update_percentage_metadata)
      concentration.update_metadata({})
    end
  end
end
