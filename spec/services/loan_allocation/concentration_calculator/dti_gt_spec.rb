# frozen_string_literal: true

require 'rails_helper'
RSpec.describe LoanAllocation::ConcentrationCalculator::DtiGt do
  let(:investor) { create(:above_lending_investor) }
  let!(:parameter) { 0.60 }
  let(:upb) { 500 }

  let(:concentration) { create(:concentration, investor: investor, category: :dti_gt, parameter: parameter) }
  let!(:loan1) { create(:above_lending_loan, :purchased, :with_selected_offer, investor: investor, dti: 0.61) }
  let!(:loan2) { create(:above_lending_loan, :purchased, :with_selected_offer, investor: investor, dti: 0.59) }

  let!(:archive1) { create(:loan_reverse_status_archive, loan_entity: loan1.loan_pro_loan_entity, principal_balance: upb) }
  let!(:archive2) { create(:loan_reverse_status_archive, loan_entity: loan2.loan_pro_loan_entity, principal_balance: upb) }

  describe '#filtered_loans' do
    it 'should include higher dti loan' do
      expect(concentration.filtered_loans).to include(loan1.loan_pro_loan_entity)
    end

    it 'should not include lower dti loan' do
      expect(concentration.filtered_loans).to_not include(loan2.loan_pro_loan_entity)
    end
  end

  describe '#supported_balance_types' do
    subject { concentration.supported_balance_types }
    it { should eq(%i[upb]) }
  end

  describe '#calculate' do
    it 'should calculate upb sum' do
      numerator = archive1.principal_balance
      denominator = [archive1, archive2].map(&:principal_balance).sum
      (value, metadata) = concentration.calculate
      expect(value).to eq((numerator / denominator * 100.0).round(2))
      expect(metadata.values).to include(numerator)
      expect(metadata.values).to include(denominator)
    end
  end

  describe '#update_metadata' do
    it 'should update percentage metadata' do
      expect(concentration).to receive(:update_percentage_metadata)
      concentration.update_metadata({})
    end
  end
end
