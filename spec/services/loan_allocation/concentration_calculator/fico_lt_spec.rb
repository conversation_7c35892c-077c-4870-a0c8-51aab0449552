# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanAllocation::ConcentrationCalculator::FicoLt do
  let(:investor) { create(:above_lending_investor, name: 'above_funding_trust_2') }
  let(:credit_score) { Faker::Number.between(from: 500, to: 999) }

  let(:concentration) { create(:concentration, investor: investor, category: :fico_lt, parameter: credit_score, balance_type: :ipb) }
  let(:upb_concentration) { create(:concentration, investor: investor, category: :fico_lt, parameter: credit_score, balance_type: :upb) }
  let!(:loan1) { create(:above_lending_loan, :purchased, investor: investor, credit_score: credit_score - rand(1..100)) }
  let!(:loan2) { create(:above_lending_loan, :purchased, investor: investor, credit_score: credit_score + rand(1..100)) }
  let!(:loan1_reverse_status_archive) { create(:loan_reverse_status_archive, principal_balance: (loan1.amount - 100), loan_id: loan1.loan_pro_loan_entity.id) }
  let!(:loan2_reverse_status_archive) { create(:loan_reverse_status_archive, principal_balance: (loan2.amount - 100), loan_id: loan2.loan_pro_loan_entity.id) }

  describe '#filtered_loans' do
    it 'should include lower credit score loan' do
      expect(concentration.filtered_loans).to include(loan1.loan_pro_loan_entity)
    end

    it 'should not include higher credit score loan' do
      expect(concentration.filtered_loans).to_not include(loan2.loan_pro_loan_entity)
    end
  end

  describe '#calculate' do
    let!(:offer1) { create(:above_lending_offer, loan: loan1) }
    let!(:offer2) { create(:above_lending_offer, loan: loan2) }

    it 'should correctly calculate the IPB scores' do
      numerator = loan1.selected_offer.amount
      denominator = [loan1, loan2].map(&:selected_offer).sum(&:amount)
      (value, metadata) = concentration.calculate
      expect(value).to eq((numerator / denominator * 100.0).round(5))
      expect(metadata.values).to include(numerator)
      expect(metadata.values).to include(denominator)
    end

    it 'should correctly calculate the UPB scores' do
      loan1_principle = loan1_reverse_status_archive.principal_balance
      loan2_principle = loan2_reverse_status_archive.principal_balance
      numerator = loan1_principle
      denominator = [loan1_principle, loan2_principle].sum
      (value, metadata) = upb_concentration.calculate
      expect(value).to eq((numerator / denominator * 100.0).round(5))
      expect(metadata.values).to include(numerator)
      expect(metadata.values).to include(denominator)
    end

    it 'works if a loan does not have a credit score' do
      loan2.update!(credit_score: nil)
      amount = loan1.selected_offer.amount
      (value, *) = concentration.calculate
      expect(value).to eq((amount / amount * 100.0).round(5))
    end
  end

  describe '#prerequisite' do
    it 'should be applicable for loans where the credit score is lower than the parameter' do
      expect(concentration.prerequisite({ credit_score: credit_score - rand(1..100) })).to be_truthy
      expect(concentration.prerequisite({ credit_score: credit_score + rand(1..100) })).to be_falsey
    end

    it 'works when credit score is nil' do
      expect(concentration.prerequisite({ credit_score: nil })).to be_truthy
    end
  end

  describe '#supported_balance_types' do
    subject { concentration.supported_balance_types }
    it { should eq(%i[ipb upb]) }
  end

  describe '#update_metadata' do
    it 'should update percentage metadata' do
      expect(concentration).to receive(:update_percentage_metadata)
      concentration.update_metadata({})
    end
  end
end
