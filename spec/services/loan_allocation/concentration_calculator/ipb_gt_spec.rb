# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanAllocation::ConcentrationCalculator::IpbGt do
  let(:investor) { create(:above_lending_investor, name: 'above_funding_trust') }
  let(:ipb) { Faker::Number.between(from: 20_000, to: 25_000) }

  let(:concentration) { create(:concentration, investor: investor, category: :ipb_gt, parameter: ipb, balance_type: :ipb) }
  let!(:loan1) { create(:above_lending_loan, :purchased, :with_selected_offer, investor: investor, amount: ipb * 1.1) }
  let!(:loan2) { create(:above_lending_loan, :purchased, :with_selected_offer, investor: investor, amount: ipb * 0.5) }

  describe '#filtered_loans' do
    it 'should include higher ipb loan' do
      expect(concentration.filtered_loans).to include(loan1.loan_pro_loan_entity)
    end

    it 'should not include lower ipb loan' do
      expect(concentration.filtered_loans).to_not include(loan2.loan_pro_loan_entity)
    end
  end

  describe '#calculate' do
    it 'should correctly calculate the IPB scores' do
      numerator = loan1.selected_offer.amount
      denominator = [loan1, loan2].map(&:selected_offer).map(&:amount).sum
      (value, metadata) = concentration.calculate
      expect(value).to eq((numerator / denominator * 100.0).round(5))
      expect(metadata.values).to include(numerator)
      expect(metadata.values).to include(denominator)
    end
  end

  describe '#prerequisite' do
    it 'should be applicable for loans where the credit score is lower than the parameter' do
      expect(concentration.prerequisite({ loan_amount: ipb + rand(1..100) })).to be_truthy
      expect(concentration.prerequisite({ loan_amount: ipb - rand(1..100) })).to be_falsey
    end
  end

  describe '#supported_balance_types' do
    subject { concentration.supported_balance_types }
    it { should eq(%i[upb ipb]) }
  end

  describe '#update_metadata' do
    it 'should update percentage metadata' do
      expect(concentration).to receive(:update_percentage_metadata)
      concentration.update_metadata({})
    end
  end
end
