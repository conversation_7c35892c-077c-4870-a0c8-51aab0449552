# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanAllocation::ConcentrationCalculator::MaxBorrowerLoansAsPercentOf do
  let(:investor) { create(:above_lending_investor, name: 'above_funding_trust') }
  let!(:above_funding_trust_2) { create(:above_lending_investor, name: :above_funding_trust_2) }
  let(:max_facility_amount) { Faker::Number.number(digits: 7) }
  let(:concentration) { create(:concentration, investor: investor, category: :max_borrower_loans_as_percent_of, balance_type: :upb, parameter: max_facility_amount) }
  let!(:loan) { create(:above_lending_loan, :purchased, investor: investor, product_type: 'IPL', originating_party: 'CRB') }
  let!(:loan_archive) { create(:loan_reverse_status_archive, loan_entity: loan.loan_pro_loan_entity, principal_balance: (loan.amount * 0.75)) }
  let!(:unallocated_loan) { create(:above_lending_loan, :unallocated, product_type: 'IPL', originating_party: 'CRB', borrower_tmp: loan.borrower) }
  let!(:other_investor_loan) { create(:above_lending_loan, :purchased, investor: create(:above_lending_investor), product_type: 'IPL', originating_party: 'CRB', borrower_tmp: loan.borrower) }

  before do
    concentration.loan_details = create_unallocated_loan_record(
      loan_amount: unallocated_loan.amount,
      unified_id: unallocated_loan.unified_id,
      borrower_id: loan.borrower_id
    )
  end

  describe '#al_loans' do
    it 'should not include same investor loans' do
      expect(concentration.al_loans).to include(loan)
    end

    it 'should not include other investor loans' do
      expect(concentration.al_loans).to_not include(other_investor_loan)
    end

    it 'should not include the candidate loan' do
      expect(concentration.al_loans).to_not include(unallocated_loan)
    end
  end

  describe '#calculate' do
    it 'should return the percentage of parameter borrowed by the current candidate\'s borrower' do
      test_value = (unallocated_loan.amount + loan_archive.principal_balance) * 100 / max_facility_amount
      expect(concentration.calculate).to eq([test_value.round(2), {}])
    end
  end

  describe '#prerequisite' do
    it 'should be truthy' do
      expect(concentration.prerequisite({})).to be_truthy
    end
  end

  describe '#supported_balance_types' do
    subject { concentration.supported_balance_types }
    it { should eq(%i[upb]) }
  end
end
