# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanAllocation::ConcentrationCalculator::MonthsAtBeyondLt do
  let(:investor) { create(:above_lending_investor, name: 'above_funding_trust') }
  let(:concentration) { create(:concentration, investor:, category: :months_at_beyond_lt, parameter: 6, balance_type: :upb) }
  let!(:three_month_loan) { create(:above_lending_loan, :purchased, investor:, product_type: 'IPL', originating_party: 'CRB', months_since_enrollment: 3) }
  let!(:twelve_month_loan) { create(:above_lending_loan, :purchased, investor:, product_type: 'UPL', originating_party: 'CRB', months_since_enrollment: 12) }

  describe '#filtered_loans' do
    let!(:loan3) { create(:above_lending_loan, :purchased, investor:, product_type: 'PPC', originating_party: 'CRB', months_since_enrollment: 6) }

    it 'should include all loan types' do
      expect(concentration.filtered_loans).to include(three_month_loan.loan_pro_loan_entity)
      expect(concentration.filtered_loans).not_to include(twelve_month_loan.loan_pro_loan_entity)
      expect(concentration.filtered_loans).not_to include(loan3.loan_pro_loan_entity)
    end
  end

  describe '#calculate' do
    subject { concentration.calculate }
    it 'should delegate to balance_percentage' do
      expect(concentration).to receive(:balance_percentage)
      subject
    end
  end

  describe '#prerequisite' do
    subject { concentration.prerequisite(months_at_beyond: beyond_months) }

    context 'when months at beyond is blank' do
      let(:beyond_months) { nil }
      it { should be_falsey }
    end

    context 'when months at beyond is less than parameter' do
      let(:beyond_months) { Faker::Number.number(digits: 2) }
      it { should be_falsey }
    end

    context 'when months at beyond is more than parameter' do
      let(:beyond_months) { [0, 1, 2].sample }
      it { should be_truthy }
    end
  end

  describe '#supported_balance_types' do
    subject { concentration.supported_balance_types }
    it { should eq(%i[upb ipb]) }
  end

  describe '#update_metadata' do
    it 'should update percentage metadata' do
      expect(concentration).to receive(:update_percentage_metadata)
      concentration.update_metadata({})
    end
  end
end
