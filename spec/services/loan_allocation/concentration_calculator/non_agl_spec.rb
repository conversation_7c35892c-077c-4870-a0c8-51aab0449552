# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanAllocation::ConcentrationCalculator::NonAgl do
  let(:investor) { create(:above_lending_investor, name: 'above_funding_trust') }
  let!(:crb) { create(:above_lending_investor, name: :crb) }
  let(:concentration) { create(:concentration, investor: investor, category: :non_agl, balance_type: :upb) }
  let!(:apl_loan) { create(:above_lending_loan, :purchased, investor: investor, product_type: 'IPL', originating_party: 'CRB') }
  let!(:non_agl_loan) { create(:above_lending_loan, :purchased, investor: investor, product_type: Faker::Superhero.name, originating_party: 'CRB') }
  let!(:unmapped_loan) { create(:loan_entity, :purchased, investor: non_agl_loan.loan_pro_loan_entity.investor, originator: non_agl_loan.loan_pro_loan_entity.originator) }
  let!(:other_investor_non_agl_loan) { create(:above_lending_loan, :purchased, product_type: Faker::Superhero.name, originating_party: 'CRB') }

  describe '#filtered_loans' do
    it 'should include non-AGL loan' do
      expect(concentration.filtered_loans).to include(non_agl_loan.loan_pro_loan_entity)
    end
    it 'should not include APL loan' do
      expect(concentration.filtered_loans).to_not include(apl_loan.loan_pro_loan_entity)
    end
    it 'should not include loan without correctly mapped AL loan' do
      expect(concentration.filtered_loans).to_not include(unmapped_loan)
    end
  end

  describe '#calculate' do
    let(:non_agl_sum) { Faker::Number.between(from: 2000, to: 20_000) }
    let(:all_sum) { Faker::Number.between(from: 30_000, to: 50_000) }
    let(:test_percentage) { non_agl_sum * 100 / all_sum }

    it 'should retrieve UPB sums for non-AGL and all loans for AFT, and calculate percentage' do
      expect(concentration).to receive(:upb_sum).with([non_agl_loan.loan_pro_loan_entity]).and_return(non_agl_sum)
      expect(concentration).to receive(:upb_sum)
        .with(array_including([non_agl_loan.loan_pro_loan_entity, apl_loan.loan_pro_loan_entity])).and_return(all_sum)
      expect(concentration.calculate).to eq([test_percentage, { non_agl_upb: non_agl_sum, total_upb: all_sum }])
    end
  end

  describe '#prerequisite' do
    subject { concentration.prerequisite(product: product) }
    context 'for IPL product' do
      let(:product) { 'IPL' }

      it { should be_falsey }
    end

    context 'for DM product' do
      let(:product) { 'DM' }

      it { should be_truthy }
    end

    context 'for UPL product' do
      let(:product) { 'UPL' }

      it { should be_truthy }
    end

    context 'for ACL product' do
      let(:product) { 'ACL' }

      it { should be_truthy }
    end
  end

  describe '#supported_balance_types' do
    subject { concentration.supported_balance_types }
    it { should eq(%i[upb ipb]) }
  end

  describe '#update_metadata' do
    it 'should update percentage metadata' do
      expect(concentration).to receive(:update_percentage_metadata)
      concentration.update_metadata({})
    end
  end
end
