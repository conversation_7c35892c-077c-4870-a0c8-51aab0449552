# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanAllocation::ConcentrationCalculator::OriginalTermGt72Lte84UpbGt do
  let(:investor) { create(:above_lending_investor, name: 'comvest') }
  let(:upb) { 500 }
  let(:balance_type) { 'upb' }
  let(:concentration) { create(:concentration, investor: investor, category: :original_term_gt_72_lte_84_upb_gt, balance_type: balance_type, parameter: upb) }
  let!(:loan1) { create(:above_lending_loan, :purchased, :with_selected_offer, investor: investor, term: 84) }
  let!(:loan2) { create(:above_lending_loan, :purchased, :with_selected_offer, investor: investor, term: 72) }
  let!(:loan3) { create(:above_lending_loan, :purchased, :with_selected_offer, investor: investor, term: 80) }
  let!(:archive1) { create(:loan_reverse_status_archive, loan_entity: loan1.loan_pro_loan_entity, principal_balance: upb + 1) }
  let!(:archive2) { create(:loan_reverse_status_archive, loan_entity: loan2.loan_pro_loan_entity, principal_balance: upb + 1) }
  let!(:archive3) { create(:loan_reverse_status_archive, loan_entity: loan3.loan_pro_loan_entity, principal_balance: upb) }

  describe '#filtered_loans' do
    it 'should include loan within term and UPB criteria' do
      expect(concentration.filtered_loans).to include(loan1.loan_pro_loan_entity)
    end

    it 'should not include loans outside of term and UPB criteria' do
      expect(concentration.filtered_loans).to_not include(loan2.loan_pro_loan_entity)
      expect(concentration.filtered_loans).to_not include(loan3.loan_pro_loan_entity)
    end
  end

  describe '#calculate' do
    it 'should correctly calculate the UPB scores' do
      numerator = archive1.principal_balance
      denominator = [archive1, archive2, archive3].map(&:principal_balance).sum
      (value, metadata) = concentration.calculate
      expect(value).to eq((numerator / denominator * 100.0).round(5))
      expect(metadata.values).to include(numerator)
      expect(metadata.values).to include(denominator)
    end
  end

  describe '#prerequisite' do
    it 'should be applicable for loans where the term is within the defined range and UPB is greater than the parameter' do
      expect(concentration.prerequisite({ term: 80, loan_amount: 600 })).to be_truthy
      expect(concentration.prerequisite({ term: 72, loan_amount: 600 })).to be(false)
      expect(concentration.prerequisite({ term: 85, loan_amount: 600 })).to be(false)
      expect(concentration.prerequisite({ term: 80, loan_amount: 500 })).to be(false)
    end
  end

  describe '#supported_balance_types' do
    subject { concentration.supported_balance_types }
    it { should eq(%i[upb]) }
  end

  describe '#update_metadata' do
    it 'should update percentage metadata' do
      expect(concentration).to receive(:update_percentage_metadata)
      concentration.update_metadata({})
    end
  end
end
