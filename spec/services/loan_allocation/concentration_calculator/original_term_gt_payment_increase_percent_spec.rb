# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanAllocation::ConcentrationCalculator::OriginalTermGtPaymentIncreasePercent do
  let(:investor) { create(:above_lending_investor, name: 'comvest') }
  let(:concentration) { create(:concentration, investor: investor, category: :original_term_gt_payment_increase_percent, balance_type: 'upb', parameter: '60,30') }
  let!(:loan1) { create(:above_lending_loan, :purchased, :with_selected_offer, investor: investor, term: 72) }
  let!(:loan2) { create(:above_lending_loan, :purchased, :with_selected_offer, investor: investor, term: 60) }
  let!(:loan3) { create(:above_lending_loan, :purchased, :with_selected_offer, investor: investor, term: 70) }
  let!(:loan4) { create(:above_lending_loan, :purchased, :with_selected_offer, product_type: 'UPL', investor: investor, term: 70) }
  let(:beyond_payment_amount) { Faker::Number.decimal(l_digits: 3) }

  before do
    loan1.loan_payment_detail.update!(beyond_payment_amount: beyond_payment_amount)
    loan1.selected_offer.update!(initial_term_payment: beyond_payment_amount * 1.31)
    loan2.loan_payment_detail.update!(beyond_payment_amount: beyond_payment_amount)
    loan2.selected_offer.update!(initial_term_payment: beyond_payment_amount * 1.31)
    loan3.loan_payment_detail.update!(beyond_payment_amount: beyond_payment_amount)
    loan3.selected_offer.update!(initial_term_payment: beyond_payment_amount * 1.30)
  end

  describe '#calculate' do
    before do
      create(:loan_reverse_status_archive, loan_entity: loan1.loan_pro_loan_entity)
      create(:loan_reverse_status_archive, loan_entity: loan2.loan_pro_loan_entity)
      create(:loan_reverse_status_archive, loan_entity: loan3.loan_pro_loan_entity)
      create(:loan_reverse_status_archive, loan_entity: loan4.loan_pro_loan_entity)
    end

    it 'should correctly calculate the UPB scores' do
      balances = [loan1, loan2, loan3, loan4].map { |loan| loan.loan_pro_loan_entity.loan_reverse_status_archive.principal_balance }
      numerator = balances[0]
      denominator = balances.sum

      (value, metadata) = concentration.calculate
      expect(value).to eq((numerator * 100.0 / denominator).round(5))
      expect(metadata[concentration.numerator_key]).to eq(numerator)
      expect(metadata[concentration.total_balance_key]).to eq(denominator)
    end
  end

  describe '#name' do
    subject { concentration.name }
    it { should eq('comvest-OriginalTermGt60PaymentIncreasePercentGt30.00-upb') }
  end

  describe '#numerator_key' do
    subject { concentration.numerator_key }
    it { should eq(:"original_term_gt_60_payment_increase_percent_gt_30.00") }
  end

  describe '#original_term_param' do
    subject { concentration.original_term_param }
    it { should eq(60) }
  end

  describe '#payment_increase_percent_param' do
    subject { concentration.payment_increase_percent_param }
    it { should eq(30.0) }
  end

  describe '#prerequisite' do
    it 'should be false for no matching term' do
      expect(concentration.prerequisite({ product: 'IPL', term: 60, payment_increase_percentage: 20 })).to eq(false)
      expect(concentration.prerequisite({ product: 'IPL', term: 60, payment_increase_percentage: 30 })).to eq(false)
      expect(concentration.prerequisite({ product: 'IPL', term: 60, payment_increase_percentage: 40 })).to eq(false)
    end

    it 'should be false for no matching payment increase percentage' do
      expect(concentration.prerequisite({ product: 'IPL', term: 70, payment_increase_percentage: 20 })).to eq(false)
      expect(concentration.prerequisite({ product: 'IPL', term: 70, payment_increase_percentage: 30 })).to eq(false)
    end

    it 'should be true for matching term and payment increase percentage' do
      expect(concentration.prerequisite({ product: 'IPL', term: 70, payment_increase_percentage: 40 })).to eq(true)
      expect(concentration.prerequisite({ product: 'UPL', term: 70, payment_increase_percentage: 40 })).to eq(true)
    end
  end

  describe '#filtered_loans' do
    it 'should include loans for matching term and payment increase percentage' do
      expect(concentration.filtered_loans).to include(loan1.loan_pro_loan_entity)
    end

    it 'should not include other loans' do
      expect(concentration.filtered_loans).to_not include(loan2.loan_pro_loan_entity)
      expect(concentration.filtered_loans).to_not include(loan3.loan_pro_loan_entity)
    end
  end

  describe '#supported_balance_types' do
    subject { concentration.supported_balance_types }
    it { should eq([:upb]) }
  end

  describe '#update_metadata' do
    let(:loan_details) { { balance: 100_00 } }
    let(:numerator_key) { :"original_term_gt_60_payment_increase_percent_gt_30.00" }
    let(:total_balance_key) { :total_upb }

    it 'calls update_percentage_metadata with correct arguments' do
      expect(concentration).to receive(:update_percentage_metadata).with(numerator_key, total_balance_key, loan_details)
      concentration.update_metadata(loan_details)
    end
  end
end
