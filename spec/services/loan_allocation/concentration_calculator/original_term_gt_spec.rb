# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanAllocation::ConcentrationCalculator::OriginalTermGt do
  let(:investor) { create(:above_lending_investor, name: ' above_funding_trust_2') }
  let(:term) { Faker::Number.number(digits: 2) }
  let(:concentration) { create(:concentration, investor: investor, category: :original_term_gt, balance_type: balance_type, parameter: term) }
  let(:balance_type) { 'upb' }
  let!(:loan1) { create(:above_lending_loan, :purchased, :with_selected_offer, investor: investor, term: term) }
  let!(:loan2) { create(:above_lending_loan, :purchased, :with_selected_offer, investor: investor, term: term + 1) }
  let!(:loan3) { create(:above_lending_loan, :purchased, :with_selected_offer, investor: investor, term: term, frequency: 'loan.frequency.semiMonthly') }

  describe '#filtered_loans' do
    it 'should include higher term loan and the loan with the term equal to param' do
      expect(concentration.filtered_loans).to include(loan2.loan_pro_loan_entity)
    end

    it 'should not include lesser term loan' do
      expect(concentration.filtered_loans).to_not include(loan3.loan_pro_loan_entity)
    end
  end

  describe '#calculate' do
    context 'when balance_type is UPB' do
      let(:balance_type) { 'upb' }
      let!(:archive1) { create(:loan_reverse_status_archive, loan_entity: loan1.loan_pro_loan_entity) }
      let!(:archive2) { create(:loan_reverse_status_archive, loan_entity: loan2.loan_pro_loan_entity) }
      let!(:archive3) { create(:loan_reverse_status_archive, loan_entity: loan3.loan_pro_loan_entity) }

      it 'should correctly calculate the UPB scores' do
        numerator = archive2.principal_balance
        denominator = [archive1, archive2, archive3].map(&:principal_balance).sum
        (value, metadata) = concentration.calculate
        expect(value).to eq((numerator / denominator * 100.0).round(5))
        expect(metadata.values).to include(numerator)
        expect(metadata.values).to include(denominator)
      end
    end

    context 'when balance_type is IPB' do
      let(:balance_type) { 'ipb' }

      it 'should calculate the original term for the investor, based on IPB' do
        numerator = loan2.selected_offer.amount
        denominator = [loan1, loan2, loan3].map(&:selected_offer).map(&:amount).sum
        (value, metadata) = concentration.calculate
        expect(value).to eq((numerator / denominator * 100.0).round(5))
        expect(metadata.values).to include(numerator)
        expect(metadata.values).to include(denominator)
      end
    end
  end

  describe '#prerequisite' do
    it 'should be applicable for loans where the original term is greater than the parameter' do
      expect(concentration.prerequisite({ term: term + 1 })).to be_truthy
      expect(concentration.prerequisite({ term: term })).to be(false)
      expect(concentration.prerequisite({ term: term - 1 })).to be(false)
    end
  end

  describe '#supported_balance_types' do
    subject { concentration.supported_balance_types }
    it { should eq(%i[upb ipb]) }
  end

  describe '#update_metadata' do
    it 'should update percentage metadata' do
      expect(concentration).to receive(:update_percentage_metadata)
      concentration.update_metadata({})
    end
  end
end
