# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanAllocation::ConcentrationCalculator::OverdueGte do
  let(:investor) { create(:above_lending_investor, name: 'above_funding_trust_2') }
  let(:days_past_due) { Faker::Number.between(from: 10, to: 100) }
  let(:balance_type) { 'upb' }
  let(:concentration) { create(:concentration, investor: investor, category: :overdue_gte, parameter: days_past_due, balance_type: balance_type) }
  let!(:loan1) { create(:above_lending_loan, :purchased, investor: investor) }
  let!(:loan2) { create(:above_lending_loan, :purchased, investor: investor) }
  let!(:loan_status1) { create(:loan_reverse_status_archive, loan_entity: loan1.loan_pro_loan_entity, days_past_due: days_past_due) }
  let!(:loan_status2) { create(:loan_reverse_status_archive, loan_entity: loan2.loan_pro_loan_entity, days_past_due: days_past_due - 1) }

  describe '#filtered_loans' do
    it 'should include gte days past due loan' do
      expect(concentration.filtered_loans).to include(loan1.loan_pro_loan_entity)
    end

    it 'should not include lower days past due loan' do
      expect(concentration.filtered_loans).to_not include(loan2.loan_pro_loan_entity)
    end
  end

  describe '#calculate' do
    context 'when balance_type is UPB' do
      it 'should calculate the overdue GTE for the investor, based on UPB' do
        numerator = loan1.loan_pro_loan_entity.loan_reverse_status_archive.principal_balance
        denominator = [loan1, loan2].map(&:loan_pro_loan_entity).map { |loan| loan.loan_reverse_status_archive.principal_balance }.sum
        (value, metadata) = concentration.calculate
        expect(value).to eq((numerator / denominator * 100.0).round(5))
        expect(metadata.values).to include(numerator)
        expect(metadata.values).to include(denominator)
      end
    end
  end

  describe '#prerequisite' do
    it 'should be applicable for loans where the days past due is higher than the parameter' do
      expect(concentration.prerequisite({ days_past_due: days_past_due + rand(1..100) })).to be_truthy
      expect(concentration.prerequisite({ days_past_due: days_past_due - rand(1..5) })).to be(false)
    end
  end

  describe '#supported_balance_types' do
    subject { concentration.supported_balance_types }
    it { should eq(%i[upb]) }
  end

  describe '#update_metadata' do
    it 'should update percentage metadata' do
      expect(concentration).to receive(:update_percentage_metadata)
      concentration.update_metadata({})
    end
  end
end
