# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanAllocation::ConcentrationCalculator::PaymentIncreasePercentGte do
  let(:investor) { create(:above_lending_investor, name: 'above_funding_trust') }
  let(:balance_type) { 'upb' }
  let(:concentration) { create(:concentration, investor: investor, category: :payment_increase_percent_gte, balance_type:, parameter: 30) }
  let!(:loan1) { create(:above_lending_loan, :purchased, :with_selected_offer, investor: investor) }
  let!(:loan2) { create(:above_lending_loan, :purchased, :with_selected_offer, product_type: 'UPL', investor: investor) }
  let!(:loan3) { create(:above_lending_loan, :purchased, :with_selected_offer, investor: investor) }
  let(:beyond_payment_amount) { Faker::Number.decimal(l_digits: 3) }

  before do
    allow(Flipper).to receive(:enabled?).with(:new_non_ipl_payment_increase_percentage).and_return(true)
    loan1.loan_payment_detail.update!(beyond_payment_amount: beyond_payment_amount)
    loan1.selected_offer.update!(initial_term_payment: beyond_payment_amount * 1.35)
    loan2.loan_payment_detail.update!(beyond_payment_amount: beyond_payment_amount)
    loan2.selected_offer.update!(initial_term_payment: beyond_payment_amount * 1.30)
    loan3.loan_payment_detail.update!(beyond_payment_amount: beyond_payment_amount)
    loan3.selected_offer.update!(initial_term_payment: beyond_payment_amount * 1.25)
  end

  describe 'when balance type is upb' do
    let(:balance_type) { 'upb' }

    context '#filtered_loans' do
      it 'should include higher payment increase percentage loans greater than the parameter' do
        expect(concentration.filtered_loans).to include(loan1.loan_pro_loan_entity)
      end

      it 'should not include the equal to and less than payment increase percentage loans' do
        expect(concentration.filtered_loans).not_to include(loan2.loan_pro_loan_entity)
        expect(concentration.filtered_loans).not_to include(loan3.loan_pro_loan_entity)
      end
    end

    context '#update_metadata' do
      it 'should update percentage metadata' do
        expect(concentration).to receive(:update_percentage_metadata)
        concentration.update_metadata({})
      end
    end
  end

  describe 'when balance type is ipb' do
    let(:balance_type) { 'ipb' }

    context '#filtered_loans' do
      it 'should include higher payment increase percentage loans greater than the parameter' do
        expect(concentration.filtered_loans).to include(loan1.loan_pro_loan_entity)
      end

      it 'should not include the equal to and less than payment increase percentage loans' do
        expect(concentration.filtered_loans).not_to include(loan2.loan_pro_loan_entity)
        expect(concentration.filtered_loans).not_to include(loan3.loan_pro_loan_entity)
      end
    end

    context '#update_metadata' do
      it 'should update percentage metadata' do
        expect(concentration).to receive(:update_percentage_metadata)
        concentration.update_metadata({})
      end
    end
  end

  describe '#supported_balance_types' do
    subject { concentration.supported_balance_types }
    it { should eq(%i[upb ipb]) }
  end

  describe '#calculate' do
    context 'when balance_type is UPB' do
      before do
        create(:loan_reverse_status_archive, loan_entity: loan1.loan_pro_loan_entity)
        create(:loan_reverse_status_archive, loan_entity: loan2.loan_pro_loan_entity)
        create(:loan_reverse_status_archive, loan_entity: loan3.loan_pro_loan_entity)
      end

      it 'should calculate based on UPB' do
        balances = [loan1, loan2, loan3].map { |loan| loan.loan_pro_loan_entity.loan_reverse_status_archive.principal_balance }
        numerator = balances[0]
        denominator = balances.sum

        (value, metadata) = concentration.calculate
        expect(value).to eq((numerator / denominator * 100.0).round(5))
        expect(metadata.values).to include(numerator)
        expect(metadata.values).to include(denominator)
      end
    end

    context 'when balance_type is IPB' do
      let(:balance_type) { 'ipb' }

      it 'should calculate based on IPB' do
        balances = [loan1, loan2, loan3].map { |loan| loan.selected_offer.amount }
        numerator = balances[0]
        denominator = balances.sum

        (value, metadata) = concentration.calculate
        expect(value).to eq((numerator / denominator * 100.0).round(5))
        expect(metadata.values).to include(numerator)
        expect(metadata.values).to include(denominator)
      end
    end
  end

  describe '#prerequisite' do
    it 'should be true for matching payment increase percentage' do
      expect(concentration.prerequisite({ payment_increase_percentage: 30 })).to eq(true)
      expect(concentration.prerequisite({ payment_increase_percentage: 31 })).to eq(true)
    end

    it 'should be false for no matching payment increase percentage' do
      expect(concentration.prerequisite({ payment_increase_percentage: 29 })).to eq(false)
    end
  end
end
