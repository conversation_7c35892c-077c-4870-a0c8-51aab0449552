# frozen_string_literal: true

require 'rails_helper'
RSpec.describe LoanAllocation::ConcentrationCalculator::ResidualIncome do
  let(:investor) { create(:above_lending_investor) }
  let!(:parameter) { 1000 }
  let(:upb) { 500 }

  let(:concentration) { create(:concentration, investor: investor, category: :residual_income, parameter: parameter) }
  let!(:loan1) { create(:above_lending_loan, :purchased, investor: investor, anual_income: 24_000) }
  let!(:loan2) { create(:above_lending_loan, :purchased, investor: investor, anual_income: 24_000) }

  let!(:til_history1) { create(:til_history, loan_id: loan1.id, signed_at: Time.zone.now, til_data: { itemization: { rawAmountFinanced: 100 } }) }
  let!(:til_history2) { create(:til_history, loan_id: loan2.id, signed_at: Time.zone.now, til_data: { itemization: { rawAmountFinanced: 200 } }) }

  let!(:archive1) { create(:loan_reverse_status_archive, loan_entity: loan1.loan_pro_loan_entity, principal_balance: upb) }
  let!(:archive2) { create(:loan_reverse_status_archive, loan_entity: loan2.loan_pro_loan_entity, principal_balance: upb) }

  before do
    # Each loan can have at most one GDS Decsion Engine Payload. We must destroy the existing one before
    # creating a new one and then reload the loan to populate the new record for this association.

    loan1.loan_payment_detail.update!(beyond_payment_frequency: 'monthly', beyond_payment_amount: 100)
    allow(GdsApi::DecisionEngineOutputReport).to receive(:fetch).with(loan1.request_id).and_return(build(:gds_api_decision_engine_output_report, monthly_debt_amount: 100))
    allow(GdsApi::DecisionEngineOutputReport).to receive(:fetch).with(loan2.request_id).and_return(build(:gds_api_decision_engine_output_report, monthly_debt_amount: 200))
  end

  describe '#filtered_loans' do
    context 'with monthly payment frequency' do
      it 'should include loans with higher residual income' do
        expect(concentration.filtered_loans).to include(loan1.loan_pro_loan_entity)
      end

      it 'should not include loans with lower residual income' do
        expect(concentration.filtered_loans).to_not include(loan2.loan_pro_loan_entity)
      end
    end

    context 'with semi-monthly payment frequency' do
      before do
        loan1.loan_pro_loan_entity.loan_setup_entity.update!(payment_frequency: 'loan.frequency.semiMonthly')
        loan2.loan_pro_loan_entity.loan_setup_entity.update!(payment_frequency: 'loan.frequency.semiMonthly')

        til_history1.update!(til_data: { itemization: { rawAmountFinanced: 50 } })
        til_history2.update!(til_data: { itemization: { rawAmountFinanced: 100 } })
      end

      it 'should include loans with higher residual income' do
        expect(concentration.filtered_loans).to include(loan1.loan_pro_loan_entity)
      end

      it 'should not include loans with lower residual income' do
        expect(concentration.filtered_loans).to_not include(loan2.loan_pro_loan_entity)
      end
    end

    context 'with bi-weekly payment frequency' do
      before do
        loan1.loan_pro_loan_entity.loan_setup_entity.update!(payment_frequency: 'loan.frequency.biWeekly')
        loan2.loan_pro_loan_entity.loan_setup_entity.update!(payment_frequency: 'loan.frequency.biWeekly')

        til_history1.update!(til_data: { itemization: { rawAmountFinanced: 46.15 } })
        til_history2.update!(til_data: { itemization: { rawAmountFinanced: 92.30 } })
      end

      it 'should include loans with higher residual income' do
        expect(concentration.filtered_loans).to include(loan1.loan_pro_loan_entity)
      end

      it 'should not include loans with lower residual income' do
        expect(concentration.filtered_loans).to_not include(loan2.loan_pro_loan_entity)
      end
    end
  end

  describe '#supported_balance_types' do
    subject { concentration.supported_balance_types }
    it { should eq(%i[upb]) }
  end

  describe '#calculate' do
    it 'should calculate residual income' do
      numerator = archive1.principal_balance
      denominator = [archive1, archive2].map(&:principal_balance).sum
      (value, metadata) = concentration.calculate
      expect(value).to eq((numerator / denominator * 100.0).round(2))
      expect(metadata.values).to include(numerator)
      expect(metadata.values).to include(denominator)
    end
  end

  describe '#update_metadata' do
    it 'should update percentage metadata' do
      expect(concentration).to receive(:update_percentage_metadata)
      concentration.update_metadata({})
    end
  end
end
