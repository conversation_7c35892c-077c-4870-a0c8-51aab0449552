# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanAllocation::ConcentrationCalculator::SameStateNonCa do
  let(:investor) { create(:above_lending_investor, name: 'comvest') }
  let(:concentration) { create(:concentration, investor: investor, category: :same_state_non_ca, balance_type: :upb) }
  let!(:il_loan) { create(:above_lending_loan, :purchased, investor: investor, borrower_additional_info: create(:borrower_additional_info, state: 'IL'), amount: 10_000) }
  let!(:il_loan2) { create(:above_lending_loan, :purchased, investor: investor, borrower_additional_info: create(:borrower_additional_info, state: 'IL'), amount: 50_000) }
  let!(:ca_loan) { create(:above_lending_loan, :purchased, investor: investor, borrower_additional_info: create(:borrower_additional_info, state: 'CA'), amount: 60_000) }
  let!(:tx_loan) { create(:above_lending_loan, :purchased, investor: investor, borrower_additional_info: create(:borrower_additional_info, state: 'TX'), amount: 20_000) }
  let!(:az_loan) { create(:above_lending_loan, :purchased, investor: investor, borrower_additional_info: create(:borrower_additional_info, state: 'AZ'), amount: 30_000) }
  let!(:il_offer) { create(:above_lending_offer, loan: il_loan) }
  let!(:il_offer2) { create(:above_lending_offer, loan: il_loan2) }
  let!(:tx_offer) { create(:above_lending_offer, loan: tx_loan) }
  let!(:az_offer) { create(:above_lending_offer, loan: az_loan) }
  let!(:ca_offer) { create(:above_lending_offer, loan: ca_loan) }
  let!(:il_lp_loan) { create(:loan_reverse_status_archive, principal_balance: il_loan.amount, loan_id: il_loan.loan_pro_loan_entity.id) }
  let!(:il_lp_loan2) { create(:loan_reverse_status_archive, principal_balance: il_loan2.amount, loan_id: il_loan2.loan_pro_loan_entity.id) }
  let!(:tx_lp_loan) { create(:loan_reverse_status_archive, principal_balance: tx_loan.amount, loan_id: tx_loan.loan_pro_loan_entity.id) }
  let!(:az_lp_loan) { create(:loan_reverse_status_archive, principal_balance: az_loan.amount, loan_id: az_loan.loan_pro_loan_entity.id) }
  let!(:ca_lp_loan) { create(:loan_reverse_status_archive, principal_balance: ca_loan.amount, loan_id: ca_loan.loan_pro_loan_entity.id) }

  describe '#calculate' do
    context 'when the balance_type is upb' do
      it 'should retrieve UPB sums for all states and calculate the percentage for a given state' do
        il_upb_sum = concentration.upb_sum([il_loan.loan_pro_loan_entity, il_loan2.loan_pro_loan_entity])
        tx_upb_sum = concentration.upb_sum([tx_loan.loan_pro_loan_entity])
        az_upb_sum = concentration.upb_sum([az_loan.loan_pro_loan_entity])
        ca_upb_sum = concentration.upb_sum([ca_loan.loan_pro_loan_entity])
        total_upb = concentration.upb_sum(
          [il_loan.loan_pro_loan_entity,
           il_loan2.loan_pro_loan_entity,
           tx_loan.loan_pro_loan_entity,
           az_loan.loan_pro_loan_entity,
           ca_loan.loan_pro_loan_entity]
        )

        il = il_loan.borrower_additional_info.state
        tx = tx_loan.borrower_additional_info.state
        az = az_loan.borrower_additional_info.state
        ca = ca_loan.borrower_additional_info.state
        il_value = (il_upb_sum * 100 / total_upb).round(2)

        expected_output = [
          il_value.to_d,
          {
            il => il_upb_sum,
            tx => tx_upb_sum,
            az => az_upb_sum,
            ca => ca_upb_sum
          }
        ]

        expect(concentration.calculate_with_loan_details(state: il)).to eq(expected_output)
      end

      it 'should retrieve UPB sums for all states and have a value of zero when there is no @loan_details' do
        il_upb_sum = concentration.upb_sum([il_loan.loan_pro_loan_entity, il_loan2.loan_pro_loan_entity])
        tx_upb_sum = concentration.upb_sum([tx_loan.loan_pro_loan_entity])
        az_upb_sum = concentration.upb_sum([az_loan.loan_pro_loan_entity])
        ca_upb_sum = concentration.upb_sum([ca_loan.loan_pro_loan_entity])

        il = il_loan.borrower_additional_info.state
        tx = tx_loan.borrower_additional_info.state
        az = az_loan.borrower_additional_info.state
        ca = ca_loan.borrower_additional_info.state

        expected_output = [
          0,
          {
            il => il_upb_sum,
            tx => tx_upb_sum,
            az => az_upb_sum,
            ca => ca_upb_sum
          }
        ]

        expect(concentration.calculate).to eq(expected_output)
      end
    end
  end

  describe '#prerequisite' do
    subject { concentration.prerequisite(state: state) }

    context 'when loan state is CA' do
      let(:state) { 'CA' }
      it { should be_falsey }
    end

    context 'when loan state is non CA' do
      let(:state) { 'AZ' }
      it { should be_truthy }
    end
  end

  describe '#supported_balance_types' do
    subject { concentration.supported_balance_types }
    it { should eq(%i[upb]) }
  end

  describe '#update_metadata' do
    it 'should update state metadata' do
      expect(concentration).to receive(:update_state_metadata)
      concentration.update_metadata({})
    end
  end
end
