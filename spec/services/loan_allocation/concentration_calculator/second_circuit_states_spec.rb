# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanAllocation::ConcentrationCalculator::SecondCircuitStates do
  let(:investor) { create(:above_lending_investor, name: 'above_funding_trust_2') }
  let(:concentration) { create(:concentration, investor: investor, category: :second_circuit_states, balance_type: balance_type) }
  let!(:ny_loan) { create(:above_lending_loan, :purchased, investor: investor, borrower_additional_info: create(:borrower_additional_info, state: 'NY')) }
  let!(:ct_loan) { create(:above_lending_loan, :purchased, investor: investor, borrower_additional_info: create(:borrower_additional_info, state: 'CT')) }
  let!(:vt_loan) { create(:above_lending_loan, :purchased, investor: investor, borrower_additional_info: create(:borrower_additional_info, state: 'VT')) }
  let!(:az_loan) { create(:above_lending_loan, :purchased, investor: investor, borrower_additional_info: create(:borrower_additional_info, state: 'AZ')) }
  let!(:ca_loan) { create(:above_lending_loan, :purchased, investor: investor, borrower_additional_info: create(:borrower_additional_info, state: 'CA')) }
  let!(:in_loan) { create(:above_lending_loan, :purchased, investor: investor, borrower_additional_info: create(:borrower_additional_info, state: 'IN')) }
  let!(:ny_lp_loan) { create(:loan_reverse_status_archive, principal_balance: Faker::Number.number(digits: 5), loan_id: ny_loan.loan_pro_loan_entity.id) }
  let!(:ct_lp_loan2) { create(:loan_reverse_status_archive, principal_balance: Faker::Number.number(digits: 5), loan_id: ct_loan.loan_pro_loan_entity.id) }
  let!(:vt_lp_loan) { create(:loan_reverse_status_archive, principal_balance: Faker::Number.number(digits: 5), loan_id: vt_loan.loan_pro_loan_entity.id) }
  let!(:az_lp_loan) { create(:loan_reverse_status_archive, principal_balance: Faker::Number.number(digits: 5), loan_id: az_loan.loan_pro_loan_entity.id) }
  let!(:ca_lp_loan) { create(:loan_reverse_status_archive, principal_balance: Faker::Number.number(digits: 3), loan_id: ca_loan.loan_pro_loan_entity.id) }
  let!(:in_lp_loan) { create(:loan_reverse_status_archive, principal_balance: Faker::Number.number(digits: 3), loan_id: in_loan.loan_pro_loan_entity.id) }
  let!(:offer1) { create(:above_lending_offer, loan: ny_loan) }
  let!(:offer2) { create(:above_lending_offer, loan: ct_loan) }
  let!(:offer3) { create(:above_lending_offer, loan: vt_loan) }

  let(:initial_principle_balance) { offer1.amount + offer2.amount + offer3.amount }
  let(:unpaid_principle_balance) { concentration.filtered_loans.map { |loan| loan.loan_reverse_status_archive.principal_balance }.sum }

  let(:balance_type) { 'upb' }

  let(:all_loans) do
    [ny_loan.loan_pro_loan_entity,
     ct_loan.loan_pro_loan_entity,
     vt_loan.loan_pro_loan_entity,
     az_loan.loan_pro_loan_entity,
     ca_loan.loan_pro_loan_entity,
     in_loan.loan_pro_loan_entity]
  end

  let(:second_circuit_loans) do
    [ny_loan.loan_pro_loan_entity,
     ct_loan.loan_pro_loan_entity,
     vt_loan.loan_pro_loan_entity]
  end

  before do
    allow(concentration).to receive(:loans).and_return(all_loans)
  end

  describe '#loans_for_concentration' do
    it 'returns only loans from NY, CT, and VT' do
      expect(concentration.filtered_loans.size).to eq(3)
    end
  end

  describe '#calculate' do
    it 'calculates second circuit percentage' do
      expect(concentration).to receive(:balance_percentage).and_call_original
      (value,) = concentration.calculate
      second_circuit_upb = second_circuit_loans.map(&:loan_reverse_status_archive).map(&:principal_balance).sum
      total_upb = all_loans.map(&:loan_reverse_status_archive).map(&:principal_balance).sum
      test_value = (second_circuit_upb * 100 / total_upb).round(5)
      expect(value).to eq(test_value)
    end
  end

  describe '#base_loan_query' do
    it 'is called from #calculate' do
      expect(concentration.base_loan_query).not_to be(nil)
    end
  end

  describe '#supported_balance_types' do
    subject { concentration.supported_balance_types }
    it { should eq(%i[upb ipb]) }
  end

  describe '#prerequisite' do
    subject { concentration.prerequisite(state: state) }
    context 'when loan is a part of second circuit states' do
      %w[NY CT VT].each do |code|
        let(:state) { code }
        it { should be_truthy }
      end
    end
    context 'when loan is not a part of second circuit states' do
      let(:state) { 'CA' }
      it { should be_falsey }
    end
  end

  describe '#update_metadata' do
    it 'should update state metadata' do
      expect(concentration).to receive(:update_percentage_metadata)
      concentration.update_metadata({})
    end
  end
end
