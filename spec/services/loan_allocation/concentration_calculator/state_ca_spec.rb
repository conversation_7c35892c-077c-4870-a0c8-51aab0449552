# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanAllocation::ConcentrationCalculator::StateCa do
  let(:investor) { create(:above_lending_investor, name: 'comvest') }
  let(:concentration) { create(:concentration, investor: investor, category: :state_ca, balance_type: :upb) }
  let!(:ca_loans) { create_list(:above_lending_loan, 4, :purchased, product_type: 'DM', investor: investor, borrower_additional_info: create(:borrower_additional_info, state: 'CA')) }
  let!(:il_loan) { create(:above_lending_loan, :purchased, product_type: 'DM', investor: investor, borrower_additional_info: create(:borrower_additional_info, state: 'IL')) }
  let!(:ca_other_investor_loan) { create(:above_lending_loan, :purchased, borrower_additional_info: create(:borrower_additional_info, state: 'CA')) }
  let!(:ca_non_ipl_loan) { create(:above_lending_loan, :purchased, product_type: 'IPL', investor: investor, borrower_additional_info: create(:borrower_additional_info, state: 'CA')) }

  before do
    # assures that different product types are covered
    ca_loans << ca_non_ipl_loan
  end

  describe '#filtered_loans' do
    it 'should include CA loans' do
      expect(concentration.filtered_loans).to match_array(ca_loans.collect(&:loan_pro_loan_entity))
    end
    it 'should include non IPL CA loan' do
      expect(concentration.filtered_loans).to include(ca_non_ipl_loan.loan_pro_loan_entity)
    end
    it 'should not include IL loan' do
      expect(concentration.filtered_loans).to_not include(il_loan.loan_pro_loan_entity)
    end
    it 'should not include loan with other investor' do
      expect(concentration.filtered_loans).to_not include(ca_other_investor_loan.loan_pro_loan_entity)
    end
  end

  describe '#calculate' do
    let(:state_ca_sum) { Faker::Number.between(from: 2000, to: 20_000) }
    let(:all_sum) { Faker::Number.between(from: 30_000, to: 50_000) }
    let(:test_percentage) { (state_ca_sum * 100) / all_sum }

    it 'should retrieve UPB sums for non-ASL and all loans for AFT, and calculate percentage' do
      expect(concentration).to receive(:upb_sum).with(ca_loans.collect(&:loan_pro_loan_entity)).and_return(state_ca_sum)
      expect(concentration).to receive(:upb_sum)
        .with(array_including([il_loan.loan_pro_loan_entity, ca_non_ipl_loan.loan_pro_loan_entity])).and_return(all_sum)
      expect(concentration.calculate).to eq([test_percentage.round(2), { state_ca_upb: state_ca_sum, total_upb: all_sum }])
    end
  end

  describe '#prerequisite' do
    context 'when loan matches CA state and DM product' do
      it 'returns true' do
        expect(concentration.prerequisite({ state: 'CA', product: 'DM' })).to eq(true)
      end
    end

    context 'when loan state is not in CA' do
      it 'returns false' do
        expect(concentration.prerequisite({ state: 'TX', product: 'DM' })).to eq(false)
      end
    end
  end
  describe '#update_metadata' do
    it 'should update percentage metadata' do
      expect(concentration).to receive(:update_percentage_metadata)
      concentration.update_metadata({})
    end
  end

  describe '#supported_balance_types' do
    subject { concentration.supported_balance_types }
    it { should eq(%i[upb]) }
  end
end
