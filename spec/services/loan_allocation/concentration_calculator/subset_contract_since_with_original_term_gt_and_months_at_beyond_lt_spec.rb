# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanAllocation::ConcentrationCalculator::SubsetContractSinceWithPaymentIncreasePercentGt do
  let(:investor) { create(:above_lending_investor, name: 'comvest') }
  let(:base_date) { Date.new(2020, 1, 1) }
  let(:invalid_date) { base_date - 1.week }
  let(:valid_date) { base_date + 1.week }
  let(:parameter) { "#{base_date.iso8601},60,10" }
  let(:concentration) { create(:concentration, investor:, category: :subset_contract_since_with_original_term_gt_and_months_at_beyond_lt, balance_type: 'upb', parameter:) }
  let!(:loan1) { create(:above_lending_loan, :purchased, :with_selected_offer, investor:, term: 72, months_since_enrollment: 8, contract_date: base_date) }
  let!(:loan2) { create(:above_lending_loan, :purchased, :with_selected_offer, investor:, term: 60, months_since_enrollment: 10, contract_date: invalid_date) }
  let!(:loan3) { create(:above_lending_loan, :purchased, :with_selected_offer, investor:, term: 70, months_since_enrollment: 10, contract_date: valid_date) }
  let!(:loan4) { create(:above_lending_loan, :purchased, :with_selected_offer, investor:, term: 70, months_since_enrollment: 8, contract_date: valid_date, product_type: 'UPL') }

  describe '#calculate' do
    before do
      create(:loan_reverse_status_archive, loan_entity: loan1.loan_pro_loan_entity)
      create(:loan_reverse_status_archive, loan_entity: loan3.loan_pro_loan_entity)
      create(:loan_reverse_status_archive, loan_entity: loan4.loan_pro_loan_entity)
    end

    it 'should correctly calculate the UPB scores' do
      balances = [loan1, loan3, loan4].map { |loan| loan.loan_pro_loan_entity.loan_reverse_status_archive.principal_balance }
      numerator = balances[0]
      denominator = balances.sum

      (value, metadata) = concentration.calculate
      expect(value).to eq((numerator * 100.0 / denominator).round(5))
      expect(metadata[concentration.numerator_key]).to eq(numerator)
      expect(metadata[concentration.total_balance_key]).to eq(denominator)
    end
  end

  describe '#name' do
    subject { concentration.name }
    it { should eq('comvest-SubsetContractSince-2020-01-01-withOriginalTermGt-60-andMonthsAtBeyondLt-10-upb') }
  end

  describe '#numerator_key' do
    subject { concentration.numerator_key }
    it { should eq(:"contract_since_2020-01-01_original_term_gt_60_and_months_at_beyond_lt_10") }
  end

  describe '#total_balance_key' do
    subject { concentration.total_balance_key }
    it { should eq(:"contract_since_2020-01-01") }
  end

  describe '#date_param' do
    subject { concentration.date_param }
    it { should eq(base_date) }
  end

  describe '#original_term_param' do
    subject { concentration.original_term_param }
    it { should eq(60) }
  end

  describe '#months_at_beyond_param' do
    subject { concentration.months_at_beyond_param }
    it { should eq(10) }
  end

  describe '#weight' do
    it 'is 1 for the loan #1' do
      expect(concentration.weight(loan1.loan_pro_loan_entity)).to eq(1.0)
    end

    it 'is 1 for the loan #2' do
      expect(concentration.weight(loan2.loan_pro_loan_entity)).to eq(0.0)
    end

    it 'is 0 for the loan #3' do
      expect(concentration.weight(loan3.loan_pro_loan_entity)).to eq(0.0)
    end

    it 'is 0 for the loan #4' do
      expect(concentration.weight(loan4.loan_pro_loan_entity)).to eq(0)
    end
  end

  describe '#candidate_weight' do
    it 'should return 0 for no matching term or product' do
      expect(concentration.candidate_weight({ product: 'UPL', contract_date: invalid_date, term: 60, months_at_beyond: 10 })).to eq(0)
      expect(concentration.candidate_weight({ product: 'UPL', contract_date: valid_date, term: 70, months_at_beyond: 8 })).to eq(0)

      expect(concentration.candidate_weight({ product: 'DM', contract_date: valid_date, term: 70, months_at_beyond: 8 })).to eq(0)
      expect(concentration.candidate_weight({ product: 'PPC', contract_date: valid_date, term: 70, months_at_beyond: 8 })).to eq(0)

      expect(concentration.candidate_weight({ product: 'IPL', contract_date: invalid_date, term: 60, months_at_beyond: 10 })).to eq(0)
      expect(concentration.candidate_weight({ product: 'IPL', contract_date: invalid_date, term: 70, months_at_beyond: 8 })).to eq(0)
    end

    it 'should return 0 for smaller payment increase percentage' do
      expect(concentration.candidate_weight({ product: 'IPL', contract_date: valid_date, term: 60, months_at_beyond: 8 })).to eq(0.0)
      expect(concentration.candidate_weight({ product: 'IPL', contract_date: valid_date, term: 70, months_at_beyond: 10 })).to eq(0.0)
    end

    it 'should return 1 for larger payment increase percentage' do
      expect(concentration.candidate_weight({ product: 'IPL', contract_date: valid_date, term: 70, months_at_beyond: 8 })).to eq(1.0)
    end
  end

  describe '#prerequisite' do
    it 'should be false for no matching term' do
      expect(concentration.prerequisite({ product: 'IPL', contract_date: invalid_date, term: 60, months_at_beyond: 10 })).to eq(false)
      expect(concentration.prerequisite({ product: 'UPL', contract_date: invalid_date, term: 70, months_at_beyond: 8 })).to eq(false)
    end

    it 'should be true for matching product and term, regardless of payment increase percentage' do
      expect(concentration.prerequisite({ product: 'IPL', contract_date: valid_date, term: 60, months_at_beyond: 10 })).to eq(true)
      expect(concentration.prerequisite({ product: 'UPL', contract_date: valid_date, term: 70, months_at_beyond: 8 })).to eq(true)
    end
  end

  describe '#supported_balance_types' do
    subject { concentration.supported_balance_types }
    it { should eq(%i[upb]) }
  end

  describe '#loans' do
    let!(:loan5) { create(:above_lending_loan, :purchased, :with_selected_offer, investor:, product_type: 'UPL', contract_date: valid_date) }
    let!(:loan6) { create(:above_lending_loan, :purchased, :with_selected_offer, investor:, product_type: 'DM', contract_date: invalid_date) }
    let!(:loan7) { create(:above_lending_loan, :purchased, :with_selected_offer, investor:, product_type: 'PPC', contract_date: invalid_date) }

    it 'should include loan within term and product criteria' do
      expect(concentration.loans).to include(loan1.loan_pro_loan_entity)
      expect(concentration.loans).to include(loan3.loan_pro_loan_entity)
      expect(concentration.loans).to include(loan4.loan_pro_loan_entity)
      expect(concentration.loans).to include(loan5.loan_pro_loan_entity)
    end

    it 'should not include loans outside of term and product criteria' do
      expect(concentration.loans).to_not include(loan2.loan_pro_loan_entity)
      expect(concentration.loans).to_not include(loan6.loan_pro_loan_entity)
      expect(concentration.loans).to_not include(loan7.loan_pro_loan_entity)
    end
  end
end
