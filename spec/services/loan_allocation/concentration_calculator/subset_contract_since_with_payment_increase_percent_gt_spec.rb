# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanAllocation::ConcentrationCalculator::SubsetContractSinceWithPaymentIncreasePercentGt do
  let(:investor) { create(:above_lending_investor, name: 'comvest') }
  let(:base_date) { Date.new(2020, 1, 1) }
  let(:invalid_date) { base_date - 1.week }
  let(:valid_date) { base_date + 1.week }
  let(:parameter) { "#{base_date.iso8601},30" }
  let(:concentration) { create(:concentration, investor: investor, category: :subset_contract_since_with_payment_increase_percent_gt, balance_type: 'upb', parameter:) }
  let!(:loan1) { create(:above_lending_loan, :purchased, :with_selected_offer, investor: investor, contract_date: base_date) }
  let!(:loan2) { create(:above_lending_loan, :purchased, :with_selected_offer, investor: investor, contract_date: invalid_date) }
  let!(:loan3) { create(:above_lending_loan, :purchased, :with_selected_offer, investor: investor, contract_date: valid_date) }
  let!(:loan4) { create(:above_lending_loan, :purchased, :with_selected_offer, product_type: 'UPL', investor: investor, contract_date: valid_date) }
  let(:beyond_payment_amount) { Faker::Number.decimal(l_digits: 3) }

  before do
    loan1.loan_payment_detail.update!(beyond_payment_amount: beyond_payment_amount)
    loan1.selected_offer.update!(initial_term_payment: beyond_payment_amount * 1.31)
    loan2.loan_payment_detail.update!(beyond_payment_amount: beyond_payment_amount)
    loan2.selected_offer.update!(initial_term_payment: beyond_payment_amount * 1.31)
    loan3.loan_payment_detail.update!(beyond_payment_amount: beyond_payment_amount)
    loan3.selected_offer.update!(initial_term_payment: beyond_payment_amount * 1.30)
  end

  describe '#calculate' do
    before do
      create(:loan_reverse_status_archive, loan_entity: loan1.loan_pro_loan_entity)
      create(:loan_reverse_status_archive, loan_entity: loan3.loan_pro_loan_entity)
      create(:loan_reverse_status_archive, loan_entity: loan4.loan_pro_loan_entity)
    end

    it 'should correctly calculate the UPB scores' do
      balances = [loan1, loan3, loan4].map { |loan| loan.loan_pro_loan_entity.loan_reverse_status_archive.principal_balance }
      numerator = balances[0]
      denominator = balances.sum

      (value, metadata) = concentration.calculate
      expect(value).to eq((numerator * 100.0 / denominator).round(5))
      expect(metadata[concentration.numerator_key]).to eq(numerator)
      expect(metadata[concentration.total_balance_key]).to eq(denominator)
    end
  end

  describe '#name' do
    subject { concentration.name }
    it { should eq('comvest-SubsetContractSince-2020-01-01-withPaymentIncreasePercentGt-30.00-upb') }
  end

  describe '#numerator_key' do
    subject { concentration.numerator_key }
    it { should eq(:"contract_since_2020-01-01_payment_increase_percent_gt_30.00") }
  end

  describe '#total_balance_key' do
    subject { concentration.total_balance_key }
    it { should eq(:"contract_since_2020-01-01") }
  end

  describe '#date_param' do
    subject { concentration.date_param }
    it { should eq(base_date) }
  end

  describe '#payment_increase_percent_param' do
    subject { concentration.payment_increase_percent_param }
    it { should eq(30.0) }
  end

  describe '#weight' do
    it 'is 1 for the loan #1' do
      expect(concentration.weight(loan1.loan_pro_loan_entity)).to eq(1.0)
    end

    it 'is 1 for the loan #2' do
      expect(concentration.weight(loan2.loan_pro_loan_entity)).to eq(1.0)
    end

    it 'is 0 for the loan #3' do
      expect(concentration.weight(loan3.loan_pro_loan_entity)).to eq(0.0)
    end

    it 'is 0 for the loan #4' do
      expect(concentration.weight(loan4.loan_pro_loan_entity)).to eq(0)
    end
  end

  describe '#candidate_weight' do
    it 'should return 0 for no matching term or product' do
      expect(concentration.candidate_weight({ product: 'UPL', contract_date: invalid_date, payment_increase_percentage: 40 })).to eq(0)
      expect(concentration.candidate_weight({ product: 'UPL', contract_date: valid_date, payment_increase_percentage: 40 })).to eq(0)

      expect(concentration.candidate_weight({ product: 'DM', contract_date: valid_date, payment_increase_percentage: 40 })).to eq(0)
      expect(concentration.candidate_weight({ product: 'PPC', contract_date: valid_date, payment_increase_percentage: 40 })).to eq(0)

      expect(concentration.candidate_weight({ product: 'IPL', contract_date: invalid_date, payment_increase_percentage: 20 })).to eq(0)
      expect(concentration.candidate_weight({ product: 'IPL', contract_date: invalid_date, payment_increase_percentage: 30 })).to eq(0)
      expect(concentration.candidate_weight({ product: 'IPL', contract_date: invalid_date, payment_increase_percentage: 40 })).to eq(0)
    end

    it 'should return 0 for smaller payment increase percentage' do
      expect(concentration.candidate_weight({ product: 'IPL', contract_date: valid_date, payment_increase_percentage: 20 })).to eq(0.0)
      expect(concentration.candidate_weight({ product: 'IPL', contract_date: valid_date, payment_increase_percentage: 30 })).to eq(0.0)
    end

    it 'should return 1 for larger payment increase percentage' do
      expect(concentration.candidate_weight({ product: 'IPL', contract_date: valid_date, payment_increase_percentage: 40 })).to eq(1.0)
    end
  end

  describe '#prerequisite' do
    it 'should be false for no matching term' do
      expect(concentration.prerequisite({ product: 'IPL', contract_date: invalid_date, payment_increase_percentage: 20 })).to eq(false)
      expect(concentration.prerequisite({ product: 'IPL', contract_date: invalid_date, payment_increase_percentage: 30 })).to eq(false)
      expect(concentration.prerequisite({ product: 'UPL', contract_date: invalid_date, payment_increase_percentage: 40 })).to eq(false)
    end

    it 'should be true for matching product and term, regardless of payment increase percentage' do
      expect(concentration.prerequisite({ product: 'IPL', contract_date: valid_date, payment_increase_percentage: 20 })).to eq(true)
      expect(concentration.prerequisite({ product: 'IPL', contract_date: valid_date, payment_increase_percentage: 30 })).to eq(true)
      expect(concentration.prerequisite({ product: 'UPL', contract_date: valid_date, payment_increase_percentage: 40 })).to eq(true)
    end
  end

  describe '#supported_balance_types' do
    subject { concentration.supported_balance_types }
    it { should eq(%i[upb]) }
  end

  describe '#loans' do
    let!(:loan5) { create(:above_lending_loan, :purchased, :with_selected_offer, product_type: 'DM', investor: investor, contract_date: invalid_date) }
    let!(:loan6) { create(:above_lending_loan, :purchased, :with_selected_offer, product_type: 'PPC', investor: investor, contract_date: invalid_date) }

    it 'should include loan within term and product criteria' do
      expect(concentration.loans).to include(loan1.loan_pro_loan_entity)
      expect(concentration.loans).to include(loan3.loan_pro_loan_entity)
      expect(concentration.loans).to include(loan4.loan_pro_loan_entity)
    end

    it 'should not include loans outside of term and product criteria' do
      expect(concentration.loans).to_not include(loan2.loan_pro_loan_entity)
      expect(concentration.loans).to_not include(loan5.loan_pro_loan_entity)
      expect(concentration.loans).to_not include(loan6.loan_pro_loan_entity)
    end
  end
end
