# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanAllocation::ConcentrationCalculator::SubsetOriginalTermWithPaymentIncreasePercentGte do
  let(:investor) { create(:above_lending_investor, name: 'comvest') }
  let(:concentration) { create(:concentration, investor: investor, category: :subset_original_term_with_payment_increase_percent_gte, balance_type: 'upb', parameter: '60,30') }
  let!(:loan1) { create(:above_lending_loan, :purchased, :with_selected_offer, investor: investor, term: 72) }
  let!(:loan2) { create(:above_lending_loan, :purchased, :with_selected_offer, investor: investor, term: 60) }
  let!(:loan3) { create(:above_lending_loan, :purchased, :with_selected_offer, investor: investor, term: 70) }
  let!(:loan4) { create(:above_lending_loan, :purchased, :with_selected_offer, product_type: 'UPL', investor: investor, term: 70) }
  let(:beyond_payment_amount) { Faker::Number.decimal(l_digits: 3) }

  before do
    allow(Flipper).to receive(:enabled?).with(:new_non_ipl_payment_increase_percentage).and_return(true)
    loan1.loan_payment_detail.update!(beyond_payment_amount: beyond_payment_amount)
    loan1.selected_offer.update!(initial_term_payment: beyond_payment_amount * 1.35)
    loan2.loan_payment_detail.update!(beyond_payment_amount: beyond_payment_amount)
    loan2.selected_offer.update!(initial_term_payment: beyond_payment_amount * 1.30)
    loan3.loan_payment_detail.update!(beyond_payment_amount: beyond_payment_amount)
    loan3.selected_offer.update!(initial_term_payment: beyond_payment_amount * 1.25)
  end

  describe '#calculate' do
    before do
      create(:loan_reverse_status_archive, loan_entity: loan1.loan_pro_loan_entity)
      create(:loan_reverse_status_archive, loan_entity: loan3.loan_pro_loan_entity)
      create(:loan_reverse_status_archive, loan_entity: loan4.loan_pro_loan_entity)
    end

    it 'should correctly calculate the UPB scores' do
      balances = [loan1, loan3, loan4].map { |loan| loan.loan_pro_loan_entity.loan_reverse_status_archive.principal_balance }
      numerator = balances[0]
      denominator = balances.sum

      (value, metadata) = concentration.calculate
      expect(value).to eq((numerator * 100.0 / denominator).round(5))
      expect(metadata[concentration.numerator_key]).to eq(numerator)
      expect(metadata[concentration.total_balance_key]).to eq(denominator)
    end
  end

  describe '#name' do
    subject { concentration.name }
    it { should eq('comvest-SubsetOriginalTermGt-60-withPaymentIncreasePercentGte-30.00-upb') }
  end

  describe '#numerator_key' do
    subject { concentration.numerator_key }
    it { should eq(:"original_term_gt_60_payment_increase_percent_gte_30.00") }
  end

  describe '#total_balance_key' do
    subject { concentration.total_balance_key }
    it { should eq(:original_term_gt_60) }
  end

  describe '#original_term_param' do
    subject { concentration.original_term_param }
    it { should eq(60) }
  end

  describe '#payment_increase_percent_param' do
    subject { concentration.payment_increase_percent_param }
    it { should eq(30.0) }
  end

  describe '#weight' do
    it 'is 1 for the loan #1' do
      expect(concentration.weight(loan1.loan_pro_loan_entity)).to eq(1.0)
    end

    it 'is 1 for the loan #2' do
      allow(loan2.loan_pro_loan_entity.above_lending_loan).to receive(:payment_increase_percentage).and_return(30)
      expect(concentration.weight(loan2.loan_pro_loan_entity)).to eq(1.0)
    end

    it 'is 0 for the loan #3' do
      expect(concentration.weight(loan3.loan_pro_loan_entity)).to eq(0.0)
    end

    it 'is 0 for the loan #4' do
      expect(concentration.weight(loan4.loan_pro_loan_entity)).to eq(0.0)
    end
  end

  describe '#candidate_weight' do
    it 'should return nil for no matching term' do
      expect(concentration.candidate_weight({ term: 60, payment_increase_percentage: 40 })).to be_nil
    end

    it 'should return 0 for smaller payment increase percentage' do
      expect(concentration.candidate_weight({ term: 70, payment_increase_percentage: 20 })).to eq(0.0)
    end

    it 'should return 1 for larger payment increase percentage' do
      expect(concentration.candidate_weight({ term: 70, payment_increase_percentage: 30 })).to eq(1.0)
      expect(concentration.candidate_weight({ term: 70, payment_increase_percentage: 40 })).to eq(1.0)
    end
  end

  describe '#prerequisite' do
    it 'should be false for no matching term' do
      expect(concentration.prerequisite({ term: 60, payment_increase_percentage: 20 })).to eq(false)
      expect(concentration.prerequisite({ term: 60, payment_increase_percentage: 30 })).to eq(false)
      expect(concentration.prerequisite({ term: 60, payment_increase_percentage: 40 })).to eq(false)
    end

    it 'should be true for matching term, regardless of payment increase percentage' do
      expect(concentration.prerequisite({ term: nil, payment_increase_percentage: 20 })).to eq(true)
      expect(concentration.prerequisite({ term: 70, payment_increase_percentage: 20 })).to eq(true)
      expect(concentration.prerequisite({ term: 70, payment_increase_percentage: 30 })).to eq(true)
      expect(concentration.prerequisite({ term: 70, payment_increase_percentage: 40 })).to eq(true)
    end
  end

  describe '#supported_balance_types' do
    subject { concentration.supported_balance_types }
    it { should eq(%i[upb]) }
  end

  describe '#loans' do
    it 'should include loan within term and product criteria' do
      expect(concentration.loans).to include(loan1.loan_pro_loan_entity)
      expect(concentration.loans).to include(loan3.loan_pro_loan_entity)
      expect(concentration.loans).to include(loan4.loan_pro_loan_entity)
    end

    it 'should not include loans outside of term and product criteria' do
      expect(concentration.loans).not_to include(loan2.loan_pro_loan_entity)
    end
  end
end
