# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanAllocation::ConcentrationCalculator::Top1State do
  let(:investor) { create(:above_lending_investor, name: 'above_funding_trust') }
  let(:concentration) { create(:concentration, investor: investor, category: :top_1_state, balance_type: :upb) }
  let!(:il_loan) { create(:above_lending_loan, :purchased, investor: investor, borrower_additional_info: create(:borrower_additional_info, state: 'IL'), amount: 10_000) }
  let!(:il_loan2) { create(:above_lending_loan, :purchased, investor: investor, borrower_additional_info: create(:borrower_additional_info, state: 'IL'), amount: 50_000) }
  let!(:tx_loan) { create(:above_lending_loan, :purchased, investor: investor, borrower_additional_info: create(:borrower_additional_info, state: 'TX'), amount: 20_000) }
  let!(:az_loan) { create(:above_lending_loan, :purchased, investor: investor, borrower_additional_info: create(:borrower_additional_info, state: 'AZ'), amount: 30_000) }
  let!(:il_offer) { create(:above_lending_offer, loan: il_loan) }
  let!(:il_offer2) { create(:above_lending_offer, loan: il_loan2) }
  let!(:tx_offer) { create(:above_lending_offer, loan: tx_loan) }
  let!(:az_offer) { create(:above_lending_offer, loan: az_loan) }
  let!(:il_lp_loan) { create(:loan_reverse_status_archive, principal_balance: il_loan.amount, loan_id: il_loan.loan_pro_loan_entity.id) }
  let!(:il_lp_loan2) { create(:loan_reverse_status_archive, principal_balance: il_loan2.amount, loan_id: il_loan2.loan_pro_loan_entity.id) }
  let!(:tx_lp_loan) { create(:loan_reverse_status_archive, principal_balance: tx_loan.amount, loan_id: tx_loan.loan_pro_loan_entity.id) }
  let!(:az_lp_loan) { create(:loan_reverse_status_archive, principal_balance: az_loan.amount, loan_id: az_loan.loan_pro_loan_entity.id) }

  describe '#calculate' do
    context 'when the balance_type is upb' do
      it 'should retrieve UPB sums for the top 1 state and all loans for AFT, and calculate percentage' do
        il_upb_sum = [il_lp_loan, il_lp_loan2].map(&:principal_balance).sum
        tx_upb_sum = tx_lp_loan.principal_balance
        az_upb_sum = az_lp_loan.principal_balance
        total_upb = il_upb_sum + tx_upb_sum + az_upb_sum
        value = (il_upb_sum * 100 / total_upb).round(2)
        il = il_loan.borrower_additional_info.state
        tx = tx_loan.borrower_additional_info.state
        az = az_loan.borrower_additional_info.state

        expect(concentration.calculate).to eq([value.to_d, { il.to_s => il_upb_sum, az.to_s => az_upb_sum, tx.to_s => tx_upb_sum }])
      end
    end

    context 'when the balance_type is ipb' do
      let(:concentration) { create(:concentration, investor: investor, category: :top_1_state, balance_type: :ipb) }
      it 'should retrieve IPB sums for the top 1 state and all loans for AFT, and calculate percentage' do
        il_ipb_sum = [il_loan, il_loan2].map(&:selected_offer).map(&:amount).sum
        tx_ipb_sum = tx_loan.selected_offer.amount
        az_ipb_sum = az_loan.selected_offer.amount
        total_ipb = [il_loan, il_loan2, tx_loan, az_loan].map(&:selected_offer).map(&:amount).sum
        value = (il_ipb_sum * 100 / total_ipb).round(2)
        expect(concentration.calculate).to eq([value.to_d, { 'IL' => il_ipb_sum, 'AZ' => az_ipb_sum, 'TX' => tx_ipb_sum }])
      end
    end
  end

  describe '#prerequisite' do
    subject { concentration.prerequisite(state: state) }

    context 'when loan state is in top 1 state' do
      let(:state) { 'IL' }
      it { should be_truthy }
    end

    context 'when loan state is not in top 1 state' do
      let(:state) { 'AZ' }
      it { should be_falsey }
    end
  end

  describe '#supported_balance_types' do
    subject { concentration.supported_balance_types }
    it { should eq(%i[upb ipb]) }
  end

  describe '#update_metadata' do
    it 'should update state metadata' do
      expect(concentration).to receive(:update_state_metadata)
      concentration.update_metadata({})
    end
  end
end
