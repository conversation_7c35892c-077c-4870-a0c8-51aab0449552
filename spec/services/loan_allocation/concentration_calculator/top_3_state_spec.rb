# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanAllocation::ConcentrationCalculator::Top3State do
  let(:investor) { create(:above_lending_investor, name: 'above_funding_trust') }
  let(:concentration) { create(:concentration, investor: investor, category: :top_3_state, balance_type: :upb) }
  let!(:il_loan) { create(:above_lending_loan, :purchased, investor: investor, borrower_additional_info: create(:borrower_additional_info, state: 'IL')) }
  let!(:il_loan2) { create(:above_lending_loan, :purchased, investor: investor, borrower_additional_info: create(:borrower_additional_info, state: 'IL')) }
  let!(:tx_loan) { create(:above_lending_loan, :purchased, investor: investor, borrower_additional_info: create(:borrower_additional_info, state: 'TX')) }
  let!(:az_loan) { create(:above_lending_loan, :purchased, investor: investor, borrower_additional_info: create(:borrower_additional_info, state: 'AZ')) }
  let!(:ca_loan) { create(:above_lending_loan, :purchased, investor: investor, borrower_additional_info: create(:borrower_additional_info, state: 'CA')) }
  let!(:ca_loan) { create(:above_lending_loan, :purchased, investor: investor, borrower_additional_info: create(:borrower_additional_info, state: 'CA')) }
  let!(:in_loan) { create(:above_lending_loan, :purchased, investor: investor, borrower_additional_info: create(:borrower_additional_info, state: 'IN')) }
  let!(:il_lp_loan) { create(:loan_reverse_status_archive, principal_balance: Faker::Number.number(digits: 5), loan_id: il_loan.loan_pro_loan_entity.id) }
  let!(:il_lp_loan2) { create(:loan_reverse_status_archive, principal_balance: Faker::Number.number(digits: 5), loan_id: il_loan2.loan_pro_loan_entity.id) }
  let!(:tx_lp_loan) { create(:loan_reverse_status_archive, principal_balance: Faker::Number.number(digits: 5), loan_id: tx_loan.loan_pro_loan_entity.id) }
  let!(:az_lp_loan) { create(:loan_reverse_status_archive, principal_balance: Faker::Number.number(digits: 5), loan_id: az_loan.loan_pro_loan_entity.id) }
  let!(:ca_lp_loan) { create(:loan_reverse_status_archive, principal_balance: Faker::Number.number(digits: 3), loan_id: ca_loan.loan_pro_loan_entity.id) }
  let!(:in_lp_loan) { create(:loan_reverse_status_archive, principal_balance: Faker::Number.number(digits: 3), loan_id: in_loan.loan_pro_loan_entity.id) }

  let(:included_loans) do
    [il_loan.loan_pro_loan_entity.id,
     il_loan2.loan_pro_loan_entity.id,
     tx_loan.loan_pro_loan_entity.id,
     az_loan.loan_pro_loan_entity.id]
  end

  let(:all_loans) do
    [il_loan.loan_pro_loan_entity.id,
     il_loan2.loan_pro_loan_entity.id,
     tx_loan.loan_pro_loan_entity.id,
     az_loan.loan_pro_loan_entity.id,
     ca_loan.loan_pro_loan_entity.id,
     in_loan.loan_pro_loan_entity.id]
  end

  describe '#calculate' do
    it 'should retrieve UPB sums for the top 1 state and all loans for AFT, and calculate percentage' do
      il_upb_sum = [il_lp_loan, il_lp_loan2].map(&:principal_balance).sum
      tx_upb_sum = tx_lp_loan.principal_balance
      az_upb_sum = az_lp_loan.principal_balance
      ca_upb_sum = ca_lp_loan.principal_balance
      in_upb_sum = in_lp_loan.principal_balance
      top_3_upb = il_upb_sum + tx_upb_sum + az_upb_sum
      total_upb = top_3_upb + ca_upb_sum + in_upb_sum
      value = (top_3_upb * 100 / total_upb).round(2)
      il = il_loan.borrower_additional_info.state
      tx = tx_loan.borrower_additional_info.state
      az = az_loan.borrower_additional_info.state
      ca = ca_loan.borrower_additional_info.state
      in_ = in_loan.borrower_additional_info.state

      expect(concentration.calculate[0]).to be_within(0.01).of(value)
      expect(concentration.calculate[1]).to eq({ il.to_s => il_upb_sum, az.to_s => az_upb_sum, tx.to_s => tx_upb_sum, ca.to_s => ca_upb_sum, in_.to_s => in_upb_sum })
    end
  end

  describe '#prerequisite' do
    subject { concentration.prerequisite(state: state) }

    context 'when loan state in top 3 states' do
      it 'returns true' do
        %w[IL TX AZ].each do |included_state|
          expect(concentration.prerequisite({ state: included_state })).to eq(true)
        end
      end
    end

    context 'when loan state is not in top 3 states' do
      it 'returns false' do
        %w[IN CA].each do |excluded_state|
          expect(concentration.prerequisite({ state: excluded_state })).to eq(false)
        end
      end
    end
  end

  describe '#supported_balance_types' do
    subject { concentration.supported_balance_types }
    it { should eq(%i[upb ipb]) }
  end

  describe '#update_metadata' do
    it 'should update state metadata' do
      expect(concentration).to receive(:update_state_metadata)
      concentration.update_metadata({})
    end
  end
end
