# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanAllocation::ConcentrationCalculator::UpbGt do
  let(:investor) { create(:above_lending_investor, name: 'comvest') }
  let(:parameter_gt) { Faker::Number.number(digits: 5) }
  let(:concentration) { create(:concentration, investor: investor, category: :upb_gt, parameter: parameter_gt, balance_type: :upb) }
  let!(:upb_gt_loan1) { create(:above_lending_loan, :purchased, investor: investor, amount: Faker::Number.number(digits: 6)) }
  let!(:upb_lt_loan1) { create(:above_lending_loan, :purchased, investor: investor, amount: Faker::Number.number(digits: 2)) }
  let!(:upb_gt_loan1_reverse_archive) { create(:loan_reverse_status_archive, principal_balance: upb_gt_loan1.amount, loan_id: upb_gt_loan1.loan_pro_loan_entity.id) }
  let!(:upb_lt_loan1_reverse_archive) { create(:loan_reverse_status_archive, principal_balance: upb_lt_loan1.amount, loan_id: upb_lt_loan1.loan_pro_loan_entity.id) }

  describe '#filtered_loans' do
    it 'should include loans with a upb greater than parameter' do
      expect(concentration.filtered_loans).to match_array(upb_gt_loan1.loan_pro_loan_entity)
    end

    it 'should not include loans with a upb less than parameter' do
      expect(concentration.filtered_loans).to_not include(upb_lt_loan1.loan_pro_loan_entity)
    end
  end

  describe '#calculate' do
    subject { concentration.calculate }
    it 'should delegate to balance_percentage' do
      expect(concentration).to receive(:balance_percentage)

      subject
    end

    it 'should calculate upb sum for loans with a upb greater than parameter' do
      numerator = [upb_gt_loan1].map(&:loan_pro_loan_entity).map { |loan| loan.loan_reverse_status_archive.principal_balance }.sum
      denominator = [upb_gt_loan1_reverse_archive, upb_lt_loan1_reverse_archive].map(&:principal_balance).sum
      (value, metadata) = concentration.calculate

      expect(value).to eq((numerator * 100 / denominator).round(5))
      expect(metadata.values).to include(numerator)
      expect(metadata.values).to include(denominator)
    end
  end

  describe '#prerequisite' do
    it 'should be applicable for loans with a upb greater than the parameter' do
      expect(concentration.prerequisite({ loan_amount: parameter_gt + 1 })).to be_truthy
    end

    it 'should not be applicable for loans with a upb less than the parameter' do
      expect(concentration.prerequisite({ loan_amount: parameter_gt - 1 })).to be_falsey
    end
  end

  describe '#supported_balance_types' do
    subject { concentration.supported_balance_types }
    it { should eq(%i[upb]) }
  end

  describe '#update_metadata' do
    let(:loan_details) { create_unallocated_loan_record }

    it 'should update metadata' do
      expect(concentration).to receive(:update_percentage_metadata)
      concentration.update_metadata({})
      expect(concentration.numerator_key).to eq("upb_gt_#{parameter_gt}_#{concentration.balance_type}".to_sym)
    end
  end
end
