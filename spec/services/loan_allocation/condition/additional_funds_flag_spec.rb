# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanAllocation::Condition::AdditionalFundsFlag do
  let(:engine_runner) { LoanAllocation::EngineRunner.new(loans: loan_details) }
  let(:loan_details) { create_loan_details(loan_amount: 500, state: 'CA', product: 'APL', term: 30, originator: 'CRB', additional_funds_flag: '1') }

  context 'for eq operator' do
    let!(:allocation_rule) do
      create :loan_allocation_rule, :comvest, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :additional_funds_flag, operator: :eq, threshold_value: '1')
      ]
    end

    it 'matches when the additional funds flag == the threshold_value additional funds flag' do
      expect(engine_runner.run['1']).to eq(allocation_rule.investor.name)
    end
  end

  context 'for ne operator' do
    let!(:allocation_rule) do
      create :loan_allocation_rule, :crb, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :additional_funds_flag, operator: :ne, threshold_value: '0')
      ]
    end

    it 'matches when the loan additional funds flag != the threshold_value additional funds flag' do
      expect(engine_runner.run['1']).to eq(allocation_rule.investor.name)
    end
  end
end
