# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanAllocation::Condition::BankruptcyLast3Years do
  let(:engine_runner) { LoanAllocation::EngineRunner.new(loans: loan_details) }
  let(:loan_details) { create_loan_details(product: 'APL', bankruptcy_filed_date: 3.years.ago, originator: 'CRB') }

  context 'for greater than equal operator' do
    let!(:crb_rule) do
      create :loan_allocation_rule, :crb, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :bankruptcy_last_3_years, operator: :gte, threshold_value: 2)
      ]
    end

    it 'matches when the bankruptcy filed date is greater than to the threshold_value amount' do
      expect(engine_runner.run['1']).to eq(crb_rule.investor.name)
    end

    context 'equal to' do
      let(:loan_details) { create_loan_details(loan_amount: 500, state: 'CA', product: 'APL', bankruptcy_filed_date: 3.years.ago, originator: 'CRB') }

      it 'matches when the bankruptcy filed date is equal to the threshold_value amount' do
        expect(engine_runner.run['1']).to eq(crb_rule.investor.name)
      end
    end

    context 'does not match' do
      let(:loan_details) { create_loan_details(loan_amount: 500, state: 'CA', product: 'APL', bankruptcy_filed_date: 1.years.ago, originator: 'CRB') }

      it 'does not match when the banruptcy filed date is less than the threshold_value amount' do
        expect(engine_runner.run['1']).not_to eq(crb_rule.investor.name)
      end
    end
  end
end
