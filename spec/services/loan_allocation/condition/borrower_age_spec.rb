# frozen_string_literal: true

require 'rails_helper'
require 'loan_allocation/condition/borrower_age'

RSpec.describe LoanAllocation::Condition::BorrowerAge do
  let(:engine_runner) { LoanAllocation::EngineRunner.new(loans: loan_details) }
  let(:loan_details) { create_loan_details(loan_amount: 500, state: 'CA', product: 'APL', borrower_age: 27, originator: 'CRB') }

  context 'for greater than equal operator' do
    let!(:crb_rule) do
      create :loan_allocation_rule, :crb, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :borrower_age, operator: :gte, threshold_value: 18)
      ]
    end

    it 'matches when the borrowers age is greater than to the threshold_value amount' do
      expect(engine_runner.run['1']).to eq(crb_rule.investor.name)
    end

    context 'equal to' do
      let(:loan_details) { create_loan_details(loan_amount: 500, state: 'CA', product: 'APL', borrower_age: 18, originator: 'CRB') }

      it 'matches when the borrowers age is equal to the threshold_value amount' do
        expect(engine_runner.run['1']).to eq(crb_rule.investor.name)
      end
    end

    context 'does not match' do
      let(:loan_details) { create_loan_details(loan_amount: 500, state: 'CA', product: 'APL', borrower_age: 17, originator: 'CRB') }

      it 'does not match when the borrowers age is less than the threshold_value amount' do
        expect(engine_runner.run['1']).not_to eq(crb_rule.investor.name)
      end
    end
  end
end
