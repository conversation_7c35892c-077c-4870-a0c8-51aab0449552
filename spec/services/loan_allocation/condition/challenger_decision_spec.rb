# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanAllocation::Condition::ChallengerDecision do
  let(:engine_runner) { LoanAllocation::EngineRunner.new(loans: loan_details) }
  let(:challenger_decision) { 'OFFERED' }
  let(:loan_details) { create_loan_details(challenger_decision:) }

  context 'for eq operator for OFFERED challenger' do
    let!(:allocation_rule) do
      create :loan_allocation_rule, :comvest, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :challenger_decision, operator: :eq, threshold_value: 'OFFERED')
      ]
    end

    it 'matches when the challenger Offered Decision == the threshold_value level' do
      expect(engine_runner.run['1']).to eq(allocation_rule.investor.name)
    end
  end

  context 'for ne operator for decision challenger' do
    let!(:allocation_rule) do
      create :loan_allocation_rule, :comvest, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :challenger_decision, operator: :ne, threshold_value: 'FRONT_END_DECLINED')
      ]
    end

    it 'matches when the loan credit model level != the threshold_value credit model level' do
      expect(engine_runner.run['1']).to eq(allocation_rule.investor.name)
    end
  end
end
