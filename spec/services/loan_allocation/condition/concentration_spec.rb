# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanAllocation::Condition::Concentration do
  let(:engine_runner) { LoanAllocation::EngineRunner.new(loans: loan_details) }
  let(:loan_details) { create_loan_details(loan_amount: rand(2500..10_000), state: 'CA', product: 'APL', term: rand(12..84), originator: 'CRB') }

  let(:concentration) { create(:concentration, :comvest, category: :custom, parameter: "foobar_#{Faker::Number.between(**mid_range).round(2)}") }
  let(:mid_range) { { from: 10.00, to: 90.00 } }
  let(:low_range) { { from: 0.01, to: 9.99 } }
  let(:high_range) { { from: 90.01, to: 99.99 } }
  let(:threshold) { Faker::Number.between(**threshold_range).round(2) }

  let(:allocation_rule) do
    create :loan_allocation_rule, :comvest, category: :allocation, loan_allocation_conditions: [
      build(:loan_allocation_condition, parameter: :concentration, concentration: concentration, operator: operator, threshold_value: threshold)
    ]
  end

  context 'for less operator' do
    let(:operator) { :less }
    context 'when concentration percentage < condition\'s threshold value' do
      let(:threshold_range) { high_range }
      before { allocation_rule }
      it 'matches and allocates the loan' do
        expect(engine_runner.run['1']).to eq(allocation_rule.investor.name)
      end
    end

    context 'when concentration percentage > condition\'s threshold value' do
      let(:threshold_range) { low_range }
      before { allocation_rule }
      it 'does not match the condition' do
        expect(engine_runner.run['1']).to be_nil
      end
    end
  end

  context 'for lte operator' do
    let(:operator) { :lte }
    context 'when concentration percentage < condition\'s threshold value' do
      let(:threshold_range) { high_range }
      before { allocation_rule }
      it 'matches and allocates the loan' do
        expect(engine_runner.run['1']).to eq(allocation_rule.investor.name)
      end
    end

    context 'when concentration percentage = condition\'s threshold value' do
      let(:threshold) { concentration.value }
      before { allocation_rule }
      it 'matches and allocates the loan' do
        expect(engine_runner.run['1']).to eq(allocation_rule.investor.name)
      end
    end

    context 'when concentration percentage > condition\'s threshold value' do
      let(:threshold_range) { low_range }
      before { allocation_rule }
      it 'does not match the condition' do
        expect(engine_runner.run['1']).to be_nil
      end
    end
  end

  context 'for greater operator' do
    let(:operator) { :greater }
    context 'when concentration percentage > condition\'s threshold value' do
      let(:threshold_range) { low_range }
      before { allocation_rule }
      it 'matches and allocates the loan' do
        expect(engine_runner.run['1']).to eq(allocation_rule.investor.name)
      end
    end

    context 'when concentration percentage < condition\'s threshold value' do
      let(:threshold_range) { high_range }
      before { allocation_rule }
      it 'does not match the condition' do
        expect(engine_runner.run['1']).to be_nil
      end
    end
  end

  context 'for gte operator' do
    let(:operator) { :gte }
    context 'when concentration percentage > condition\'s threshold value' do
      let(:threshold_range) { low_range }
      before { allocation_rule }
      it 'matches and allocates the loan' do
        expect(engine_runner.run['1']).to eq(allocation_rule.investor.name)
      end
    end

    context 'when concentration percentage = condition\'s threshold value' do
      let(:threshold) { concentration.value }
      before { allocation_rule }
      it 'matches and allocates the loan' do
        expect(engine_runner.run['1']).to eq(allocation_rule.investor.name)
      end
    end

    context 'when concentration percentage < condition\'s threshold value' do
      let(:threshold_range) { high_range }
      before { allocation_rule }
      it 'does not match the condition' do
        expect(engine_runner.run['1']).to be_nil
      end
    end
  end

  context 'for eq operator' do
    let(:operator) { :eq }
    context 'when concentration percentage = condition\'s threshold value' do
      let(:threshold) { concentration.value }
      before { allocation_rule }
      it 'matches and allocates the loan' do
        expect(engine_runner.run['1']).to eq(allocation_rule.investor.name)
      end
    end

    context 'when concentration percentage != condition\'s threshold value' do
      let(:threshold_range) { high_range }
      before { allocation_rule }
      it 'does not match the condition' do
        expect(engine_runner.run['1']).to be_nil
      end
    end
  end

  context 'for ne operator' do
    let(:operator) { :ne }
    context 'when concentration percentage != condition\'s threshold value' do
      let(:threshold_range) { high_range }
      before { allocation_rule }
      it 'matches and allocates the loan' do
        expect(engine_runner.run['1']).to eq(allocation_rule.investor.name)
      end
    end

    context 'when concentration percentage = condition\'s threshold value' do
      let(:threshold) { concentration.value }
      before { allocation_rule }
      it 'does not match the condition' do
        expect(engine_runner.run['1']).to be_nil
      end
    end
  end

  context 'when a loan is allocated' do
    let(:operator) { :less }
    let(:threshold_range) { high_range }
    before { allocation_rule }

    it 'should update the concentration metadata' do
      allow_any_instance_of(LoanAllocationCondition).to receive(:concentration).and_return(concentration)
      expect(concentration).to receive(:update_metadata).twice.and_call_original
      engine_runner.run
    end
  end
end
