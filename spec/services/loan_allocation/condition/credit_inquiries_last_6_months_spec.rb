# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanAllocation::Condition::CreditInquiriesLast6Months do
  let(:engine_runner) { LoanAllocation::EngineRunner.new(loans: loan_details) }
  let(:loan_details) { create_loan_details(loan_amount: 500, state: 'CA', product: 'APL', term: 30, originator: 'CRB', credit_inquiries_last_6_months: 1) }

  context 'for eq operator' do
    let!(:allocation_rule) do
      create :loan_allocation_rule, :crb, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :credit_inquiries_last_6_months, operator: :eq, threshold_value: 1)
      ]
    end

    it 'matches when the loan credit inquiries in last 6 months == the threshold_value amount' do
      expect(engine_runner.run['1']).to eq('crb')
    end
  end

  context 'for ne operator' do
    let!(:allocation_rule) do
      create :loan_allocation_rule, :crb, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :credit_inquiries_last_6_months, operator: :ne, threshold_value: 3)
      ]
    end

    it 'matches when the loan credit inquiries in last 6 months != the threshold_value amount' do
      expect(engine_runner.run['1']).to eq('crb')
    end
  end

  context 'for less operator' do
    let!(:allocation_rule) do
      create :loan_allocation_rule, :crb, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :credit_inquiries_last_6_months, operator: :less, threshold_value: 2)
      ]
    end

    it 'matches when the loan credit inquiries in last 6 months < the threshold_value amount' do
      expect(engine_runner.run['1']).to eq('crb')
    end
  end

  context 'for greater operator' do
    let!(:allocation_rule) do
      create :loan_allocation_rule, :crb, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :credit_inquiries_last_6_months, operator: :greater, threshold_value: 0)
      ]
    end

    it 'matches when the loan credit inquiries in last 6 months > the threshold_value amount' do
      expect(engine_runner.run['1']).to eq('crb')
    end
  end
end
