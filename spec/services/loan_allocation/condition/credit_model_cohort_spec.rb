# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanAllocation::Condition::CreditModelCohort do
  let(:engine_runner) { LoanAllocation::EngineRunner.new(loans: loan_details) }
  let(:credit_model_cohort) { 'champion' }
  let(:loan_details) { create_loan_details(credit_model_cohort:) }

  context 'for eq operator for champion/challenger' do
    let!(:allocation_rule) do
      create :loan_allocation_rule, :comvest, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :credit_model_cohort, operator: :eq, threshold_value: 'champion')
      ]
    end

    it 'matches when the loan cohort == the threshold_value cohort' do
      expect(engine_runner.run['1']).to eq(allocation_rule.investor.name)
    end
  end

  context 'for ne operator for champion/challenger' do
    let!(:allocation_rule) do
      create :loan_allocation_rule, :comvest, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :credit_model_cohort, operator: :ne, threshold_value: 'challenger')
      ]
    end

    it 'matches when the loan cohort != the threshold_value cohort' do
      expect(engine_runner.run['1']).to eq(allocation_rule.investor.name)
    end
  end
end
