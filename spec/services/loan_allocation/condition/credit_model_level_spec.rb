# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanAllocation::Condition::CreditModelLevel do
  let(:engine_runner) { LoanAllocation::EngineRunner.new(loans: loan_details) }
  let(:credit_model_level) { 'low' }
  let(:loan_details) { create_loan_details(credit_model_level:) }

  context 'for eq operator for low credit model level' do
    let!(:allocation_rule) do
      create :loan_allocation_rule, :comvest, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :credit_model_level, operator: :eq, threshold_value: 'low')
      ]
    end

    it 'matches when the loan credit model level == the threshold_value level' do
      expect(engine_runner.run['1']).to eq(allocation_rule.investor.name)
    end
  end

  context 'for ne operator for champion/challenger' do
    let!(:allocation_rule) do
      create :loan_allocation_rule, :comvest, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :credit_model_level, operator: :ne, threshold_value: 'medium')
      ]
    end

    it 'matches when the loan credit model level != the threshold_value credit model level' do
      expect(engine_runner.run['1']).to eq(allocation_rule.investor.name)
    end
  end
end
