# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanAllocation::Condition::DailyAmount do
  let(:engine_runner) { LoanAllocation::EngineRunner.new(loans: loan_details) }
  let(:loan_details) { create_loan_details(loan_amount: 500, state: 'CA', product: 'APL', term: 30, originator: 'CRB') }
  before { engine_runner.totals[:all_originated] = { crb: 5_000, comvest: 85_000, above_funding_trust: 10_000 } }

  context 'for eq operator' do
    let!(:allocation_rule) do
      create :loan_allocation_rule, :comvest, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :daily_amount, operator: :eq, threshold_value: 85_000)
      ]
    end

    it 'matches when the investor percentage == the threshold_value percentage' do
      expect(engine_runner.run['1']).to eq(allocation_rule.investor.name)
    end
  end

  context 'for ne operator' do
    let!(:allocation_rule) do
      create :loan_allocation_rule, :comvest, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :daily_amount, operator: :ne, threshold_value: 25_000)
      ]
    end

    it 'matches when the investor percentage != the threshold_value percentage' do
      expect(engine_runner.run['1']).to eq(allocation_rule.investor.name)
    end
  end

  context 'for less operator' do
    let!(:allocation_rule) do
      create :loan_allocation_rule, :comvest, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :daily_amount, operator: :less, threshold_value: 90_000)
      ]
    end

    it 'matches when the investor percentage < the threshold_value percentage' do
      expect(engine_runner.run['1']).to eq(allocation_rule.investor.name)
    end
  end

  context 'for greater operator' do
    let!(:allocation_rule) do
      create :loan_allocation_rule, :comvest, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :daily_amount, operator: :greater, threshold_value: 50_000)
      ]
    end

    it 'matches when the investor percentage > the threshold_value percentage' do
      expect(engine_runner.run['1']).to eq(allocation_rule.investor.name)
    end
  end
end
