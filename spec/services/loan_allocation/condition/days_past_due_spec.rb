# frozen_string_literal: true

require 'rails_helper'
require 'loan_allocation/condition/months_at_beyond'

RSpec.describe LoanAllocation::Condition::DaysPastDue do
  let(:engine_runner) { LoanAllocation::EngineRunner.new(loans: loan_details) }
  let(:loan_details) { create_loan_details(loan_amount: 500, state: 'CA', product: 'APL', days_past_due: 30, originator: 'CRB') }

  context 'for less equal operator' do
    let!(:crb_rule) do
      create :loan_allocation_rule, :crb, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :days_past_due, operator: :lte, threshold_value: 45)
      ]
    end
    let!(:comvest_rule) do
      create :loan_allocation_rule, :comvest, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :days_past_due, operator: :lte, threshold_value: 60)
      ]
    end

    it 'matches when the days past due is less than 45 the threshold_value amount' do
      expect(engine_runner.run['1']).to eq(crb_rule.investor.name)
    end

    context 'equal to' do
      let(:loan_details) { create_loan_details(loan_amount: 500, state: 'CA', product: 'APL', days_past_due: 45, originator: 'CRB') }

      it 'matches when the days past due is equal to 45 the threshold_value amount' do
        expect(engine_runner.run['1']).to eq(crb_rule.investor.name)
      end
    end

    context 'does not match' do
      let(:loan_details) { create_loan_details(loan_amount: 500, state: 'CA', product: 'APL', days_past_due: 46, originator: 'CRB') }

      it 'does not match when the days past due is greater than 45 the threshold_value amount' do
        expect(engine_runner.run['1']).not_to eq(crb_rule.investor.name)
        expect(engine_runner.run['1']).to eq(comvest_rule.investor.name)
      end
    end
  end
end
