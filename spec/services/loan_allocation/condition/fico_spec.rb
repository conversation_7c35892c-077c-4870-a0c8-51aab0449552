# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanAllocation::Condition::Fico do
  let(:engine_runner) { LoanAllocation::EngineRunner.new(loans: loan_details) }
  let(:loan_details) { create_loan_details(loan_amount: 500, state: 'CA', product: 'APL', term: 30, originator: 'CRB', credit_score: 500) }

  context 'for eq operator' do
    let!(:allocation_rule) do
      create :loan_allocation_rule, :crb, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :fico, operator: :eq, threshold_value: 500)
      ]
    end

    it 'matches when the credit score == the threshold_value amount' do
      expect(engine_runner.run['1']).to eq(allocation_rule.investor.name)
    end
  end

  context 'for ne operator' do
    let!(:allocation_rule) do
      create :loan_allocation_rule, :crb, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :fico, operator: :ne, threshold_value: 500.1)
      ]
    end

    it 'matches when the credit score != the threshold_value amount' do
      expect(engine_runner.run['1']).to eq(allocation_rule.investor.name)
    end
  end

  context 'for less operator' do
    let!(:allocation_rule) do
      create :loan_allocation_rule, :crb, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :fico, operator: :less, threshold_value: 500.1)
      ]
    end

    it 'matches when the credit score < the threshold_value amount' do
      expect(engine_runner.run['1']).to eq(allocation_rule.investor.name)
    end
  end

  context 'for greater operator' do
    let!(:allocation_rule) do
      create :loan_allocation_rule, :crb, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :fico, operator: :greater, threshold_value: 499.9)
      ]
    end

    it 'matches when the credit score > the threshold_value amount' do
      expect(engine_runner.run['1']).to eq(allocation_rule.investor.name)
    end
  end
end
