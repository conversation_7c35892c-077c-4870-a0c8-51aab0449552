# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanAllocation::Condition::FundDistributionPercentage do
  let(:engine_runner) { LoanAllocation::EngineRunner.new(loans: loan_details) }
  let(:loan_details) { create_loan_details(loan_amount: 500, state: 'CA', product: 'APL', term: 30, originator: 'CRB', cashout_amount: 500) }
  let(:distribution_percentage) { (loan_details.first[:cashout_amount].to_f / loan_details.first[:loan_amount] * 100.0).round(2) }

  context 'for eq operator' do
    let!(:allocation_rule) do
      create :loan_allocation_rule, :crb, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :fund_distribution_percentage, operator: :eq, threshold_value: distribution_percentage)
      ]
    end

    it 'matches when the percentage == the threshold_value percentage' do
      expect(engine_runner.run['1']).to eq(allocation_rule.investor.name)
    end
  end

  context 'for ne operator' do
    let!(:allocation_rule) do
      create :loan_allocation_rule, :crb, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :fund_distribution_percentage, operator: :ne, threshold_value: distribution_percentage + 1)
      ]
    end

    it 'matches when the percentage != the threshold_value percentage' do
      expect(engine_runner.run['1']).to eq(allocation_rule.investor.name)
    end
  end

  context 'for less operator' do
    let!(:allocation_rule) do
      create :loan_allocation_rule, :crb, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :fund_distribution_percentage, operator: :less, threshold_value: distribution_percentage + 1)
      ]
    end

    it 'matches when the percentage < the threshold_value percentage' do
      expect(engine_runner.run['1']).to eq(allocation_rule.investor.name)
    end
  end

  context 'for greater operator' do
    let!(:allocation_rule) do
      create :loan_allocation_rule, :crb, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :fund_distribution_percentage, operator: :greater, threshold_value: distribution_percentage - 1)
      ]
    end

    it 'matches when the loan amount > the threshold_value amount' do
      expect(engine_runner.run['1']).to eq(allocation_rule.investor.name)
    end
  end
end
