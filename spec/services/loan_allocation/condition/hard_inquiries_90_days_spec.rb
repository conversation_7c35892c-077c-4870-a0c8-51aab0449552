# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanAllocation::Condition::HardInquiries90Days do
  let(:engine_runner) { LoanAllocation::EngineRunner.new(loans: loan_details) }
  let(:loan_details) { create_loan_details(loan_amount: 500, state: 'CA', product: 'APL', term: 30, originator: 'CRB', hard_inquiries_90_days: 3) }

  context 'for eq operator' do
    let!(:allocation_rule) do
      create :loan_allocation_rule, :crb, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :hard_inquiries_90_days, operator: :eq, threshold_value: 3)
      ]
    end

    it 'matches when the hard_inquiries_90_days == the threshold_value' do
      expect(engine_runner.run['1']).to eq(allocation_rule.investor.name)
    end
  end

  context 'for ne operator' do
    let!(:allocation_rule) do
      create :loan_allocation_rule, :crb, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :hard_inquiries_90_days, operator: :ne, threshold_value: 4)
      ]
    end

    it 'matches when the hard_inquiries_90_days != the threshold_value' do
      expect(engine_runner.run['1']).to eq(allocation_rule.investor.name)
    end
  end

  context 'for less operator' do
    let!(:allocation_rule) do
      create :loan_allocation_rule, :crb, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :hard_inquiries_90_days, operator: :less, threshold_value: 7)
      ]
    end

    it 'matches when the hard_inquiries_90_days < the threshold_value' do
      expect(engine_runner.run['1']).to eq(allocation_rule.investor.name)
    end
  end

  context 'for greater operator' do
    let!(:allocation_rule) do
      create :loan_allocation_rule, :crb, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :hard_inquiries_90_days, operator: :greater, threshold_value: 1)
      ]
    end

    it 'matches when the hard_inquiries_90_days > the threshold_value' do
      expect(engine_runner.run['1']).to eq(allocation_rule.investor.name)
    end
  end
end
