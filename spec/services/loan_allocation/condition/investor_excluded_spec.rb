# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanAllocation::Condition::InvestorExcluded do
  let(:engine_runner) { LoanAllocation::EngineRunner.new(loans: loan_details) }
  let(:loan_details) { create_loan_details(loan_amount: 500, state: 'CA', product: 'APL', term: 30, originator: 'CRB') }
  let!(:investor1) { create(:above_lending_investor, name: 'comvest') }
  let!(:investor2) { create(:above_lending_investor, name: 'above_funding_trust') }
  let!(:exclusion_rule) do
    create :loan_allocation_rule, :above_funding_trust, category: :exclusion
  end

  context 'for eq operator' do
    let!(:allocation_rule) do
      create :loan_allocation_rule, :comvest, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :investor_excluded, operator: :eq, threshold_value: 'above_funding_trust')
      ]
    end

    it 'matches when an exclusion rule is triggered for the threshold_value investor' do
      expect(engine_runner.run['1']).to eq(allocation_rule.investor.name)
    end
  end

  context 'for ne operator' do
    let!(:allocation_rule) do
      create :loan_allocation_rule, :crb, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :investor_excluded, operator: :ne, threshold_value: 'comvest')
      ]
    end

    it 'matches when an exclusion rule is not triggered for the threshold_value investor' do
      expect(engine_runner.run['1']).to eq(allocation_rule.investor.name)
    end
  end
end
