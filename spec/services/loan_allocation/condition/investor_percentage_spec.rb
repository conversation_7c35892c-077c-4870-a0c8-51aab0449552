# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanAllocation::Condition::InvestorPercentage do
  let(:engine_runner) { LoanAllocation::EngineRunner.new(loans: loan_details) }
  let(:loan_details) { create_loan_details(loan_amount: 500, state: 'CA', product: 'APL', term: 30, originator: 'CRB') }
  let!(:investor) { create(:above_lending_investor, name: 'crb') }
  before { engine_runner.totals[:daily] = { crb: 500, comvest: 8500, above_funding_trust: 1000 } }

  context 'for eq operator' do
    let!(:allocation_rule) do
      create :loan_allocation_rule, :crb, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :daily_percentage, operator: :eq, threshold_value: 5.0)
      ]
    end

    it 'matches when the investor percentage == the threshold_value percentage' do
      expect(engine_runner.run['1']).to eq(allocation_rule.investor.name)
    end
  end

  context 'for ne operator' do
    let!(:allocation_rule) do
      create :loan_allocation_rule, :crb, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :daily_percentage, operator: :ne, threshold_value: 5.1)
      ]
    end

    it 'matches when the investor percentage != the threshold_value percentage' do
      expect(engine_runner.run['1']).to eq(allocation_rule.investor.name)
    end
  end

  context 'for less operator' do
    let!(:allocation_rule) do
      create :loan_allocation_rule, :crb, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :daily_percentage, operator: :less, threshold_value: 5.1)
      ]
    end

    it 'matches when the investor percentage < the threshold_value percentage' do
      expect(engine_runner.run['1']).to eq(allocation_rule.investor.name)
    end
  end

  context 'for greater operator' do
    let!(:allocation_rule) do
      create :loan_allocation_rule, :crb, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :daily_percentage, operator: :greater, threshold_value: 4.9)
      ]
    end

    it 'matches when the investor percentage > the threshold_value percentage' do
      expect(engine_runner.run['1']).to eq(allocation_rule.investor.name)
    end
  end
end
