# frozen_string_literal: true

RSpec.describe LoanAllocation::Condition::Originator do
  let(:engine_runner) { LoanAllocation::EngineRunner.new(loans: loan_details) }
  let(:loan_details) { create_loan_details(loan_amount: 500, state: 'CA', product: 'APL', term: 30, originator: 'CRB') }

  context 'for eq operator' do
    let!(:allocation_rule) do
      create :loan_allocation_rule, :comvest, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :originator, operator: :eq, threshold_value: 'CRB')
      ]
    end

    it 'matches when the loan originator == the threshold_value originator' do
      expect(engine_runner.run['1']).to eq(allocation_rule.investor.name)
    end
  end

  context 'for ne operator' do
    let!(:allocation_rule) do
      create :loan_allocation_rule, :crb, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :originator, operator: :ne, threshold_value: 'ABOVE')
      ]
    end

    it 'matches when the loan originator != the threshold_value originator' do
      expect(engine_runner.run['1']).to eq(allocation_rule.investor.name)
    end
  end
end
