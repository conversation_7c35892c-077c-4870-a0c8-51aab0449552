# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanAllocation::Condition::PaymentFrequency do
  let(:engine_runner) { LoanAllocation::EngineRunner.new(loans: loan_details) }
  let(:loan_details) { create_loan_details(loan_amount: 500, state: 'CA', product: 'APL', originator: 'CRB', payment_frequency: 'loan.frequency.monthly') }

  context 'for eq operator' do
    let!(:allocation_rule) do
      create :loan_allocation_rule, :crb, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :payment_frequency, operator: :eq, threshold_value: 'loan.frequency.monthly')
      ]
    end

    it 'matches when the loan frequency == the threshold_value frequency' do
      expect(engine_runner.run['1']).to eq(allocation_rule.investor.name)
    end
  end

  context 'for ne operator' do
    let!(:allocation_rule) do
      create :loan_allocation_rule, :crb, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :payment_frequency, operator: :ne, threshold_value: 'loan.frequency.biWeekly')
      ]
    end

    it 'matches when the loan frequency != the threshold_value frequency' do
      expect(engine_runner.run['1']).to eq(allocation_rule.investor.name)
    end
  end

  context 'for ne operator' do
    let!(:allocation_rule) do
      create :loan_allocation_rule, :crb, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :payment_frequency, operator: :ne, threshold_value: 'loan.frequency.semiMonthly')
      ]
    end

    it 'matches when the loan frequency != the threshold_value frequency' do
      expect(engine_runner.run['1']).to eq(allocation_rule.investor.name)
    end
  end
end
