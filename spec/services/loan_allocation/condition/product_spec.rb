# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanAllocation::Condition::Product do
  let(:engine_runner) { LoanAllocation::EngineRunner.new(loans: loan_details) }
  let(:loan_details) { create_loan_details(loan_amount: 500, state: 'CA', product: 'IPL', term: 30, originator: 'CRB') }

  context 'for eq operator for IPL/AGL' do
    let!(:allocation_rule) do
      create :loan_allocation_rule, :comvest, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :product, operator: :eq, threshold_value: 'AGL')
      ]
    end

    it 'matches when the loan product == the threshold_value product with transformed value' do
      expect(engine_runner.run['1']).to eq(allocation_rule.investor.name)
    end
  end

  context 'for eq operator for non-IPL/APL' do
    let(:loan_details) { create_loan_details(loan_amount: 500, state: 'CA', product: 'DM', term: 30, originator: 'CRB') }
    let!(:allocation_rule) do
      create :loan_allocation_rule, :comvest, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :product, operator: :eq, threshold_value: 'APL')
      ]
    end

    it 'matches when the loan product == the threshold_value product with transformed value' do
      expect(engine_runner.run['1']).to eq(allocation_rule.investor.name)
    end
  end

  context 'for ne operator' do
    let!(:allocation_rule) do
      create :loan_allocation_rule, :crb, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :product, operator: :ne, threshold_value: 'APL')
      ]
    end

    it 'matches when the loan product != the threshold_value product' do
      expect(engine_runner.run['1']).to eq(allocation_rule.investor.name)
    end
  end
end
