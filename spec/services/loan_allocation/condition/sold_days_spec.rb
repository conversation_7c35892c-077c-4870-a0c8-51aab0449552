# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanAllocation::Condition::SoldDays do
  let(:engine_runner) { LoanAllocation::EngineRunner.new(loans: loan_details) }
  let(:loan_details) { create_loan_details(loan_amount: 500, state: 'CA', product: 'APL', contract_date: contract_date.to_s, originator: 'CRB') }

  let!(:crb_rule) do
    create :loan_allocation_rule, :crb, category: :allocation, loan_allocation_conditions: [
      build(:loan_allocation_condition, parameter: :sold_days, operator: :lte, threshold_value: 3)
    ]
  end
  let!(:comvest_rule) do
    create :loan_allocation_rule, :comvest, category: :allocation, loan_allocation_conditions: [
      build(:loan_allocation_condition, parameter: :sold_days, operator: :lte, threshold_value: 10)
    ]
  end

  context 'for less equal operator' do
    let(:contract_date) { Date.new(2022, 9, 8) }

    it 'matches when the sold days is less than or equal to the threshold_value amount' do
      Timecop.freeze(2022, 9, 10, 12) do
        expect(engine_runner.run['1']).to eq(crb_rule.investor.name)
      end
    end

    context 'equal to' do
      it 'matches when the sold days is equal to the threshold_value amount' do
        Timecop.freeze(2022, 9, 13, 12) do
          expect(engine_runner.run['1']).to eq(crb_rule.investor.name)
        end
      end
    end

    context 'does not match' do
      it 'does not match when the sold days is not equal to the threshold_value amount' do
        Timecop.freeze(2022, 9, 16, 12) do
          expect(engine_runner.run['1']).not_to eq(crb_rule.investor.name)
          expect(engine_runner.run['1']).to eq(comvest_rule.investor.name)
        end
      end
    end
  end

  context 'for a long date range over weekends and holidays' do
    let(:contract_date) { Date.new(2024, 12, 20) }

    it 'should calculate the correct number of sold days' do
      Timecop.freeze(2025, 1, 3, 12) do
        expect(described_class.new(comvest_rule.loan_allocation_conditions.first).sold_days(contract_date)).to eq 8
      end
    end
  end
end
