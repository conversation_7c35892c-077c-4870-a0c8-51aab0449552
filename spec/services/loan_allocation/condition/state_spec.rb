# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanAllocation::Condition::State do
  let(:engine_runner) { LoanAllocation::EngineRunner.new(loans: loan_details) }
  let(:loan_details) { create_loan_details(loan_amount: 500, state: 'CA', product: 'APL', term: 30, originator: 'CRB') }

  context 'for eq operator' do
    let!(:allocation_rule) do
      create :loan_allocation_rule, :comvest, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :state, operator: :eq, threshold_value: 'CA')
      ]
    end

    it 'matches when the loan state == the threshold_value state' do
      expect(engine_runner.run['1']).to eq(allocation_rule.investor.name)
    end
  end

  context 'for ne operator' do
    let!(:allocation_rule) do
      create :loan_allocation_rule, :crb, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :state, operator: :ne, threshold_value: 'TX')
      ]
    end

    it 'matches when the loan state != the threshold_value state' do
      expect(engine_runner.run['1']).to eq(allocation_rule.investor.name)
    end
  end
end
