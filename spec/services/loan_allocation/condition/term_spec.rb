# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanAllocation::Condition::Term do
  let(:engine_runner) { LoanAllocation::EngineRunner.new(loans: loan_details) }
  let(:loan_details) { create_loan_details(loan_amount: 500, state: 'CA', product: 'APL', term: 30, originator: 'CRB') }

  context 'for eq operator' do
    let!(:allocation_rule) do
      create :loan_allocation_rule, :comvest, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :term, operator: :eq, threshold_value: 30)
      ]
    end

    it 'matches when the loan term == the threshold_value term' do
      expect(engine_runner.run['1']).to eq(allocation_rule.investor.name)
    end
  end

  context 'for ne operator' do
    let!(:allocation_rule) do
      create :loan_allocation_rule, :crb, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :term, operator: :ne, threshold_value: 31)
      ]
    end

    it 'matches when the loan term != the threshold_value term' do
      expect(engine_runner.run['1']).to eq(allocation_rule.investor.name)
    end
  end

  context 'for less operator' do
    let!(:allocation_rule) do
      create :loan_allocation_rule, :comvest, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :term, operator: :less, threshold_value: 31)
      ]
    end

    it 'matches when the loan term < the threshold_value term' do
      expect(engine_runner.run['1']).to eq(allocation_rule.investor.name)
    end
  end

  context 'for greater operator' do
    let!(:allocation_rule) do
      create :loan_allocation_rule, :comvest, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :term, operator: :greater, threshold_value: 29)
      ]
    end

    it 'matches when the loan term > the threshold_value term' do
      expect(engine_runner.run['1']).to eq(allocation_rule.investor.name)
    end
  end
end
