# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanAllocation::Condition::UnpaidPrincipalBalance do
  let(:engine_runner) { LoanAllocation::EngineRunner.new(loans: loan_details) }
  let(:loan_details) do
    create_loan_details(loan_amount: 1_000, product: 'APL', originator: 'CRB', unpaid_principal_balance: 500)
  end

  context 'for eq operator' do
    let!(:allocation_rule) do
      create :loan_allocation_rule, :crb, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :unpaid_principal_balance, operator: :eq, threshold_value: 500)
      ]
    end

    it 'matches when the UPB == the threshold_value amount' do
      expect(engine_runner.run['1']).to eq(allocation_rule.investor.name)
    end
  end

  context 'for ne operator' do
    let!(:allocation_rule) do
      create :loan_allocation_rule, :crb, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :unpaid_principal_balance, operator: :ne, threshold_value: 501)
      ]
    end

    it 'matches when the UPB != the threshold_value amount' do
      expect(engine_runner.run['1']).to eq(allocation_rule.investor.name)
    end
  end

  context 'for less operator' do
    let!(:allocation_rule) do
      create :loan_allocation_rule, :crb, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :unpaid_principal_balance, operator: :less, threshold_value: 501)
      ]
    end

    it 'matches when the UPB < the threshold_value amount' do
      expect(engine_runner.run['1']).to eq(allocation_rule.investor.name)
    end
  end

  context 'for lte operator' do
    let!(:allocation_rule) do
      create :loan_allocation_rule, :crb, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :unpaid_principal_balance, operator: :lte, threshold_value: 501)
      ]
    end

    let!(:comvest_rule) do
      create :loan_allocation_rule, :comvest, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :unpaid_principal_balance, operator: :lte, threshold_value: 601)
      ]
    end

    it 'matches when the UPB < the threshold_value amount' do
      expect(engine_runner.run['1']).to eq(allocation_rule.investor.name)
    end

    context 'matches equal to' do
      let(:loan_details) { create_loan_details(unpaid_principal_balance: 501, product: 'APL', originator: 'CRB') }

      it 'when the UPB <= the threshold_value amount' do
        expect(engine_runner.run['1']).to eq(allocation_rule.investor.name)
      end
    end

    context 'does not match' do
      let(:loan_details) { create_loan_details(unpaid_principal_balance: 502, product: 'APL', originator: 'CRB') }

      it 'when the UPB more the threshold_value amount' do
        expect(engine_runner.run['1']).not_to eq(allocation_rule.investor.name)
      end
    end
  end

  context 'for greater operator' do
    let!(:allocation_rule) do
      create :loan_allocation_rule, :crb, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :unpaid_principal_balance, operator: :greater, threshold_value: 499)
      ]
    end

    it 'matches when the UPB > the threshold_value amount' do
      expect(engine_runner.run['1']).to eq(allocation_rule.investor.name)
    end
  end

  context 'for gte operator' do
    let!(:comvest_rule) do
      create :loan_allocation_rule, :comvest, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :unpaid_principal_balance, operator: :lte, threshold_value: 601)
      ]
    end
    let!(:crb_rule) do
      create :loan_allocation_rule, :crb, category: :allocation, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :unpaid_principal_balance, operator: :gte, threshold_value: 499)
      ]
    end

    it 'matches when the UPB > the threshold_value amount' do
      expect(engine_runner.run['1']).to eq(comvest_rule.investor.name)
    end

    context 'matches equal to' do
      let(:loan_details) { create_loan_details(unpaid_principal_balance: 500, product: 'APL', originator: 'CRB') }

      it 'when the UPB >= the threshold_value amount' do
        expect(engine_runner.run['1']).to eq(comvest_rule.investor.name)
      end
    end

    context 'does not match' do
      let(:loan_details) { create_loan_details(unpaid_principal_balance: 750, product: 'APL', originator: 'CRB') }

      it 'when the UPB more the threshold_value amount' do
        expect(engine_runner.run['1']).not_to eq(comvest_rule.investor.name)
      end
    end
  end
end
