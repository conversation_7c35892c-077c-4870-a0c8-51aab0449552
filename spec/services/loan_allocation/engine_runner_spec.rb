# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanAllocation::EngineRunner do
  include NotifierHelper

  let(:engine_runner) { LoanAllocation::EngineRunner.new(loans: loan_details) }

  context 'when a loan detail is invalid' do
    let(:loan_details) { create_loan_details(loan_amount: nil, state: 'CA', product: 'APL', term: 30, originator: 'CRB') }

    it 'does not process the loan' do
      engine_runner.run

      expect(engine_runner.allocations).to be_empty
      expect(engine_runner.engine_run.invalid_loans).to eq({ loan_details.first.unified_id => 'Loan amount can\'t be blank' })
    end
  end

  context 'logging' do
    before { allow(ActiveSupport::Notifications).to receive(:instrument).and_call_original }

    let(:loan_details) { create_loan_details(loan_amount: nil, state: 'CA', product: 'ACL', term: 30, originator: 'CRB') }

    it 'logs start' do
      expect(Rails.logger).to receive(:info).with('Engine Runner Process Dry Run After Query')

      engine_runner.run
    end

    it 'notifies of an engine run starting after the query' do
      engine_runner.run
      expect_to_notify('loan_allocation_engine_runner', success: true, message: 'logs start of engine run after query')
    end
  end

  describe '#initialize' do
    it 'accepts configured options' do
      runner = LoanAllocation::EngineRunner.new(loans: create_loan_details, seed: 1234, use_cache: true)
      expect(runner.seed).to eq(1234)
      expect(runner.use_cache).to be_truthy
    end

    it 'in prod, it uses its own values' do
      allow(Rails.env).to receive(:production?).and_return(true)
      runner = LoanAllocation::EngineRunner.new(loans: create_loan_details, seed: 1234, use_cache: true)
      expect(runner.seed).not_to eq(1234)
      expect(runner.seed).not_to be_nil
      expect(runner.use_cache).to be_truthy
    end
  end

  describe '#randomized_unified_ids' do
    let(:loan_details) { 5.times.map { |id| create_unallocated_loan_record(unified_id: id.to_s) } }

    it 'randomizes the loans' do
      first = LoanAllocation::EngineRunner.new(loans: loan_details)
      second = LoanAllocation::EngineRunner.new(loans: loan_details)

      first.remove_invalid_loans
      second.remove_invalid_loans

      expect(first.randomized_unified_ids).not_to eq(second.randomized_unified_ids)
    end

    it 'uses the same loans list for same seed' do
      first = LoanAllocation::EngineRunner.new(loans: loan_details, seed: 1234)
      second = LoanAllocation::EngineRunner.new(loans: loan_details, seed: 1234)

      first.remove_invalid_loans
      second.remove_invalid_loans

      expect(first.randomized_unified_ids).to eq(second.randomized_unified_ids)
    end
  end

  describe '#concentrations' do
    let(:date) { Date.yesterday }
    let(:investor) { loan_allocation_rule.investor }
    let(:loan_allocation_rule) { create(:loan_allocation_rule, :crb, category: :exclusion) }
    let(:concentration) { create(:concentration, investor:, category: :average_balance, balance_type: 'upb') }

    before { create(:loan_allocation_condition, loan_allocation_rule:, investor:, parameter: 'concentration', concentration:, operator: :eq, threshold_value: 10) }

    subject { LoanAllocation::EngineRunner.new(date, loans: create_loan_details).concentrations }

    context 'without cache' do
      it 'properly loads necessary concentrations' do
        expect_any_instance_of(Concentration).to receive(:date=).with(date)
        expect_any_instance_of(Concentration).not_to receive(:metadata=)
        is_expected.to eq([concentration])
      end
    end

    context 'with cache' do
      before { allow(Rails.cache).to receive(:read).with(concentration.cache_key).and_return([10, { total_upb: 20, loans_count: 2 }]) }

      subject { LoanAllocation::EngineRunner.new(date, loans: create_loan_details, use_cache: true).concentrations }

      it 'properly loads necessary concentrations with metadata' do
        expect_any_instance_of(Concentration).to receive(:date=).with(date)
        expect_any_instance_of(Concentration).to receive(:metadata=).with({ total_upb: 20, loans_count: 2 })
        is_expected.to eq([concentration])
      end
    end
  end

  describe '#user_email' do
    it 'sets user_email correctly' do
      engine_runner = LoanAllocation::EngineRunner.new(loans: create_loan_details, user_email: '<EMAIL>')
      expect(engine_runner.user_email).to eq('<EMAIL>')
    end
  end
end
