# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanAllocation::ImportRules do
  let(:file_path) { Rails.root.join('spec', 'fixtures', 'files', 'loan_allocation_rules.json') }
  let!(:concentration) { create(:concentration, :comvest, category: :wa_fico, balance_type: 'upb') }
  let!(:aft) { create(:above_lending_investor, name: 'above_funding_trust') }

  subject(:service) { described_class.new(file_path) }

  describe '#initialize' do
    context 'when the file is valid' do
      it 'parses the JSON file and initializes the service' do
        expect(service.required_concentrations).to be_a(Hash)
        expect(service.rules).to be_a(Array)
      end
    end

    context 'when the file is not a JSON' do
      let(:file_path) { Rails.root.join('spec', 'fixtures', 'files', 'test.csv') }

      it 'raises a ProcessError' do
        expect { service }.to raise_error(LoanAllocation::ImportRules::ProcessError)
      end
    end

    context 'when the file is missing required keys' do
      let(:file_path) { double(read: '{}') }

      it 'raises a ProcessError' do
        expect { service }.to raise_error(LoanAllocation::ImportRules::ProcessError)
      end
    end

    context 'when concentrations are missing' do
      before { concentration.destroy }

      it 'raises a ProcessError' do
        expect { service }.to raise_error(LoanAllocation::ImportRules::ProcessError)
      end
    end
  end

  describe '#call' do
    context 'when the rules are valid' do
      it 'creates loan allocation rules' do
        expect { service.call }.to change(LoanAllocationRule, :count).to(2)
      end

      it 'creates loan allocation conditions' do
        expect { service.call }.to change(LoanAllocationCondition, :count).to(3)
      end

      it 'associates the correct investor and concentration' do
        service.call

        rule = LoanAllocationRule.last
        condition = rule.loan_allocation_conditions.last

        expect(rule.investor_id).to eq(concentration.investor_id)
        expect(condition.concentration_id).to eq(concentration.id)
      end

      context 'when rule creation fails' do
        let(:file_path) { Rails.root.join('spec', 'fixtures', 'files', 'invalid_loan_allocation_rule.json') }

        it 'raises a ProcessError with rule index' do
          expect { service.call }.to raise_error(LoanAllocation::ImportRules::ProcessError, /Failed to create rule #1:/)
        end
      end
    end

    context 'when there are existing rules' do
      let!(:rule1) do
        create(:loan_allocation_rule, investor: aft, category: :allocation, loan_allocation_conditions: [
                 build(:loan_allocation_condition, parameter: :originator, operator: :eq, threshold_value: 'CRB'),
                 build(:loan_allocation_condition, parameter: :daily_percentage, operator: :less, threshold_value: 5.0)
               ])
      end

      it 'destroy all existing records' do
        service.call

        expect(LoanAllocationRule).not_to exist(id: rule1.id)
        expect(LoanAllocationCondition).not_to exist(id: rule1.loan_allocation_conditions.first.id)
        expect(LoanAllocationCondition).not_to exist(id: rule1.loan_allocation_conditions.second.id)
      end
    end

    context 'when an investor is missing' do
      before { aft.destroy }

      it 'raises a ProcessError' do
        expect { service.call }.to raise_error(LoanAllocation::ImportRules::ProcessError)
      end
    end
  end
end
