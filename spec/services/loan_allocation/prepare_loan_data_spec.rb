# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanAllocation::PrepareLoanData do
  let(:loan_details) { LoanAllocation::UnallocatedLoanRecord.unallocated_loans }

  let!(:loan) { create(:above_lending_loan, :unallocated, :loanpro, :with_selected_offer) }
  let!(:loan1) { create(:above_lending_loan, :unallocated, :loanpro, :with_selected_offer, exclude_from_allocation: true) }
  let!(:loan3) { create(:above_lending_loan) }
  let!(:rule) { create :loan_allocation_rule, :comvest, category: :allocation }

  context 'preparing the loan data' do
    it 'doesnt prepare loan data for loans excluded from allocation' do
      expect(loan_details.count).to eq 1
      expect(loan_details.any? { |loan| loan.loan_id == loan1.id }).to eq false
    end

    it 'doesnt pull loans where loan_entity is nil' do
      expect(loan_details.count).to eq(1)
      expect(loan_details.any? { |loan| loan.loan_id == loan3.id }).to eq false
    end

    it 'correctly formats the loans for the engine' do
      loan_details = described_class.new(above_lending_loans: [loan]).call.first
      borrower_dob = loan.borrower&.date_of_birth

      expect(loan_details[:unified_id]).to eq(loan.unified_id)
      expect(loan_details[:contract_date]).to eq(loan.contract_date)
      expect(loan_details[:loan_id]).to eq(loan.loanpro_loan.loanpro_loan_id.to_i)
      expect(loan_details[:term]).to eq(loan.term_months)
      expect(loan_details[:state]).to eq(loan.borrower.latest_borrower_info.state)
      expect(loan_details[:months_at_beyond]).to eq(loan.program_duration_in_tmonths)
      expect(loan_details[:interest_rate]).to eq(loan.selected_offer.interest_rate)
      expect(loan_details[:days_past_due]).to eq(loan.loan_pro_loan_entity.loan_status_archive_today.days_past_due)
      expect(loan_details[:external_dti]).to eq(loan.external_dti)
      expect(loan_details[:apr]).to eq(loan.loan_pro_loan_entity.loan_setup_entity.apr)
      expect(loan_details[:credit_score]).to eq(loan.credit_score)
      expect(loan_details[:additional_funds_flag]).to eq(loan.selected_offer.cashout_amount.positive? ? '1' : '0')
      expect(loan_details[:borrower_age]).to eq((Date.today - borrower_dob).to_i.days.in_years.floor)
      expect(loan_details[:cashout_amount]).to eq(loan.selected_offer.cashout_amount)
      expect(loan_details[:payment_frequency]).to eq(loan.loan_payment_detail.beyond_payment_frequency)
      expect(loan_details[:consecutive_payments_count]).to eq(loan.consecutive_payments_count_months)
      expect(loan_details[:bankruptcy_filed_date]).to eq(loan.bankruptcy_filed_date.to_s)
      expect(loan_details[:unpaid_principal_balance]).to eq(loan.loan_pro_loan_entity.loan_status_archive_today.principal_balance)
      expect(loan_details[:credit_model_cohort]).to eq('champion')
      expect(loan_details[:credit_model_level]).to eq('n/a')
      expect(loan_details[:champion_decision]).to eq(loan.loan_detail.decision_champion)
      expect(loan_details[:challenger_decision]).to eq(loan.loan_detail.decision_challenger)
    end

    context 'when cashout amount is nil' do
      before { loan.selected_offer.update(cashout_amount: nil) }
      it 'returns cashout amount and additional funds flag as zero' do
        loan_details = described_class.new(above_lending_loans: [loan]).call.first
        expect(loan_details[:cashout_amount]).to eq(0)
        expect(loan_details[:additional_funds_flag]).to eq('0')
      end
    end

    context 'when credit model cohort is challenger' do
      before do
        create(:above_lending_experiment_subject, subject: loan.borrower, cohort: 'challenger')
      end

      it 'returns champion cohort' do
        loan_details = described_class.new(above_lending_loans: [loan]).call.first
        expect(loan_details[:credit_model_cohort]).to eq('challenger')
      end

      it 'returns the credit model level' do
        loan.loan_detail.update(credit_model_level: 'medium')

        loan_details = described_class.new(above_lending_loans: [loan]).call.first
        expect(loan_details[:credit_model_level]).to eq('medium')
      end

      it 'returns the credit model level default when nil' do
        loan_details = described_class.new(above_lending_loans: [loan]).call.first
        expect(loan_details[:credit_model_level]).to eq('n/a')
      end
    end

    context 'when credit model decision is nil' do
      before do
        loan.loan_detail.update(decision_champion: nil, decision_challenger: nil)
      end

      it 'champion and challenger decision defaults to n/a' do
        loan_details = described_class.new(above_lending_loans: [loan]).call.first

        expect(loan_details[:champion_decision]).to eq('n/a')
        expect(loan_details[:challenger_decision]).to eq('n/a')
      end
    end
  end
end
