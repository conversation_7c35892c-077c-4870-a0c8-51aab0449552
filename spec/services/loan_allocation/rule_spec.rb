# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanAllocationRule do
  include NotifierHelper

  let(:engine_runner) { LoanAllocation::EngineRunner.new(loans: loan_details) }

  context 'for category allocation' do
    let(:loan_details) { create_loan_details(loan_amount: 500, state: 'CA', product: 'DM', term: 30, originator: 'CRB') }
    let(:concentration) { create :concentration, investor: rule.investor, category: :apl, balance_type: :ipb }
    let!(:condition) { create :loan_allocation_condition, loan_allocation_rule: rule, parameter: :concentration, concentration: concentration, operator: :lte, threshold_value: 100 }
    let!(:rule) { create :loan_allocation_rule, :comvest, category: :allocation }

    it 'allocates the matched loan to the investor' do
      engine_runner.run
      expect(engine_runner.allocations['1']).to eq(rule.investor.name)
    end

    it 'logs concentration values' do
      set_notifier_stubs
      engine_runner.run

      # Value after the loan was allocated
      expect(engine_runner.engine_run.audit_logs.last.log_data.to_h['concentrations']).to eq({ 'comvest-Apl-ipb' => 100.0 })

      # Value before any loan has been allocated
      expect_to_notify('concentration_calculated', success: true, extra: {
                         id: concentration.id,
                         concentration: concentration.name,
                         source: 'allocations',
                         value: 0.0,
                         values: [0, 0],
                         keys: %w[apl_ipb total_ipb],
                         count_loans: 0
                       })
    end

    context 'when update_daily_total is set' do
      let!(:rule) { create :loan_allocation_rule, :comvest, category: :allocation, update_daily_total: true }
      it 'adds the loan amount to totals for the investor' do
        expect { engine_runner.run }.to change { engine_runner.totals[:daily][:comvest] }.by(loan_details.first.loan_amount)
        expect { engine_runner.run }.to change { engine_runner.totals[:all_originated][:comvest] }.by(loan_details.first.loan_amount)
      end
    end

    context 'when update_daily_total is not set' do
      it 'does not update the daily total for the investor' do
        expect { engine_runner.run }.to_not(change { engine_runner.totals[:daily][:comvest] })
      end
      it 'updates all_originated total for the investor' do
        expect { engine_runner.run }.to change { engine_runner.totals[:all_originated][:comvest] }.by(loan_details.first.loan_amount)
      end
    end

    context 'when the rule is inactive' do
      before { rule.update(active: false) }

      it 'does not apply the rule' do
        engine_runner.run
        expect(engine_runner.allocations).to be_empty
      end

      specify 'the rule does not exist in the engine runner' do
        expect(engine_runner.rules).to_not include(rule)
      end
    end

    context 'when the applied value of a condition raises an error' do
      before do
        allow_any_instance_of(LoanAllocationCondition).to receive(:applied_value).and_raise StandardError.new('Boom')
      end
      it 'should not raise error' do
        expect { engine_runner.run }.to_not raise_error
      end
      it 'should save the error in invalid_loans' do
        engine_runner.run
        expect(engine_runner.engine_run.invalid_loans['1']).to match(/Engine run error.*value_calculation.*Boom/)
      end
      it 'should not allocate loan' do
        expect(engine_runner.run).to be_empty
      end
    end

    context 'when the prerequisite of a condition raises an error' do
      let!(:rule2) { create :loan_allocation_rule, :comvest, category: :allocation, update_daily_total: true }
      let!(:condition2) { create :loan_allocation_condition, loan_allocation_rule: rule2, parameter: :borrower_age, operator: :gte, threshold_value: 18 }
      before do
        allow_any_instance_of(LoanAllocationCondition).to receive(:prerequisite).and_raise StandardError.new('Boom')
      end
      it 'should not raise error' do
        expect { engine_runner.run }.to_not raise_error
      end
      it 'should save the error in invalid_loans' do
        engine_runner.run
        expect(engine_runner.engine_run.invalid_loans['1']).to match(/Engine run error.*prerequisite.*Boom/)
      end
      it 'should not allocate loan' do
        expect(engine_runner.run).to be_empty
      end
    end
  end

  context 'for category exclusion' do
    let(:loan_details) { create_loan_details(loan_amount: 500, state: 'CA', product: 'APL', term: 30, originator: 'CRB') }
    let!(:rule) { create :loan_allocation_rule, :crb, category: :exclusion }

    let!(:rule2) do
      create :loan_allocation_rule, :crb, category: :exclusion, loan_allocation_conditions: [
        build(:loan_allocation_condition, parameter: :product, operator: :eq, threshold_value: 'AGL')
      ]
    end

    it 'does not allocate the matched loan' do
      expect(engine_runner.run['1']).to be_nil
    end

    it 'does not process further eligibility rules once an eligibility rule matches' do
      expect(engine_runner.rules[1].loan_allocation_conditions[0]).to_not receive(:applied_value)
      engine_runner.run
    end
  end
end
