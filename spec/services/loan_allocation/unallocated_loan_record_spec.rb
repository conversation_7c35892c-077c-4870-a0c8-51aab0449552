# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanAllocation::UnallocatedLoanRecord do
  let(:prepared_data) do
    {
      unified_id: '1', loan_amount: 5_000, contract_date: Date.parse('2022-09-15'),
      loan_id: 1, state: 'AL', product: 'IPL', interest_rate: 28, originator: 'CRB',
      payment_adherence_ratio_3_months: 25,
      payment_adherence_ratio_6_months: 20,
      borrower_age: 18, apr: 5, origination_fee: 200,
      origination_fee_percent: 5, term: 5.0, months_at_beyond: 4,
      days_past_due: 35, external_dti: 60, credit_score: 607, exclude_from_allocation: 'false'
    }
  end

  # CSV Header data
  let(:csv_data) do
    {
      'Unified ID' => '1',
      'LoanID' => 1,
      'Borrower ID' => '18acc5b5-830b-45dc-bb4c-5f3cb3f9cd2f',
      'Age' => 19,
      'Loan Amount' => 5_000,
      'Contract Date' => Date.parse('2022-09-15'),
      'State' => 'AL',
      'Product' => 'IPL',
      'Int Rate' => 15,
      'APR' => 29,
      'Originator' => 'CRB',
      'Orig Fee' => 250,
      'Orig Fee %' => 5,
      'Pmt Incr %' => 10,
      'Term (Mths)' => 35,
      'Days Past Due' => 3,
      'DTI' => 23,
      'FICO' => 550,
      'Payment Frequency' => 'loan.frequency.monthly',
      'Additional Funds Flag' => '1',
      'Months at Beyond' => 4,
      'Consec Pmt Ct' => 5,
      'Pmt Adh 3mo' => 0.9,
      'Pmt Adh 6mo' => 0.8,
      'Payment To Income' => 35,
      'Exclude' => 'false'
    }
  end

  context 'validations' do
    it 'includes abovelending loan validation errors' do
      loan = LoanAllocation::UnallocatedLoanRecord.new(prepared_data)
      loan.valid?
      expect(loan.errors.full_messages).not_to include(/Abovelending loan missing data/)

      loan.abovelending_loan_errors = ['missing data']
      expect(loan).not_to be_valid
      expect(loan.errors.full_messages).to include(/Abovelending loan missing data/)
    end

    it 'validates numericality of payment adherence ratio 6 months' do
      prepared_data[:payment_adherence_ratio_6_months] = 0.0

      loan = LoanAllocation::UnallocatedLoanRecord.new(prepared_data)
      expect(loan).not_to be_valid
      expect(loan.errors.full_messages).to include(/Payment adherence ratio 6 months must be greater than or equal to 1/)
    end
  end

  context '.unallocated_loans_by_csv' do
    it 'returns data from the loan_allocation_candidates.csv' do
      loans = LoanAllocation::UnallocatedLoanRecord.loans_by_csv

      expect(loans.count).to eq 8
      expect(loans.first.loan_amount).to eq 1250
      expect(loans.first.contract_date).to eq Date.parse('2022-03-01')
      expect(loans.first.state).to eq 'CA'
    end

    it 'returns data from a CSV file' do
      csv_data = File.read(Rails.root.join('spec', 'fixtures', 'files', 'loan_allocation_candidates.csv'))

      loans = LoanAllocation::UnallocatedLoanRecord.loans_by_csv(csv_data: csv_data)

      expect(loans.count).to eq 8
      expect(loans.first.loan_amount).to eq 1250
      expect(loans.first.contract_date.class).to eq Date
      expect(loans.first.apr.class).to eq BigDecimal
      expect(loans.first.state).to eq 'CA'
    end
  end

  context '.unallocated_loans' do
    let(:al_loans_double) { double }
    let(:al_loans_select_double) { double }
    let(:prepared_loan_double) do
      double(LoanAllocation::PrepareLoanData, call: [csv_data])
    end

    it 'returns unallocated loans' do
      expect(AboveLending::Loan)
        .to receive_message_chain('unscoped.unallocated.included_in_allocation.limit.select')
        .and_return(al_loans_double)

      expect(LoanAllocation::PrepareLoanData)
        .to receive(:new)
        .with(above_lending_loans: al_loans_double)
        .and_return(prepared_loan_double)

      loan_records = LoanAllocation::UnallocatedLoanRecord.unallocated_loans

      expect(loan_records.count).to eq 1
      csv_data.each do |key, value|
        attribute_key = LoanAllocation::UnallocatedLoanRecord::CSV_HEADER_AND_ATTRIBUTES[key]
        expect(loan_records.first.send(attribute_key)).to eq value
      end
    end

    it 'limits non production data to 100 records' do
      expect(Rails.env).to receive(:production?).and_return false

      expect(AboveLending::Loan)
        .to receive_message_chain('unscoped.unallocated.included_in_allocation.limit')
        .with(100)
        .and_return(al_loans_double)

      expect(al_loans_double)
        .to receive('select')
        .and_return(al_loans_select_double)

      expect(LoanAllocation::PrepareLoanData)
        .to receive(:new)
        .with(above_lending_loans: al_loans_select_double)
        .and_return(prepared_loan_double)

      LoanAllocation::UnallocatedLoanRecord.unallocated_loans
    end
  end

  context 'creates fields from hash' do
    it 'has prepared data fields' do
      loan_record = LoanAllocation::UnallocatedLoanRecord.new(prepared_data)

      prepared_data.each do |key, value|
        expect(loan_record.send(key)).to eq value
      end
    end
  end

  context 'creates fields from csv headers' do
    it 'creates a new record from csv headers' do
      loan_record = LoanAllocation::UnallocatedLoanRecord.new(csv_data)

      csv_data.each do |key, value|
        attribute_key = LoanAllocation::UnallocatedLoanRecord::CSV_HEADER_AND_ATTRIBUTES[key]
        expect(loan_record.send(attribute_key)).to eq value
      end
    end
  end

  context '[]' do
    it 'still returns data value when the csv header is used' do
      loan_record = LoanAllocation::UnallocatedLoanRecord.new(csv_data)

      csv_data.each do |key, value|
        expect(loan_record[key]).to eq value
      end
    end

    it 'still returns data value when the normal key is used' do
      loan_record = LoanAllocation::UnallocatedLoanRecord.new(prepared_data)

      prepared_data.each do |key, value|
        expect(loan_record[key]).to eq value
      end
    end
  end

  describe '#to_h' do
    it 'returns a hash with attributes' do
      loan_record_hash = LoanAllocation::UnallocatedLoanRecord.new(csv_data).to_h

      csv_data.each do |key, value|
        key = LoanAllocation::UnallocatedLoanRecord::CSV_HEADER_AND_ATTRIBUTES[key]
        expect(loan_record_hash[key]).to eq value
      end
    end
  end

  describe '.loans_to_csv' do
    let(:csv_records) { LoanAllocation::UnallocatedLoanRecord.loans_by_csv }
    let(:allocations) { { '1' => 'crb', '2' => 'comvest' } }
    it 'should serialize loan records to CSV correctly' do
      csv = LoanAllocation::UnallocatedLoanRecord.loans_to_csv(csv_records)
      csv_records2 = LoanAllocation::UnallocatedLoanRecord.loans_by_csv(csv_data: csv)
      expect(csv_records).to eq(csv_records2)
    end

    it 'should add an investor column when provided with allocations' do
      csv = LoanAllocation::UnallocatedLoanRecord.loans_to_csv(csv_records, allocations: allocations)
      expect(csv.lines.first).to include('Investor')
      expect(csv.lines.second).to include('crb')
      expect(csv.lines.third).to include('comvest')
    end
  end
end
