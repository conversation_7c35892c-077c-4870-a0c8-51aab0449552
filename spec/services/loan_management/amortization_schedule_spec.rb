# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanManagement::AmortizationSchedule do
  include NotifierHelper

  let!(:loan) { create(:above_lending_loan, :loanpro) }
  let!(:loan_id) { loan.loanpro_loan.loanpro_loan_id }

  before { allow(ActiveSupport::Notifications).to receive(:instrument).and_call_original }

  describe '.call' do
    let(:api_today1) { Date.today.to_time }
    let(:api_today2) { (Date.today + 1.day).to_time }
    let(:api_today3) { (Date.today + 2.day).to_time }
    let(:api_today4) { (Date.today + 3.day).to_time }

    before { allow(LoanProService::LoanManagementSystem::AmortizationSchedule).to receive(:call).with(loan_id: loan_id).and_return(lp_amortization_response) }

    let(:lp_amortization_response) do
      [
        { id: 19_460, date: "/Date(#{api_today1.to_i})/", type: 'origination', principalBalance: '9600.00',
          chargeAmount: '610.71', chargeInterest: '210.71', chargePrincipal: '400.00' },
        { id: 19_461, date: "/Date(#{api_today2.to_i})/", type: 'payment', principalBalance: '9200.00',
          paymentAmount: '610.71', paymentInterest: '210.71', paymentPrincipal: '400.00' },
        { id: 19_463, date: "/Date(#{api_today3.to_i})/", type: 'dpdAdjustment', principalBalance: '8400.00',
          chargeAmount: '610.71', chargeInterest: '210.71', chargePrincipal: '400.00' },
        { id: 19_462, date: "/Date(#{api_today4.to_i})/", type: 'scheduledPayment', principalBalance: '8800.00',
          chargeAmount: '610.71', chargeInterest: '210.71', chargePrincipal: '400.00' },
        { id: 19_464, date: "/Date(#{api_today4.to_i})/", type: 'forecastedPayment', principalBalance: '8800.00',
          chargeAmount: '510.71', chargeInterest: '110.71', chargePrincipal: '400.00' }
      ].reverse.map(&:deep_stringify_keys)
    end

    it 'Returns an array with the amortization schedule' do
      amortization = [
        { id: 19_461, date: api_today2.iso8601, type: 'Payment', isPaid: true, amount: '610.71',
          interest: '210.71', principal: '400.00', balance: '9200.00' },
        { id: 19_462, date: api_today4.iso8601, type: 'Scheduled', isPaid: false, amount: '610.71',
          interest: '210.71', principal: '400.00', balance: '8800.00' }
      ]

      expect(described_class.call(loan_id:)).to eq({ amortization:, count: 2, loanpro_loan_id: loan_id })
    end
  end

  describe 'service errors' do
    let!(:error_message) { 'Internal Server Error' }

    before { expect(LoanProService::LoanManagementSystem::AmortizationSchedule).to receive(:call).with(loan_id:).and_raise(error) }

    subject { described_class.call(loan_id:) }

    context 'when error->message->value is present in API response and status is 200' do
      let(:error) { LoanProService::Exceptions::LoanManagementSystemError.new('Internal Server Error') }

      it 'raises a LoanManagement::PaymentProfiles::PaymentProfilesSystemError with the error message' do
        expect { subject }.to raise_error(LoanManagement::AmortizationSchedule::AmortizationScheduleSystemError, error_message)
        expect_to_notify('amortization_schedule', success: false, fail_reason: 'Internal Server Error')
      end
    end

    context 'when unexpectedly formatted error is present in API response and status is 200' do
      let(:error) { LoanProService::Exceptions::LoanManagementSystemError.new('{"test"=>"unexpected error"}') }

      it 'raises a LoanManagementSystemError' do
        expect { subject }.to raise_error(LoanManagement::AmortizationSchedule::AmortizationScheduleSystemError, error_message)
        expect_to_notify('amortization_schedule', success: false, fail_reason: '{"test"=>"unexpected error"}')
      end
    end

    context 'when status is 4xx (ClientError) but no response body is present' do
      let(:error) { LoanProService::Exceptions::LoanManagementSystemError.new('the server responded with status 403') }

      it 'raises a LoanManagementSystemError with status' do
        expect { subject }.to raise_error(LoanManagement::AmortizationSchedule::AmortizationScheduleSystemError, error_message)
        expect_to_notify('amortization_schedule', success: false, fail_reason: 'the server responded with status 403')
      end
    end
  end
end
