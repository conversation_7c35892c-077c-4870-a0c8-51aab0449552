# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanManagement::BeneficialOwnerDetails do
  include NotifierHelper

  let!(:loan) { create(:above_lending_loan, :loanpro) }
  let!(:loan_id) { loan.loanpro_loan.loanpro_loan_id }
  let!(:beneficial_owner) { create(:beneficial_owner) }
  let!(:debt_sale) { create(:debt_sale, loanpro_loan_id: loan_id, loan_number: loan.unified_id, beneficial_owner:) }

  let(:beneficial_owner_details) do
    {
      name: beneficial_owner.name,
      company_name: beneficial_owner.company_name,
      phone: beneficial_owner.phone_number,
      email: beneficial_owner.email,
      address_street: beneficial_owner.address_street,
      address_apt: beneficial_owner.address_apt,
      city: beneficial_owner.city,
      state: beneficial_owner.state,
      zip_code: beneficial_owner.zip_code,
      website: beneficial_owner.website
    }
  end

  describe '.call' do
    context 'when loan is sold' do
      it 'returns the beneficial owner details' do
        expect(described_class.new(unified_id: loan.unified_id).call).to eq(beneficial_owner_details)
      end
    end

    context 'when loan is not sold' do
      it 'returns nil' do
        expect(described_class.new(unified_id: '1234567890').call).to be_nil
      end
    end

    context 'when an exception is raised' do
      before do
        allow(DebtSale).to receive(:find_by).and_raise(StandardError)
        allow(ActiveSupport::Notifications).to receive(:instrument)
      end
      it 'raises a BeneficialOwnerDetailsError' do
        expect { described_class.new(unified_id: '1234567890').call }.to raise_error(LoanManagement::BeneficialOwnerDetails::BeneficialOwnerDetailsError)
        expect(ActiveSupport::Notifications).to have_received(:instrument)
      end
    end
  end
end
