# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanManagement::CreateAutopay do
  include ActiveJob::TestHelper
  include ActiveSupport::Testing::TimeHelpers
  include NotifierHelper

  shared_examples 'a successfully scheduled autopay' do
    it 'creates the autopay and notifies of a success' do
      expect(LoanProService::LoanManagementSystem::CreateAutopay)
        .to receive(:call).with(loan_id:, attributes: payment_attributes).and_return(true)
      expect(described_class.call(loan_id:, payment_profile_id:, amount:, apply_datetime:, apply_timezone:, charge_off_recovery:)).to eq(true)
      expect_to_notify(event_name, success: true, extra: meta)
    end
  end

  let(:event_name) { 'CreateAutopay' }
  let!(:loan) { create(:above_lending_loan, :loanpro) }
  let!(:loan_id) { loan.loan_pro_loan_entity.id }
  let(:payment_profile_id) { rand(1..10) }
  let(:charge_off_recovery) { false }
  let(:apply_date) { Date.today.iso8601 }
  let(:apply_datetime) { Time.parse(apply_date).iso8601 }
  let(:amount) { Faker::Number.decimal(l_digits: 2, r_digits: 2) }
  let(:expected_process_date) { apply_date }
  let(:apply_timezone) { 'UTC' }
  let(:payment_attributes) do
    {
      amount: amount,
      applyDate: apply_date,
      processDate: expected_process_date,
      processDateTime: "#{expected_process_date} #{CentralTimeZone.three_pm_utc_hour}:00:00",
      primaryPaymentMethodId: payment_profile_id
    }
  end
  let(:payments_params) do
    {
      loan_id: loan_id,
      payment_profile_id: payment_profile_id,
      amount: amount,
      apply_datetime: apply_date
    }
  end

  let(:meta) do
    { amount: Float, apply_datetime: String, apply_timezone: String, loan_id:, payment_profile_id: Integer, process_date: String }
  end

  before do
    travel_to Time.zone.parse('2023-11-02 12:00:00')
    allow(ActiveSupport::Notifications).to receive(:instrument).and_call_original
  end

  describe '.call' do
    it_behaves_like 'a successfully scheduled autopay'
  end

  context 'when an attribute is invalid' do
    it 'raises an error for invalid amount' do
      allow(LoanProService::LoanManagementSystem::CreateAutopay).to receive(:call).with(loan_id:, attributes: payment_attributes).and_return(true)
      expect do
        expect(described_class.call(loan_id:, payment_profile_id:, amount: nil, apply_datetime:, apply_timezone:)).to eq(false)
      end.to raise_error(LoanManagement::CreateAutopay::CreateAutopayValidationError, 'Validation failed: Amount Invalid 0.0')

      expect_to_notify(event_name, success: false, fail_reason: 'ActiveModel::ValidationError: Validation failed: Amount Invalid 0.0', extra: meta.merge(process_date: nil))
    end

    it 'raises an error message for invalid date format' do
      expect do
        expect(described_class.call(loan_id:, payment_profile_id:, amount:, apply_datetime: 'aesrgdhfg', apply_timezone:)).to eq(false)
      end.to raise_error(LoanManagement::CreateAutopay::CreateAutopayValidationError,
                         'Validation failed: Apply datetime Invalid payment date. Expected YYYY-MM-DD but received: aesrgdhfg')

      expect_to_notify(event_name, success: false, fail_reason: 'ActiveModel::ValidationError: Validation failed: Apply datetime Invalid payment date. Expected YYYY-MM-DD but received: aesrgdhfg',
                                   extra: meta.merge(process_date: nil))
    end

    it 'raises an error message for invalid date format' do
      expect do
        expect(described_class.call(loan_id:, payment_profile_id:, amount:, apply_datetime:, apply_timezone: 'fake')).to eq(false)
      end.to raise_error(LoanManagement::CreateAutopay::CreateAutopayValidationError,
                         'Validation failed: Timezone No valid timezone found for fake')
      expect_to_notify(event_name, success: false, fail_reason: 'ActiveModel::ValidationError: Validation failed: Timezone No valid timezone found for fake',
                                   extra: meta.merge(process_date: nil))
    end

    it 'raises an error message for missing payment profile id' do
      expect do
        expect(described_class.call(loan_id:, payment_profile_id: '', amount:, apply_datetime:, apply_timezone:)).to eq(false)
      end.to raise_error(LoanManagement::CreateAutopay::CreateAutopayValidationError, "Validation failed: Payment profile can't be blank")

      expect_to_notify(event_name, success: false, fail_reason: "ActiveModel::ValidationError: Validation failed: Payment profile can't be blank",
                                   extra: meta.merge(process_date: nil, payment_profile_id: ''))
    end
  end

  context 'when the LP API raises an exception' do
    before do
      allow(LoanProService::LoanManagementSystem::CreateAutopay).to receive(:call).and_raise(LoanProService::Exceptions::LoanManagementSystemError, 'Loan Management System Error!')
    end

    it 'raises a CreateAutopaySystemError and notifies' do
      expect do
        described_class.call(loan_id:, payment_profile_id:, amount:,
                             apply_datetime:, apply_timezone:)
      end.to raise_error(LoanManagement::CreateAutopay::CreateAutopaySystemError, 'Internal Server Error')
      expect_to_notify('CreateAutopay', success: false, fail_reason: 'LoanProService::Exceptions::LoanManagementSystemError: Loan Management System Error!',
                                        extra: meta.merge(process_date: nil))
    end
  end

  context 'when the customer schedules a payment for today after the 2pm central cutoff time' do
    before do
      travel_to Time.zone.parse('2023-11-02 23:15:00')
    end

    let(:expected_process_date) { '2023-11-03' }

    it_behaves_like 'a successfully scheduled autopay'
  end

  context 'when the customer schedules a payment on Friday after the cutoff with a Monday holiday' do
    let(:apply_timezone) { 'America/Chicago' }
    before do
      travel_to '2024-02-16 14:30:00'.in_time_zone('America/Chicago')
    end

    let(:apply_date) { '2024-02-16' }
    let(:expected_process_date) { '2024-02-20' }

    it_behaves_like 'a successfully scheduled autopay'
  end

  context 'when the customer schedules a payment from a timezone "behind" central time' do
    let(:apply_timezone) { 'America/Los_Angeles' }

    context 'and the payment comes in at the end of the pacific day and on the next central day' do
      before do
        travel_to '2024-03-11 23:30:00'.in_time_zone('America/Los_Angeles')
      end

      let(:apply_date) { '2024-03-11' }
      let(:expected_process_date) { '2024-03-12' }

      it_behaves_like 'a successfully scheduled autopay'
    end

    context 'and the payment comes in after the 2pm central cutoff time' do
      before do
        travel_to '2024-03-11 14:30:00'.in_time_zone('America/Chicago')
      end

      let(:apply_date) { '2024-03-11' }
      let(:expected_process_date) { '2024-03-12' }

      it_behaves_like 'a successfully scheduled autopay'
    end

    context 'and the payment occurs on a Friday after midnight in Chicago' do
      before do
        travel_to '2024-03-08 23:30:00'.in_time_zone('America/Los_Angeles')
      end

      let(:apply_date) { '2024-03-08' }
      let(:expected_process_date) { '2024-03-11' }

      it_behaves_like 'a successfully scheduled autopay'
    end

    context 'and the payment occurs on a holiday prior to the cutoff' do
      before do
        travel_to '2024-02-19 12:30:00'.in_time_zone('America/Chicago') # President's Day
      end

      let(:apply_date) { '2024-02-19' }
      let(:expected_process_date) { '2024-02-20' }

      it_behaves_like 'a successfully scheduled autopay'
    end

    context 'and the payment occurs on a holiday after midnight in Chicago' do
      before do
        travel_to '2024-02-19 23:30:00'.in_time_zone('America/Los_Angeles') # President's Day
      end

      let(:apply_date) { '2024-02-19' }
      let(:expected_process_date) { '2024-02-20' }

      it_behaves_like 'a successfully scheduled autopay'
    end
  end

  context 'when the customer schedules a payment in the future on a holiday' do
    let(:apply_date) { '2023-11-23' } # Thanksgiving 2023
    let(:expected_process_date) { '2023-11-22' }

    it_behaves_like 'a successfully scheduled autopay'
  end

  context 'when the customer schedules a payment in the future the day after a holiday' do
    let(:apply_date) { '2023-11-24' } # Day after thanksgiving 2023
    let(:expected_process_date) { '2023-11-22' }

    it_behaves_like 'a successfully scheduled autopay'
  end

  context 'when a request is made for payment creation on a non-working day to make a payment on a non working day' do
    before do
      travel_to Time.zone.parse('2024-01-13 12:00:00')
    end

    context 'and there are no working days between the apply date and the request create date' do
      let(:apply_date) { '2024-01-15' }
      let(:expected_process_date) { '2024-01-16' }

      it_behaves_like 'a successfully scheduled autopay'
    end
  end

  context 'when a request is made for payment creation on a non-working day to make a payment on a working day' do
    context 'and there are no working days between the apply date and the request create date' do
      before do
        travel_to Time.zone.parse('2024-01-13 12:00:00')
      end

      let(:apply_date) { '2024-01-16' }
      let(:expected_process_date) { '2024-01-16' }

      it_behaves_like 'a successfully scheduled autopay'
    end
  end

  context 'when the apply date for a payment is in the past' do
    let(:apply_date) { '2023-11-01' }

    it 'logs the error and raises a CreateAutopayValidationError' do
      expect(ExceptionLogger).to receive(:error).with(instance_of(LoanManagement::CreateAutopay::CreateAutopayValidationError))
      expect(Dash::Observability).to receive(:error)
      expect do
        described_class.call(loan_id:, payment_profile_id:, amount:, apply_datetime:, apply_timezone:)
      end.to raise_error(LoanManagement::CreateAutopay::CreateAutopayValidationError)
      expect_to_notify(event_name, success: false, fail_reason: "CreateAutopayValidationError - Apply Date - Apply date was in the past. Parsed date: #{apply_date}. Timezone: UTC",
                                   extra: meta.merge(process_date: nil))
    end
  end

  context 'when the customer makes a request for an apply date with only-non working days in between' do
    before do
      travel_to Time.zone.parse('2024-01-19 12:00:00')
    end

    let(:apply_date) { '2024-01-22' }
    let(:expected_process_date) { '2024-01-19' }

    it_behaves_like 'a successfully scheduled autopay'

    context 'when the request comes in after the 2pm cutoff' do
      before do
        travel_to Time.zone.parse('2024-01-19 23:15:00')
      end

      let(:apply_date) { '2024-01-22' }
      let(:expected_process_date) { '2024-01-22' }

      it_behaves_like 'a successfully scheduled autopay'
    end
  end
end
