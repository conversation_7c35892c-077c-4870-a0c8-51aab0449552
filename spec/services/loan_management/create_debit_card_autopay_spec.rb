# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanManagement::CreateDebitCardAutopay do
  include NotifierHelper

  let!(:loan) { create(:above_lending_loan, :loanpro) }
  let!(:borrower_id) { loan.borrower.id }
  let!(:loan_id) { loan.loanpro_loan.loanpro_loan_id }
  let(:date) { Date.today.iso8601 }
  let(:amount) { Faker::Number.decimal(l_digits: 2, r_digits: 2) }
  let!(:first_name) { Faker::Name.first_name }
  let!(:account_number_last4) { Faker::Bank.account_number[-4..] }
  let!(:customer_id) { Faker::Number.number(digits: 5) }
  let!(:title) { "Debit Card Payment on #{date.to_datetime}" }
  let!(:get_customers_response) do
    { 'Customers' => {
      'results' => [
        { 'id' => customer_id }
      ]
    } }
  end
  let(:debit_card_params) do
    {
      loan_id: loan_id,
      amount: amount,
      date: date
    }
  end
  let(:full_debit_card_params) do
    {
      loan_id: loan_id,
      amount: amount,
      date: date,
      firstName: first_name,
      accountNumberLast4Digits: account_number_last4
    }
  end
  let(:debit_card_id) { Faker::Number.number(digits: 5) }
  let(:checking_id) { debit_card_id - 1 }
  let(:process_new_payment_response) do
    {
      'id' => loan_id,
      'displayId' => loan_id.to_s,
      'title' => loan_id.to_s,
      'settingsId' => 8750,
      'setupId' => 9100,
      'collateralId' => 0,
      'linkedLoan' => 0,
      'modId' => 0,
      'modTotal' => 0,
      'created' => '/Date(**********)/',
      'lastMaintRun' => '/Date(**********)/',
      'createdBy' => 9678,
      'active' => 1,
      'archived' => 0,
      'temporaryAccount' => 0,
      'deleted' => 0
    }
  end
  let(:get_payment_profiles_response) do
    { 'results' =>
          [
            {
              'id' => checking_id,
              'entityId' => customer_id,
              'isPrimary' => 1,
              'isSecondary' => 0,
              'title' => 'Checking Account',
              'type' => 'paymentAccount.type.checking',
              'creditCardId' => 0,
              'checkingAccountId' => 12_018,
              'active' => 1,
              'created' => '/Date(**********)/',
              'CheckingAccount' => { '__deferred' => { 'uri' => 'PaymentAccounts(12006)/CheckingAccount' } },
              'CreditCard' => { '__deferred' => { 'uri' => 'PaymentAccounts(12006)/CreditCard' } }
            },
            {
              'id' => debit_card_id,
              'entityId' => customer_id,
              'isPrimary' => 0,
              'isSecondary' => 1,
              'title' => title,
              'type' => 'paymentAccount.type.credit',
              'creditCardId' => 24,
              'checkingAccountId' => 0,
              'active' => 1,
              'created' => '/Date(**********)/',
              'CheckingAccount' => { '__deferred' => { 'uri' => 'PaymentAccounts(12009)/CheckingAccount' } },
              'CreditCard' => { '__deferred' => { 'uri' => 'PaymentAccounts(12009)/CreditCard' } }
            }
          ] }
  end
  subject { described_class.call(debit_card_params) }
  let(:base_url) { LoanProService::ConfigHelper.configuration.loan_management_system_url }

  before do
    allow(ActiveSupport::Notifications).to receive(:instrument).and_call_original
  end

  describe '.call' do
    before do
      stub_request(:get, "#{base_url}odata.svc/Loans(#{loan_id})?$expand=Customers")
        .to_return(headers: { content_type: 'application/json' }, body: { d: get_customers_response }.to_json)
      stub_request(:put, "#{base_url}odata.svc/Loans(#{loan_id})")
        .to_return(headers: { content_type: 'application/json' }, body: { d: process_new_payment_response }.to_json)
      stub_request(:get, "#{base_url}odata.svc/Customers(#{customer_id})/PaymentAccounts?$expand=CreditCard")
        .to_return(headers: { content_type: 'application/json' }, body: { d: get_payment_profiles_response }.to_json)
    end

    it 'makes a successful call with all params' do
      expect(subject).to eq(true)
    end

    it 'makes a successful call with only required params' do
      expect(described_class.call(full_debit_card_params)).to eq(true)
    end

    let(:create_debit_autopay) { described_class.new(debit_card_params) }

    context 'when making calls to the lp api' do
      it 'retrieves a customers id' do
        expect(create_debit_autopay.retrieve_customer_id).to eq(customer_id)
      end

      it 'creates a unique title' do
        expect(create_debit_autopay.unique_title).to eq(title)
      end

      it 'retrieves payment profile id' do
        create_debit_autopay.retrieve_payment_profile

        expect(create_debit_autopay.payment_profile_id).to eq(debit_card_id)
      end

      let(:process_new_payment_payload) do
        {
          payload: {
            'Payments' => {
              'results' => [
                {
                  'selectedProcessor' => 1,
                  'paymentMethodId' => 3,
                  'early' => 1,
                  'echeckAuthType' => 'payment.echeckauth.WEB',
                  'amount' => amount,
                  'date' => date,
                  'info' => title.to_s,
                  'paymentTypeId' => 1,
                  'active' => 1,
                  'resetPastDue' => 0,
                  '_saveProfile' => 1,
                  'extra' => 'payment.extra.tx.principalonly',
                  'paymentAccountId' => debit_card_id,
                  'chargeFeeType' => 'loan.cardfee.types.0',
                  'chargeFeeAmount' => 0,
                  'chargeFeePercentage' => 0
                }
              ]
            }
          },
          loan_id: loan_id
        }
      end

      it 'processes the customers payment' do
        expect(LoanProService::LoanManagementSystem::ProcessDebitCardPayment)
          .to receive(:call).with(process_new_payment_payload).and_return(process_new_payment_response)
        subject
      end
    end

    context 'when there is a lp error' do
      it 'raises custom internal server error and logs the error' do
        allow_any_instance_of(described_class).to receive(:process_new_payment).and_raise(LoanProService::Exceptions::LoanManagementSystemError, 'Loan Management System Error!')

        expect { described_class.call(debit_card_params) }.to raise_error(LoanManagement::CreateDebitCardAutopay::CreateDebitCardAutopaySystemError)
        expect_to_notify('create_debit_card_autopay', success: false, fail_reason: 'Loan Management System Error!')
      end

      it 'raises lp service loan mgmt system get loan customer id error, handles the errors, and logs the error' do
        allow(LoanProService::LoanManagementSystem::GetLoanCustomerId).to receive(:call).and_raise(LoanProService::Exceptions::LoanManagementSystemError, 'Loan Management System Error!')

        expect { described_class.call(debit_card_params) }.to raise_error(LoanManagement::CreateDebitCardAutopay::CreateDebitCardAutopaySystemError)
        expect_to_notify('create_debit_card_autopay', success: false, fail_reason: 'Loan Management System Error!')
      end

      it 'raises lp service loan mgmt system get card payment profile error, handles the errors, and logs the error' do
        allow(LoanProService::LoanManagementSystem::GetCardPaymentProfile).to receive(:call).and_raise(LoanProService::Exceptions::LoanManagementSystemError, 'Loan Management System Error!')

        expect { described_class.call(debit_card_params) }.to raise_error(LoanManagement::CreateDebitCardAutopay::CreateDebitCardAutopaySystemError)
        expect_to_notify('create_debit_card_autopay', success: false, fail_reason: 'Loan Management System Error!')
      end

      it 'raises lp service loan mgmt system process new payment error, handles the errors, and logs the error' do
        allow(LoanProService::LoanManagementSystem::PutRequest).to receive(:call).and_raise(LoanProService::Exceptions::LoanManagementSystemError, 'Loan Management System Error!')
        allow(Rails.logger).to receive(:error)

        expect(Rails.logger).to receive(:error).with(/ProcessDebitCardPayment - Failed to process payment for loan id:/)
        expect { described_class.call(debit_card_params) }.to raise_error(LoanManagement::CreateDebitCardAutopay::CreateDebitCardAutopaySystemError)
        expect_to_notify('create_debit_card_autopay', success: false, fail_reason: 'Loan Management System Error!')
      end
    end
  end
end
