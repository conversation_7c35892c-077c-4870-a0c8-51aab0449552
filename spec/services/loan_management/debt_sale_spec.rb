# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanManagement::DebtSale do
  describe LoanManagement::DebtSale do
    let(:csv_file) { double('csv_file', path: 'path/to/file.csv') }
    let(:debt_sale) { LoanManagement::DebtSale.new(csv_file) }
    let(:debt_sale_job) { double('LoanSaleJob', perform_async: nil) }

    let(:row) do
      {
        'loan_type' => 'loan_type',
        'loan_pro_loan_id' => 'loan_pro_loan_id',
        'subportfolio_id' => 'subportfolio_id',
        'sale_date' => 'sale_date',
        'bid_date' => 'bid_date',
        'sale_balance' => 'sale_balance',
        'sale_price' => 'sale_price',
        'template_key' => 'test_template_key'
      }
    end

    before do
      allow(CSV).to receive(:foreach).and_yield(row)
      allow(::LoanSaleJob).to receive(:perform_async)
    end

    describe '#initialize' do
      it 'initializes with a csv file' do
        expect(debt_sale.csv_file).to eq(csv_file)
      end
    end

    describe '#call' do
      it 'processes the debt sale for each row in the csv file' do
        expect(::LoanSaleJob).to receive(:perform_async).with(
          row['loan_type'],
          row['loan_pro_loan_id'],
          row['subportfolio_id'],
          row['sale_date'],
          row['bid_date'],
          row['sale_balance'],
          row['sale_price'],
          row['template_key']
        )
        debt_sale.call
      end
    end
  end
end
