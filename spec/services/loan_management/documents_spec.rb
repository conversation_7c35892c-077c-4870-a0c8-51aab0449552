# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanManagement::Documents do
  include NotifierHelper

  let(:loan1) { create(:above_lending_loan, :loanpro) }
  let(:doc_template1) { create(:above_lending_doc_template) }
  let(:borrower_id) { loan1.borrower.id }
  let(:documents_params) do
    {
      loan_id: loan1.id,
      borrower_id: borrower_id
    }
  end

  before do
    allow(ActiveSupport::Notifications).to receive(:instrument).and_call_original
  end

  it 'Returns an array of documents' do
    doc1 = AboveLending::Doc.create(id: SecureRandom.uuid, loan_id: loan1.id, template_id: doc_template1.id,
                                    name: 'ESIGN Act Consent_version_2_fname_lname_07a1e165-8b04-4853-b073-82e092fc5d96_.pdf',
                                    uri: 'signed-docs/ESIGN Act Consent_version_2_fname_lname_07a1e165-8b04-4853-b073-82e092fc5d96_.pdf',
                                    created_at: DateTime.yesterday)
    doc2 = AboveLending::Doc.create(id: SecureRandom.uuid, loan_id: loan1.id, template_id: doc_template1.id,
                                    name: 'CRB Truth in Lending_version_1_fname_lname_7cff361e-f687-4757-9d64-d48749a5d783.pdf',
                                    uri: 'signed-docs/CRB Truth in Lending_version_1_fname_lname_7cff361e-f687-4757-9d64-d48749a5d783.pdf',
                                    created_at: DateTime.yesterday)
    docs = [
      {
        uri: doc1.uri.to_s,
        name: doc1.name.to_s,
        type: doc_template1.type.to_s,
        created: doc1.created_at.to_s
      },
      {
        uri: doc2.uri.to_s,
        name: doc2.name.to_s,
        type: doc_template1.type.to_s,
        created: doc2.created_at.to_s
      }
    ]
    my_documents = described_class.new(borrower: loan1.borrower, loan_id: loan1.id)
    expect(my_documents.call).to eq(true)
    expect(my_documents.error).to eq(nil)
    expect(my_documents.docs).to eq(docs)
  end

  context 'when borrower does not exist in the database' do
    it 'should notify instrumentation and return false' do
      expect(described_class.call(borrower: nil, loan_id: loan1.id)).to eq(false)
      expect_to_notify('documents', success: false, fail_reason: 'Borrower not found')
    end
  end
end
