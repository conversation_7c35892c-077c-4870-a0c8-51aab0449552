# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanManagement::EditAutopay do
  include NotifierHelper

  let!(:loan) { create(:above_lending_loan, :loanpro) }
  let!(:loan_autopay_entity) { create(:loan_autopay_entity, above_lending_loanpro_loan: loan.loanpro_loan, loan_entity: loan.loan_pro_loan_entity) }
  let!(:borrower) { loan.borrower }
  let(:apply_date) { Date.tomorrow.iso8601 }
  let(:process_date) { Date.yesterday.iso8601 }
  let(:expected_autopay_params) do
    {
      processDateTime: "#{process_date} 19:00:00 UTC",
      applyDate: apply_date
    }
  end

  before do
    allow(ActiveSupport::Notifications).to receive(:instrument).and_call_original
  end

  describe '.call' do
    before do
      expect(LoanProService::LoanManagementSystem::EditAutopay)
        .to receive(:call).with(autopay_id: loan_autopay_entity.id, loan_id: loan_autopay_entity.loan_entity.id, attributes: expected_autopay_params).and_return(true)
    end

    it 'Updates a loan autopay record with the passed attributes' do
      described_class.call(autopay_id: loan_autopay_entity.id, apply_date: apply_date, process_datetime: process_date)
    end
  end

  context 'when autopay entity does not exist in the database' do
    it 'should notify instrumentation and return false' do
      expect(described_class.call(autopay_id: -1, apply_date: apply_date, process_datetime: process_date)).to eq(false)
      expect_to_notify('edit_autopay', success: false, fail_reason: 'Autopay -1 not found')
    end
  end

  context 'when the LP API raises an exception' do
    before do
      allow(LoanProService::LoanManagementSystem::EditAutopay).to receive(:call).and_raise(LoanProService::Exceptions::LoanManagementSystemError, 'Boom!')
    end

    let!(:loan) { create(:above_lending_loan, :loanpro) }
    it 'should notify instrumentation and return false' do
      expect(described_class.call(autopay_id: loan_autopay_entity.id, apply_date: apply_date, process_datetime: process_date)).to eq(false)
      expect_to_notify('edit_autopay', success: false, fail_reason: 'Boom!')
    end
  end
end
