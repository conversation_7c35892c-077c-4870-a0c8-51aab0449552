# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanManagement::GetLoanCustomers do
  include NotifierHelper

  let!(:loan) { create(:above_lending_loan, :loanpro) }
  let!(:borrower_id) { loan.borrower.id }
  let!(:loan_id) { loan.loanpro_loan.loanpro_loan_id }
  let(:customer1) do
    {
      customer_id: Faker::Number.number(digits: 5),
      first_name: Faker::Name.first_name,
      last_name: Faker::Name.last_name,
      email: Faker::Internet.email,
      loan_role: 'loan.customerRole.primary',
      phone_id: Faker::Number.number(digits: 4),
      phone: Faker::PhoneNumber.phone_number,
      isPrimary: 1,
      isSecondary: 0,
      address_id: Faker::Number.number(digits: 5),
      address1: Faker::Address.street_address,
      city: Faker::Address.city,
      state: Faker::Address.state_abbr,
      zipcode: Faker::Address.zip.first(5),
      country: 'company.country.usa'
    }
  end
  let(:customer2) do
    {
      customer_id: Faker::Number.number(digits: 5),
      first_name: Faker::Name.first_name,
      last_name: Faker::Name.last_name,
      email: Faker::Internet.email,
      loan_role: 'loan.customerRole.secondary',
      phone_id: Faker::Number.number(digits: 4),
      phone: Faker::PhoneNumber.phone_number,
      isPrimary: 1,
      isSecondary: 0,
      address_id: Faker::Number.number(digits: 5),
      address1: Faker::Address.street_address,
      city: Faker::Address.city,
      state: Faker::Address.state_abbr,
      zipcode: Faker::Address.zip.first(5),
      country: 'company.country.usa'
    }
  end
  let(:customer3) do
    {
      customer_id: Faker::Number.number(digits: 5),
      first_name: Faker::Name.first_name,
      last_name: Faker::Name.last_name,
      email: Faker::Internet.email,
      loan_role: 'loan.customerRole.additional',
      phone_id: Faker::Number.number(digits: 4),
      phone: Faker::PhoneNumber.phone_number,
      isPrimary: 1,
      isSecondary: 0,
      address_id: Faker::Number.number(digits: 5),
      address1: Faker::Address.street_address,
      city: Faker::Address.city,
      state: Faker::Address.state_abbr,
      zipcode: Faker::Address.zip.first(5),
      country: 'company.country.usa'
    }
  end
  let(:loan_pro_get_loan_customers_response) do
    { '__metadata' => { 'uri' => "http://loanpro.simnang.com/api/public/api/1/odata.svc/Loans(id=#{loan_id})", 'type' => 'Entity.Loan' },
      'Customers' => [{ '__metadata' => { 'uri' => "http://loanpro.simnang.com/api/public/api/1/odata.svc/Customers(id=#{customer1[:customer_id]})", 'type' => 'Entity.Customer' },
                        'id' => customer1[:customer_id],
                        'firstName' => customer1[:first_name],
                        'lastName' => customer1[:last_name],
                        'email' => customer1[:email],
                        'loanRole' => customer1[:loan_role] },
                      { '__metadata' => { 'uri' => "http://loanpro.simnang.com/api/public/api/1/odata.svc/Customers(id=#{customer2[:customer_id]})", 'type' => 'Entity.Customer' },
                        'id' => customer2[:customer_id],
                        'firstName' => customer2[:first_name],
                        'lastName' => customer2[:last_name],
                        'email' => customer2[:email],
                        'loanRole' => customer2[:loan_role] },
                      { '__metadata' => { 'uri' => "http://loanpro.simnang.com/api/public/api/1/odata.svc/Customers(id=#{customer3[:customer_id]})", 'type' => 'Entity.Customer' },
                        'id' => customer3[:customer_id],
                        'firstName' => customer3[:first_name],
                        'lastName' => customer3[:last_name],
                        'email' => customer3[:email],
                        'loanRole' => customer3[:loan_role] }],
      'id' => loan_id }
  end
  let(:loan_pro_get_customer1_response) do
    {
      '__metadata' => { 'uri' => "http://loanpro.simnang.com/api/public/api/1/odata.svc/Customers(id=#{customer1[:customer_id]})", 'type' => 'Entity.Customer' },
      'MailAddress' =>
        { '__metadata' => { 'uri' => "http://loanpro.simnang.com/api/public/api/1/odata.svc/Address(id=#{customer1[:address_id]})", 'type' => 'Entity.Address' },
          'id' => customer1[:address_id],
          'address1' => customer1[:address1],
          'city' => customer1[:city],
          'state' => customer1[:state],
          'zipcode' => customer1[:zipcode],
          'country' => customer1[:country] },
      'Phones' => [{ '__metadata' => { 'uri' => "http://loanpro.simnang.com/api/public/api/1/odata.svc/Phones(id=#{customer1[:phone_id]})", 'type' => 'Entity.Phone' },
                     'id' => customer1[:phone_id],
                     'entityId' => customer1[:id],
                     'phone' => customer1[:phone],
                     'isPrimary' => 1,
                     'isSecondary' => 0 }],
      'id' => customer1[:customer_id],
      'firstName' => customer1[:first_name],
      'lastName' => customer1[:last_name],
      'email' => customer1[:email]
    }
  end
  let(:loan_pro_get_customer2_response) do
    {
      '__metadata' => { 'uri' => "http://loanpro.simnang.com/api/public/api/1/odata.svc/Customers(id=#{customer2[:customer_id]})", 'type' => 'Entity.Customer' },
      'MailAddress' =>
        { '__metadata' => { 'uri' => "http://loanpro.simnang.com/api/public/api/1/odata.svc/Address(id=#{customer2[:address_id]})", 'type' => 'Entity.Address' },
          'id' => customer2[:address_id],
          'address1' => customer2[:address1],
          'city' => customer2[:city],
          'state' => customer2[:state],
          'zipcode' => customer2[:zipcode],
          'country' => customer2[:country] },
      'Phones' =>
        [{ '__metadata' => { 'uri' => "http://loanpro.simnang.com/api/public/api/1/odata.svc/Phones(id=#{customer2[:phone_id]})", 'type' => 'Entity.Phone' },
           'id' => customer2[:phone_id],
           'entityId' => customer2[:id],
           'phone' => customer2[:phone],
           'isPrimary' => 1,
           'isSecondary' => 0 }],
      'id' => customer2[:customer_id],
      'firstName' => customer2[:first_name],
      'lastName' => customer2[:last_name],
      'email' => customer2[:email]
    }
  end
  let(:loan_pro_get_customer3_response) do
    {
      '__metadata' => { 'uri' => "http://loanpro.simnang.com/api/public/api/1/odata.svc/Customers(id=#{customer3[:customer_id]})", 'type' => 'Entity.Customer' },
      'MailAddress' =>
        { '__metadata' => { 'uri' => "http://loanpro.simnang.com/api/public/api/1/odata.svc/Address(id=#{customer3[:address_id]})", 'type' => 'Entity.Address' },
          'id' => customer3[:address_id],
          'address1' => customer3[:address1],
          'city' => customer3[:city],
          'state' => customer3[:state],
          'zipcode' => customer3[:zipcode],
          'country' => customer3[:country] },
      'Phones' =>
        [{ '__metadata' => { 'uri' => "http://loanpro.simnang.com/api/public/api/1/odata.svc/Phones(id=#{customer3[:phone_id]})", 'type' => 'Entity.Phone' },
           'id' => customer3[:phone_id],
           'entityId' => customer3[:id],
           'phone' => customer3[:phone],
           'isPrimary' => 1,
           'isSecondary' => 0 }],
      'id' => customer3[:customer_id],
      'firstName' => customer3[:first_name],
      'lastName' => customer3[:last_name],
      'email' => customer3[:email]
    }
  end
  let(:get_customers_response) do
    [
      {
        'id' => customer1[:customer_id],
        'firstName' => customer1[:first_name],
        'lastName' => customer1[:last_name],
        'email' => customer1[:email],
        'customerRole' => 'primary',
        'mail_address' => {
          'address1' => customer1[:address1],
          'city' => customer1[:city],
          'state' => customer1[:state],
          'zipcode' => customer1[:zipcode],
          'country' => customer1[:country]
        },
        'phones' => {
          'id' => customer1[:phone_id],
          'phone' => customer1[:phone],
          'isPrimary' => 1,
          'isSecondary' => 0
        }
      },
      {
        'id' => customer2[:customer_id],
        'firstName' => customer2[:first_name],
        'lastName' => customer2[:last_name],
        'email' => customer2[:email],
        'customerRole' => 'secondary',
        'mail_address' => {
          'address1' => customer2[:address1],
          'city' => customer2[:city],
          'state' => customer2[:state],
          'zipcode' => customer2[:zipcode],
          'country' => customer2[:country]
        },
        'phones' => {
          'id' => customer2[:phone_id],
          'phone' => customer2[:phone],
          'isPrimary' => 1,
          'isSecondary' => 0
        }
      },
      {
        'id' => customer3[:customer_id],
        'firstName' => customer3[:first_name],
        'lastName' => customer3[:last_name],
        'email' => customer3[:email],
        'customerRole' => 'additional',
        'mail_address' => {
          'address1' => customer3[:address1],
          'city' => customer3[:city],
          'state' => customer3[:state],
          'zipcode' => customer3[:zipcode],
          'country' => customer3[:country]
        },
        'phones' => {
          'id' => customer3[:phone_id],
          'phone' => customer3[:phone],
          'isPrimary' => 1,
          'isSecondary' => 0
        }
      }
    ]
  end
  let(:base_url) { LoanProService::ConfigHelper.configuration.loan_management_system_url }
  before do
    allow(ActiveSupport::Notifications).to receive(:instrument).and_call_original
  end

  describe '.call' do
    it 'makes a successful call' do
      stub_request(:get, "#{base_url}odata.svc/Loans(#{loan_id})?$expand=Customers&nopaging=true")
        .to_return(headers: { content_type: 'application/json' }, body: { d: loan_pro_get_loan_customers_response }.to_json)
      stub_request(:get, "#{base_url}odata.svc/Customers(#{customer1[:customer_id]})?$expand=MailAddress,Phones&nopaging=true")
        .to_return(headers: { content_type: 'application/json' }, body: { d: loan_pro_get_customer1_response }.to_json)
      stub_request(:get, "#{base_url}odata.svc/Customers(#{customer2[:customer_id]})?$expand=MailAddress,Phones&nopaging=true")
        .to_return(headers: { content_type: 'application/json' }, body: { d: loan_pro_get_customer2_response }.to_json)
      stub_request(:get, "#{base_url}odata.svc/Customers(#{customer3[:customer_id]})?$expand=MailAddress,Phones&nopaging=true")
        .to_return(headers: { content_type: 'application/json' }, body: { d: loan_pro_get_customer3_response }.to_json)
      expect(described_class.call(loan_id)).to eq(get_customers_response)
    end

    context 'when making calls to the lp api' do
      it 'gets loan customers details' do
        stub_request(:get, "#{base_url}odata.svc/Customers(#{customer1[:customer_id]})?$expand=MailAddress,Phones&nopaging=true")
          .to_return(headers: { content_type: 'application/json' }, body: { d: loan_pro_get_customer1_response }.to_json)
        stub_request(:get, "#{base_url}odata.svc/Customers(#{customer2[:customer_id]})?$expand=MailAddress,Phones&nopaging=true")
          .to_return(headers: { content_type: 'application/json' }, body: { d: loan_pro_get_customer2_response }.to_json)
        stub_request(:get, "#{base_url}odata.svc/Customers(#{customer3[:customer_id]})?$expand=MailAddress,Phones&nopaging=true")
          .to_return(headers: { content_type: 'application/json' }, body: { d: loan_pro_get_customer3_response }.to_json)

        expect(LoanProService::LoanManagementSystem::GetLoanCustomers).to receive(:call).once
                                                                                        .with(loan_id)
                                                                                        .and_return(loan_pro_get_loan_customers_response['Customers'])
        described_class.call(loan_id)
      end

      it 'gets customer details' do
        loan_pro_get_loan_customer1_response =
          { '__metadata' => { 'uri' => "http://loanpro.simnang.com/api/public/api/1/odata.svc/Loans(id=#{loan_id})", 'type' => 'Entity.Loan' },
            'Customers' =>
              [{ '__metadata' => { 'uri' => "http://loanpro.simnang.com/api/public/api/1/odata.svc/Customers(id=#{customer1[:customer_id]})", 'type' => 'Entity.Customer' },
                 'id' => customer1[:customer_id],
                 'firstName' => customer1[:first_name],
                 'lastName' => customer1[:last_name],
                 'email' => customer1[:email],
                 'loanRole' => customer1[:loan_role] }],
            'id' => loan_id }

        stub_request(:get, "#{base_url}odata.svc/Loans(#{loan_id})?$expand=Customers&nopaging=true")
          .to_return(headers: { content_type: 'application/json' }, body: { d: loan_pro_get_loan_customer1_response }.to_json)

        expect(LoanProService::LoanManagementSystem::GetCustomer).to receive(:call).once
                                                                                   .with(customer1[:customer_id], expand: 'MailAddress,Phones')
                                                                                   .and_return(loan_pro_get_customer1_response)
        described_class.call(loan_id)
      end
    end

    context 'when there is a lp error' do
      it 'raises a GetLoanCustomersSystemError and notifies' do
        allow(LoanProService::LoanManagementSystem::GetLoanCustomers).to receive(:call).and_raise(LoanProService::Exceptions::LoanManagementSystemError, 'Loan Management System Error!')

        expect do
          described_class.call(loan_id)
        end.to raise_error(LoanManagement::GetLoanCustomers::GetLoanCustomersSystemError, 'Internal Server Error')
        expect_to_notify('get_loan_customers', success: false, fail_reason: 'Loan Management System Error!')
      end

      it 'raises a GetCustomerSystemError and notifies' do
        stub_request(:get, "#{base_url}odata.svc/Loans(#{loan_id})?$expand=Customers&nopaging=true")
          .to_return(headers: { content_type: 'application/json' }, body: { d: loan_pro_get_loan_customers_response }.to_json)

        allow(LoanProService::LoanManagementSystem::GetCustomer).to receive(:call).and_raise(LoanProService::Exceptions::LoanManagementSystemError, 'Loan Management System Error!')

        expect do
          described_class.call(loan_id)
        end.to raise_error(LoanManagement::GetLoanCustomers::GetLoanCustomersSystemError, 'Internal Server Error')
        expect_to_notify('get_loan_customers', success: false, fail_reason: 'Loan Management System Error!')
      end
    end
  end
end
