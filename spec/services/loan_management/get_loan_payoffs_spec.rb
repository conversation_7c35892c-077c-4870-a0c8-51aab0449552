# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanManagement::GetLoanPayoffs do
  include NotifierHelper
  include ActiveSupport::Testing::TimeHelpers

  let(:loan) { create(:above_lending_loan, :loanpro) }
  let(:api_response) do
    {
      '2023-11-01' => {
        'date' => '2023-11-01',
        'payoff' => 12_988.89,
        'change' => 1.34,
        'dailyInterest' => 1.3404980794520542,
        'details' => {
          'principal' => 12_909.809999999994,
          'fees' => 0,
          'interest' => 79.08444430410965,
          'escrow' => 0,
          'escrowAlt' => 0,
          'totalEscrow' => 0,
          'escrowSubsets' => {
            '2' => { 'escrow' => 0, 'escrowAlt' => 0, 'totalEscrow' => 0 },
            '3' => { 'escrow' => 0, 'escrowAlt' => 0, 'totalEscrow' => 0 },
            '4' => { 'escrow' => 0, 'escrowAlt' => 0, 'totalEscrow' => 0 },
            '5' => { 'escrow' => 0, 'escrowAlt' => 0, 'totalEscrow' => 0 }
          },
          'finalInterest' => 0,
          'paymentsPending' => 0
        }
      }
    }
  end

  before do
    allow(ActiveSupport::Notifications).to receive(:instrument).and_call_original
    travel_to('2023-11-01T12:00:00'.to_datetime)
  end

  describe '.call' do
    it 'Returns an array of pending payments' do
      expect(LoanProService::LoanManagementSystem::GetLoanPayoffs)
        .to receive(:call).with(loanpro_loan_id: loan.loan_pro_loan_entity.id, start_date: '2023-11-02').and_return(api_response)

      result = described_class.call(loanpro_loan_id: loan.loan_pro_loan_entity.id, start_date: '2023-11-02')
      expect(result).to eq(api_response)
    end

    it 'Defaults start date to today if given nil' do
      expect(LoanProService::LoanManagementSystem::GetLoanPayoffs)
        .to receive(:call).with(loanpro_loan_id: loan.loan_pro_loan_entity.id, start_date: '2023-11-01').and_return(api_response)

      result = described_class.call(loanpro_loan_id: loan.loan_pro_loan_entity.id, start_date: nil)
      expect(result).to eq(api_response)
    end
  end

  context 'when the LP API raises an exception' do
    before do
      allow(LoanProService::LoanManagementSystem::GetLoanPayoffs).to receive(:call).and_raise(LoanProService::Exceptions::LoanManagementSystemError, 'Boom!')
    end

    it 'should notify instrumentation and raise' do
      expect do
        described_class.call(loanpro_loan_id: loan.loan_pro_loan_entity.id, start_date: '2023-11-02')
      end.to raise_error(LoanManagement::GetLoanPayoffs::GetLoanPayoffsSystemError)

      expect_to_notify('get_loan_payoffs', success: false, fail_reason: 'Boom!')
    end
  end
end
