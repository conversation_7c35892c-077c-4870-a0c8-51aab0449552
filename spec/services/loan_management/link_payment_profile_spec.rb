# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanManagement::LinkPaymentProfile do
  include NotifierHelper

  let!(:loan) { create(:above_lending_loan, :loanpro) }
  let!(:borrower_id) { loan.borrower.id }
  let!(:loan_id) { loan.loanpro_loan.loanpro_loan_id }
  let(:payment_profile_token) { Faker::Alphanumeric.alphanumeric(number: 20) }
  let(:date) { Date.today.iso8601 }
  let(:amount) { Faker::Number.decimal(l_digits: 2, r_digits: 2) }
  let!(:first_name) { Faker::Name.first_name }
  let!(:account_number_last4) { Faker::Bank.account_number[-4..] }
  let!(:customer_id) { Faker::Number.number(digits: 5) }
  let!(:title) { "Link Payment Profile for Debit Card Payment on #{date.to_datetime}" }
  let!(:get_customers_response) do
    { 'Customers' => {
      'results' => [
        { 'id' => customer_id }
      ]
    } }
  end
  let(:debit_card_params) do
    {
      token: payment_profile_token,
      loan_id: loan_id,
      date: date
    }
  end
  let(:full_debit_card_params) do
    {
      token: payment_profile_token,
      loan_id: loan_id,
      amount: amount,
      date: date,
      firstName: first_name,
      accountNumberLast4Digits: account_number_last4
    }
  end
  let(:debit_card_id) { Faker::Number.number(digits: 5) }
  let(:checking_id) { debit_card_id - 1 }
  let(:link_payment_profile_response) do
    {
      'id' => customer_id,
      'mcId' => 3_609_465,
      'status' => 'Active',
      'firstName' => 'John',
      'lastName' => 'Doe',
      'active' => 1,
      'created' => '/Date(**********)/',
      'lastUpdate' => '/Date(**********)/',
      'PaymentAccounts' => {
        'results' => [
          {
            '__metadata' => {
              'uri' => "http =>//loanpro.simnang.com/api/public/api/1/odata.svc/PaymentAccounts(id=#{checking_id})",
              'type' => 'Entity.PaymentAccount'
            },
            'id' => checking_id,
            'entityId' => customer_id,
            'entityType' => 'Entity.Customer',
            'isPrimary' => 0,
            'isSecondary' => 1,
            'title' => 'Checking',
            'type' => 'paymentAccount.type.checking',
            'creditCardId' => 0,
            'checkingAccountId' => 360,
            'active' => 1,
            'visible' => 1,
            'verify' => 0,
            'CheckingAccount' => { '__deferred' => { 'uri' => "PaymentAccounts(#{checking_id})/CheckingAccount" } },
            'CreditCard' => { '__deferred' => { 'uri' => "PaymentAccounts(#{checking_id})/CreditCard" } }
          },
          {
            '__metadata' => {
              'uri' => "http =>//loanpro.simnang.com/api/public/api/1/odata.svc/PaymentAccounts(id=#{debit_card_id})",
              'type' => 'Entity.PaymentAccount'
            },
            'id' => debit_card_id,
            'entityId' => customer_id,
            'entityType' => 'Entity.Customer',
            'isPrimary' => 1,
            'isSecondary' => 0,
            'title' => title,
            'type' => 'paymentAccount.type.credit',
            'creditCardId' => 46,
            'checkingAccountId' => 0,
            'active' => 1,
            'visible' => 1,
            'verify' => 0,
            'CheckingAccount' => { '__deferred' => { 'uri' => "PaymentAccounts(#{debit_card_id})/CheckingAccount" } },
            'CreditCard' => { '__deferred' => { 'uri' => "PaymentAccounts(#{debit_card_id})/CreditCard" } }
          }
        ]
      }
    }
  end
  let(:link_payment_profile_payload) do
    {
      'PaymentAccounts' => {
        'results' => [
          {
            'active' => 1,
            'isPrimary' => 0,
            'isSecondary' => 0,
            'title' => title,
            'type' => 'paymentAccount.type.credit',
            'CreditCard' => {
              'token' => payment_profile_token.to_s
            }
          }
        ]
      }
    }
  end
  subject { described_class.call(debit_card_params) }
  let(:base_url) { LoanProService::ConfigHelper.configuration.loan_management_system_url }
  before do
    allow(ActiveSupport::Notifications).to receive(:instrument).and_call_original
  end

  describe '.call' do
    before do
      stub_request(:put, "#{base_url}odata.svc/Customers(#{customer_id})")
        .to_return(headers: { content_type: 'application/json' }, body: { d: link_payment_profile_payload }.to_json)
      stub_request(:get, "#{base_url}odata.svc/Loans(#{loan_id})?$expand=Customers")
        .to_return(headers: { content_type: 'application/json' }, body: { d: get_customers_response }.to_json)
    end

    it 'makes a successful call with all params' do
      expect(subject).to eq(true)
    end

    it 'makes a successful call with only required params' do
      expect(described_class.call(full_debit_card_params)).to eq(true)
    end

    let(:link_payment_profile) { described_class.new(debit_card_params) }

    context 'when making calls to the lp api' do
      it 'retrieves a customers id' do
        expect(link_payment_profile.retrieve_customer_id).to eq(customer_id)
      end

      it 'creates a unique title' do
        expect(link_payment_profile.unique_title).to eq(title)
      end

      let(:link_payment_profile_payload) do
        {
          payload: {
            'PaymentAccounts' => {
              'results' => [
                {
                  'CreditCard' => { 'token' => payment_profile_token.to_s },
                  'active' => 1,
                  'isPrimary' => 0,
                  'isSecondary' => 0,
                  'title' => title,
                  'type' => 'paymentAccount.type.credit'
                }
              ]
            }
          },
          customer_id: customer_id
        }
      end

      it 'links the payment profile to the customer' do
        expect(LoanProService::LoanManagementSystem::LinkPaymentProfile)
          .to receive(:call).with(link_payment_profile_payload).and_return(link_payment_profile_response)
        subject
      end
    end

    context 'when there is a lp error' do
      it 'raises custom internal server error and logs the error' do
        allow_any_instance_of(described_class).to receive(:link_payment_profile).and_raise(LoanProService::Exceptions::LoanManagementSystemError, 'Loan Management System Error!')

        expect { described_class.call(debit_card_params) }.to raise_error(LoanManagement::LinkPaymentProfile::LinkPaymentProfileSystemError)
        expect_to_notify('link_payment_profile', success: false, fail_reason: 'Loan Management System Error!')
      end

      it 'raises lp service loan mgmt system get loan customer id error, handles the errors, and logs the error' do
        allow(LoanProService::LoanManagementSystem::GetLoanCustomerId).to receive(:call).and_raise(LoanProService::Exceptions::LoanManagementSystemError, 'Loan Management System Error!')

        expect { described_class.call(debit_card_params) }.to raise_error(LoanManagement::LinkPaymentProfile::LinkPaymentProfileSystemError)
        expect_to_notify('link_payment_profile', success: false, fail_reason: 'Loan Management System Error!')
      end

      it 'raises lp service loan mgmt system put request error, handles the errors, and logs the errors' do
        allow(LoanProService::LoanManagementSystem::PutRequest).to receive(:call).and_raise(LoanProService::Exceptions::LoanManagementSystemError, 'Loan Management System Error!')
        allow(Rails.logger).to receive(:error)

        expect(Rails.logger).to receive(:error).with(/LinkPaymentProfile - Failed to link payment profile to customer id:/)
        expect { described_class.call(debit_card_params) }.to raise_error(LoanManagement::LinkPaymentProfile::LinkPaymentProfileSystemError)
        expect_to_notify('link_payment_profile', success: false, fail_reason: 'Loan Management System Error!')
      end
    end
  end
end
