# frozen_string_literal: true

require 'rails_helper'
require 'support/shared/loanpro_data_common'

RSpec.describe LoanManagement::LoanBuilder do
  include NotifierHelper

  include_context 'loanpro_data_common'

  let(:unified_id) { 'AL0112345678' }

  let(:params) do
    contract_date = [Date.today, Date.tomorrow].sample

    {
      amount_financed: rand(2500..10_000),
      cashout_amount: [0, 500, 1000, 1500, 2000].sample,
      contract_date: contract_date.as_json,
      first_payment_date: (contract_date + 1.month).as_json,
      interest_rate: rand(5.0..25.0),
      number_of_payments: rand(36..72),
      origination_fee: rand(100..250),
      payment_frequency: LoanProHelpers::ABOVE_TO_LOAN_PRO_PAYMENT_FREQUENCIES.keys.sample,
      settlement_amount: rand(2500..10_000),
      request_id: SecureRandom.uuid,
      unified_id:,
      loanpro_data:,
      product_type: 'IPL'
    }
  end

  let(:loan_pro_create_loan_data) do
    {
      id: 13_859,
      displayId: unified_id,
      setupId: 13_810,
      LoanSetup: {
        id: 13_810,
        loanId: 13_859,
        active: 0,
        apr: 0,
        origFinalPaymentDate: '/Date(-62169984000)/',
        paymentFrequency: 'loan.frequency.monthly',
        tilFinanceCharge: 0,
        tilPaymentSchedule: '',
        isSetupValid: false
      }
    }.as_json
  end
  let(:loan_pro_get_loan_data) do
    {
      id: 13_859,
      displayId: unified_id,
      setupId: 13_810,
      LoanSetup: {
        id: 13_810,
        loanId: 13_859,
        active: 0,
        apr: '20.6167',
        origFinalPaymentDate: '/Date(1758326400)/',
        firstPaymentDate: '/Date(1667174400)/',
        tilTotalOfPayments: '1119.0000',
        paymentFrequency: 'loan.frequency.monthly',
        tilFinanceCharge: '350.00',
        tilPaymentSchedule: "[{\"count\":35,\"payment\":37.5,\"startDate\":\"10\/20\/2022\"},{\"count\":1,\"payment\":37.49,\"startDate\":\"09\/20\/2025\"}]",
        isSetupValid: true
      }
    }.as_json
  end

  describe '.call' do
    before do
      create(:above_lending_loan_app_status, name: described_class::INITAL_LOAN_STATUS)

      allow(LoanProService::LoanManagementSystem::CreateLoan).to receive(:call).and_return(loan_pro_create_loan_data)
      allow(LoanProService::LoanManagementSystem::GetLoan).to receive(:call).and_return(loan_pro_get_loan_data)
      allow(ActiveSupport::Notifications).to receive(:instrument).and_call_original
    end

    it 'creates a Loan record in the Above Lending DB with the appropriate initial attributes' do
      expect do
        described_class.call(params: params)
      end.to change { AboveLending::Loan.count }.by(1)

      new_loan = AboveLending::Loan.order(created_at: :desc).first

      expect(new_loan.id).to be_present
      expect(new_loan.unified_id).to eq(params[:unified_id])
      expect(new_loan.request_id).to eq(params[:request_id])
      expect(new_loan.amount).to eq(params[:settlement_amount])
      expect(new_loan.purpose).to eq(described_class::PURPOSE)
      expect(new_loan.loan_app_status.name).to eq(described_class::INITAL_LOAN_STATUS)
      expect(new_loan.product_type).to eq(described_class::PRODUCT_TYPE)
      expect(new_loan.source_type).to eq(described_class::SOURCE_TYPE)
    end

    it 'creates a Loan record in the Above Lending DB with the provided source_type' do
      params[:source_type] = 'BEYOND'
      expect do
        described_class.call(params: params)
      end.to change { AboveLending::Loan.count }.by(1)

      new_loan = AboveLending::Loan.order(created_at: :desc).first

      expect(new_loan.id).to be_present
      expect(new_loan.source_type).to eq(params[:source_type])
    end

    it 'creates a Loan record in the database with expected initial attributes' do
      expect do
        described_class.call(params: params)
      end.to change { Loan.count }.by(1)

      new_loan = Loan.order(created_at: :desc).first
      above_lending_loan = AboveLending::Loan.order(created_at: :desc).first

      expect(new_loan.id).to be_present
      expect(new_loan.loan_number.to_s).to eq(above_lending_loan.unified_id)
      expect(new_loan.request_id).to eq(params[:request_id])
      expect(new_loan.product_type).to eq(params[:product_type])
      expect(new_loan.detail_info.apr).to eq(loan_pro_get_loan_data['LoanSetup']['apr'].to_d)
      expect(new_loan.detail_info.cashback_amount).to eq(params[:cashout_amount])
      expect(new_loan.detail_info.payment_frequency).to eq(params[:payment_frequency])
      expect(new_loan.detail_info.final_payment_amount).to eq(37.49)
      expect(new_loan.detail_info.final_payment_on).to eq('2025-09-20'.to_date)
      expect(new_loan.detail_info.finance_charge_amount).to eq(350)
      expect(new_loan.detail_info.first_payment_on).to eq('2022-10-31'.to_date)
      expect(new_loan.detail_info.interest_rate).to eq(params[:interest_rate])
      expect(new_loan.detail_info.payment_amount).to eq(37.5)
      expect(new_loan.detail_info.principal_amount).to eq(params[:amount_financed] + params[:origination_fee])
      expect(new_loan.detail_info.purpose).to eq(described_class::PURPOSE)
      expect(new_loan.detail_info.term).to eq(params[:number_of_payments])
      expect(new_loan.detail_info.total_payments_amount).to eq(1119)
      expect(new_loan.platform_info.external_loan_id).to eq(loan_pro_get_loan_data['id'].to_s)
      expect(new_loan.platform_info.external_raw_data).to eq(loan_pro_get_loan_data)
    end

    it 'allows purpose to be specified' do
      params[:purpose] = 'credit_card_refinancing'

      expect do
        described_class.call(params: params)
      end.to change { Loan.count }.by(1)

      new_loan = Loan.order(created_at: :desc).first
      expect(new_loan.detail_info.purpose).to eq('credit_card_refinancing')
    end

    it 'populates the request_id attribute for both the Dash and AboveLending Loan record with the Unified ID if no value is specified explicitly' do
      described_class.call(params: params.except(:request_id))

      dash_loan = Loan.order(created_at: :desc).first
      expect(dash_loan.id).to be_present
      expect(dash_loan.request_id).to eq(params[:unified_id])

      above_lending_loan = AboveLending::Loan.order(created_at: :desc).first
      expect(above_lending_loan.id).to be_present
      expect(above_lending_loan.request_id).to eq(params[:unified_id])
    end

    it 'calls the LoanPro API to create a new record for the loan in their system' do
      params[:payment_frequency] = 'semi_monthly'

      expected_loanpro_loan_data = {
        displayId: unified_id,
        'LoanSetup' => {
          contractDate: params[:contract_date],
          firstPaymentDate: params[:first_payment_date],
          loanAmount: params[:amount_financed],
          loanRate: params[:interest_rate],
          loanTerm: params[:number_of_payments],
          paymentFrequency: 'loan.frequency.semiMonthly',
          underwriting: params[:origination_fee]
        }
      }

      expect(LoanProService::LoanManagementSystem::CreateLoan).to receive(:call).with(attributes: expected_loanpro_loan_data).and_return(loan_pro_create_loan_data)

      described_class.call(params: params)
    end

    it 'defaults the payment frequency to monthly if no payment_frequency value is specified' do
      expect(LoanProService::LoanManagementSystem::CreateLoan).to receive(:call) do |attributes:|
        expect(attributes['LoanSetup'][:paymentFrequency]).to eq('loan.frequency.monthly')

        loan_pro_create_loan_data
      end

      described_class.call(params: params.merge(payment_frequency: nil))
    end

    it 'calls the LoanPro API to poll for the loan record\'s LoanSetup data to be initialized' do
      uninitialized_loan_pro_get_loan_data = {
        id: loan_pro_get_loan_data['id'],
        LoanSetup: { isSetupValid: false }
      }.as_json

      expect(LoanProService::LoanManagementSystem::GetLoan).to receive(:call).twice.and_return(uninitialized_loan_pro_get_loan_data, loan_pro_get_loan_data)

      loan_builder = described_class.call(params: params)

      expect(loan_builder.loan_pro_loan_data).to eq(loan_pro_get_loan_data)
    end

    it 'runs at most two polling attempts before continuing with an empty loan pro dataset' do
      uninitialized_loan_pro_get_loan_data = {
        id: loan_pro_get_loan_data['id'],
        LoanSetup: { isSetupValid: false }
      }.as_json

      expect(LoanProService::LoanManagementSystem::GetLoan).to receive(:call).exactly(4).times.and_return(uninitialized_loan_pro_get_loan_data)

      loan_builder = described_class.call(params: params)

      expect(loan_builder.loan_pro_loan_data).to eq(uninitialized_loan_pro_get_loan_data)
    end

    it 'records service attributes in an event' do
      described_class.call(params: params)
      expect_to_notify('LoanBuilder', success: true,
                                      extra: { above_lending_loan_id: String, loan_id: String, loan_pro_loan_id: Integer, request_id: params[:request_id], unified_id: params[:unified_id] })
    end

    context 'when the loan record is already present in database' do
      let(:loan) { create(:loan, loan_number: unified_id) }
      let!(:funding) { create(:funding, loan: loan, detail_info: funding_detail) }
      let(:funding_detail) { build(:funding_detail, funding_status: funding_status) }
      let(:funding_status) { Funding::Detail::SETTLED }

      it 'updates the Loan record in the database with the passed attributes' do
        expect do
          described_class.call(params: params)
        end.to change { Loan.count }.by(0)

        loan.reload
        expect(loan.request_id).to eq(params[:request_id])
        expect(loan.product_type).to eq(params[:product_type])
        expect(loan.detail_info.apr).to eq(loan_pro_get_loan_data['LoanSetup']['apr'].to_d)
        expect(loan.detail_info.cashback_amount).to eq(params[:cashout_amount])
        expect(loan.detail_info.payment_frequency).to eq(params[:payment_frequency])
        expect(loan.detail_info.final_payment_amount).to eq(37.49)
        expect(loan.detail_info.final_payment_on).to eq('2025-09-20'.to_date)
        expect(loan.detail_info.finance_charge_amount).to eq(350)
        expect(loan.detail_info.first_payment_on).to eq('2022-10-31'.to_date)
        expect(loan.detail_info.interest_rate).to eq(params[:interest_rate])
        expect(loan.detail_info.payment_amount).to eq(37.5)
        expect(loan.detail_info.principal_amount).to eq(params[:amount_financed] + params[:origination_fee])
        expect(loan.detail_info.purpose).to eq(described_class::PURPOSE)
        expect(loan.detail_info.term).to eq(params[:number_of_payments])
        expect(loan.detail_info.total_payments_amount).to eq(1119)
        expect(loan.platform_info.external_loan_id).to eq(loan_pro_get_loan_data['id'].to_s)
        expect(loan.platform_info.external_raw_data).to eq(loan_pro_get_loan_data)
      end

      context 'when the loan is already processed by arix' do
        let(:funding_status) { Funding::Detail::COMPLIANCE_FAILED }
        let(:error_message) { "ApplicationServiceBase::LoanAlreadyProcessed: Loan Unified ID ##{params[:unified_id]} is already processed by arix" }

        it 'raises an error' do
          expect do
            expect { described_class.call(params: params) }.to raise_error(ApplicationServiceBase::LoanAlreadyProcessed)
          end.to not_change { Loan.all.size }
            .and not_change { AboveLending::LoanproLoan.all.size }
            .and not_change { AboveLending::Loan.all.size }

          expect_to_notify('LoanBuilder', success: false, fail_reason: error_message, extra: { request_id: params[:request_id], unified_id: params[:unified_id], funding_status: })
        end
      end
    end

    describe 'rollbacks' do
      it 'persists nothing if AboveLending::Loan record creation fails' do
        expect(AboveLending::Loan).to receive(:create!).and_raise(StandardError.new('Test Error'))

        expect(LoanProService::LoanManagementSystem::CreateLoan).not_to receive(:call)

        expect do
          expect { described_class.call(params: params) }.to raise_error(StandardError)
        end.to not_change { Loan.all.size }
          .and not_change { AboveLending::LoanproLoan.all.size }
          .and not_change { AboveLending::Loan.all.size }
      end

      it 'rolls back the AboveLending::Loan record creation if Loan raises' do
        expect(Loan).to receive(:find_or_initialize_by).and_raise(StandardError.new('Test Error'))

        expect(LoanProService::LoanManagementSystem::CreateLoan).to receive(:call)

        expect do
          expect do
            expect { described_class.call(params: params) }.to raise_error(StandardError)
          end.not_to(change { Loan.count })
        end.not_to(change { AboveLending::Loan.count })
      end

      it 'rolls back the AboveLending::Loan and Loan record creation if the call to LoanPro fails' do
        expect(LoanProService::LoanManagementSystem::CreateLoan).to receive(:call).and_raise(StandardError.new('Test Error'))

        expect do
          expect do
            expect { described_class.call(params: params) }.to raise_error(StandardError)
          end.not_to(change { Loan.count })
        end.not_to(change { AboveLending::Loan.count })
      end
    end

    it 'logs/reports a detailed error if the LoanPro operations fails' do
      allow(ActiveSupport::Notifications).to receive(:instrument)

      loan_pro_operation_data = { response: { status: 500, body: 'Test Error', request: {} } }

      expect(LoanProService::LoanManagementSystem::CreateLoan).to receive(:call).and_raise(LoanProService::Exceptions::LoanManagementSystemError.new(loan_pro_operation_data))
      expect(Rails.logger).to receive(:error).at_least(:once)
      expect { described_class.call(params: params) }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError)

      expect_to_notify('loan_pro_create_loan_error', extra: { loan_pro_data: loan_pro_operation_data })
    end

    it 'creates a LoanproLoan record in the Above Lending DB with the appropriate initial attributes' do
      expect do
        described_class.call(params: params)
      end.to change { AboveLending::LoanproLoan.all.size }.by(1)

      new_loanpro_loan = AboveLending::LoanproLoan.order(created_at: :desc).select(AboveLending::LoanproLoan.attribute_names).first

      expect(new_loanpro_loan.id).to be_present
      expect(new_loanpro_loan.display_id).to eq(unified_id)
      expect(new_loanpro_loan.loan_id).to eq(AboveLending::Loan.order(created_at: :desc).first.id)
      expect(new_loanpro_loan.loanpro_loan_id).to eq(loan_pro_get_loan_data['id'].to_s)
      expect(new_loanpro_loan.loanpro_raw_response).to eq(loan_pro_get_loan_data.to_json)
    end

    describe 'process_loan' do
      before do
        create(:above_lending_loan_app_status, name: described_class::INITAL_LOAN_STATUS)
      end

      it 'skips legacy flow when process_loan: false and source_type != CRMM' do
        params[:source_type] = 'BEYOND'
        expect(LoanProService::LoanManagementSystem::CreateLoan).not_to receive(:call)
        expect(LoanProService::LoanManagementSystem::GetLoan).not_to receive(:call)
        expect do
          described_class.call(params: params, process_loan: false)
        end.to not_change { AboveLending::Loan.all.size }
          .and not_change { AboveLending::LoanproLoan.all.size }
      end

      it 'does not skip legacy flow when process_loan: false but source_type == CRMM' do
        params[:source_type] = LoanManagement::LoanBuilder::SOURCE_TYPE
        expect(LoanProService::LoanManagementSystem::CreateLoan).to receive(:call)
        expect(LoanProService::LoanManagementSystem::GetLoan).to receive(:call)
        expect do
          described_class.call(params: params, process_loan: false)
        end.to change { AboveLending::Loan.all.size }
          .and change { AboveLending::LoanproLoan.all.size }
      end

      it 'does not skip legacy flow when process_loan: true' do
        params[:source_type] = 'BEYOND'
        expect(LoanProService::LoanManagementSystem::CreateLoan).to receive(:call)
        expect(LoanProService::LoanManagementSystem::GetLoan).to receive(:call)
        expect do
          described_class.call(params: params)
        end.to change { AboveLending::Loan.all.size }
          .and change { AboveLending::LoanproLoan.all.size }
      end
    end
  end

  describe '.loan_terms' do
    before do
      create(:above_lending_loan_app_status, name: described_class::INITAL_LOAN_STATUS)
      allow(LoanProService::LoanManagementSystem::CreateLoan).to receive(:call).and_return(loan_pro_get_loan_data)
      params[:source_type] = described_class::SOURCE_TYPE
    end

    it 'returns the expected set of attributes when legacy flow skipped' do
      loan_builder = described_class.call(params:)
      loan_terms = loan_builder.loan_terms

      expect(loan_terms[:unified_id]).to eq(unified_id)
      expect(loan_terms[:loan_id]).to eq(Loan.order(created_at: :desc).first.id)
    end

    it 'returns an empty hash if no loan record is assigned' do
      loan_builder = described_class.new(params: params, process_loan: nil)
      loan_builder.instance_variable_set(:@loan_pro_loan_data, { id: 12_345 })
      expect(loan_builder.loan_terms).to eq({})
    end

    describe 'with validly initialized instance variables' do
      let(:loan_builder) { described_class.call(params: params) }

      it 'returns the expected set of attributes extracted from the Above Lending Loan record and the LoanPro loan data' do
        loan_terms = loan_builder.loan_terms

        expect(loan_terms[:unified_id]).to eq(unified_id)
        expect(loan_terms[:loan_id]).to eq(Loan.order(created_at: :desc).first.id)
        expect(loan_terms[:maturity_date]).not_to be_nil
        expect(loan_terms[:apr]).to eq(loan_pro_get_loan_data.dig('LoanSetup', 'apr').to_f)
        expect(loan_terms[:finance_charge]).to eq(loan_pro_get_loan_data.dig('LoanSetup', 'tilFinanceCharge').to_f)
        expect(loan_terms[:payment_schedule]).not_to be_nil
      end

      it 'parses the returned maturity date value properly' do
        loan_terms = loan_builder.loan_terms

        expect(loan_terms[:maturity_date]).to eq('2025-09-20'.to_date)
      end

      it 'parses the returned payment schedule properly' do
        loan_terms = loan_builder.loan_terms

        expect(loan_terms[:payment_schedule].length).to eq(2)
        expect(loan_terms[:payment_schedule].first['count']).to eq(35)
        expect(loan_terms[:payment_schedule].first['payment']).to eq(37.5)
        expect(loan_terms[:payment_schedule].first['startDate']).to eq('10/20/2022')
        expect(loan_terms[:payment_schedule].last['count']).to eq(1)
        expect(loan_terms[:payment_schedule].last['payment']).to eq(37.49)
        expect(loan_terms[:payment_schedule].last['startDate']).to eq('09/20/2025')
      end
    end

    context 'when process_loan is false' do
      before do
        create(:above_lending_loan_app_status, name: described_class::INITAL_LOAN_STATUS)
        params[:source_type] = 'BEYOND'
      end

      it 'returns an empty hash if no loan record is assigned' do
        loan_builder = described_class.new(params: params, process_loan: false)
        loan_builder.instance_variable_set(:@loan_pro_loan_data, loanpro_data.as_json)
        expect(loan_builder.loan_terms).to eq({})
      end

      it 'returns an empty hash if no loan pro data is available' do
        loan_builder = described_class.new(params: params, process_loan: false)
        loan_builder.instance_variable_set(:@loan, build(:above_lending_loan))
        expect(loan_builder.loan_terms).to eq({})
      end

      describe 'with validly initialized instance variables' do
        let(:loan_builder) { described_class.call(params: params, process_loan: false) }

        it 'returns the expected set of attributes extracted from the Loan record and the loanpro_data' do
          loan_terms = loan_builder.loan_terms

          expect(loan_terms[:unified_id]).to eq(unified_id)
          expect(loan_terms[:loan_id]).to eq(Loan.order(created_at: :desc).first.id)
          expect(loan_terms[:maturity_date]).not_to be_nil
          expect(loan_terms[:apr]).to eq(loanpro_data.dig(:LoanSetup, :apr).to_f)
          expect(loan_terms[:finance_charge]).to eq(loanpro_data.dig(:LoanSetup, :tilFinanceCharge).to_f)
          expect(loan_terms[:payment_schedule]).not_to be_nil
        end

        it 'parses the returned maturity date value properly' do
          loan_terms = loan_builder.loan_terms

          expect(loan_terms[:maturity_date]).to eq('2027-05-10'.to_date)
        end

        it 'parses the returned payment schedule properly' do
          loan_terms = loan_builder.loan_terms

          expect(loan_terms[:payment_schedule].length).to eq(1)
          expect(loan_terms[:payment_schedule].first['count']).to eq(119)
          expect(loan_terms[:payment_schedule].first['payment']).to eq(145.59)
          expect(loan_terms[:payment_schedule].first['startDate']).to eq('10/31/2022')
        end
      end
    end
  end
end
