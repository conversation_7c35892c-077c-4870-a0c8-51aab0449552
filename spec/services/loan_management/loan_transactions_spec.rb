# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanManagement::LoanTransactions do
  include NotifierHelper
  let!(:loan) { create(:above_lending_loan, :loanpro) }
  let!(:loan_id) { loan.loanpro_loan.loanpro_loan_id }
  let(:loan_id) { Faker::Number.number(digits: 5) }
  let(:base_url) { LoanProService::ConfigHelper.configuration.loan_management_system_url }
  before do
    allow(ActiveSupport::Notifications).to receive(:instrument).and_call_original
  end
  let(:loan_pro_loan_tx_response) do
    {
      'Transactions' =>
        [{ '__metadata' => { 'uri' => 'http://loanpro.simnang.com/api/public/api/1/odata.svc/LoanTransactions(id=123456789)', 'type' => 'Entity.LoanTransaction' },
           'id' => 2_632_350,
           'txId' => '15100-0-spm155',
           'entityType' => 'Entity.Loan',
           'entityId' => 15_100,
           'modId' => 0,
           'date' => '/Date(1845763200)/',
           'period' => 155,
           'periodStart' => '/Date(1844553600)/',
           'periodEnd' => '/Date(1845676800)/',
           'title' => 'Scheduled Payment: 156',
           'type' => 'scheduledPayment',
           'infoOnly' => 0,
           'infoDetails' => nil,
           'paymentId' => 0,
           'paymentDisplayId' => 0,
           'paymentAmount' => '0',
           'paymentInterest' => '0',
           'paymentPrincipal' => '0',
           'paymentDiscount' => '0',
           'paymentFees' => '0',
           'feesPaidDetails' => nil,
           'paymentEscrow' => '0',
           'paymentEscrowBreakdown' => nil,
           'chargeAmount' => '386.98',
           'chargeInterest' => '3.92',
           'chargePrincipal' => '383.06',
           'chargeDiscount' => '0',
           'chargeFees' => '0',
           'chargeEscrow' => '0',
           'chargeEscrowBreakdown' => '{"subsets":{"2":0,"3":0,"4":0,"5":0}}',
           'future' => 1,
           'principalOnly' => 0,
           'advancement' => 0,
           'payoffFee' => 0,
           'chargeOff' => 0,
           'paymentType' => 0,
           'adbDays' => 14,
           'adb' => '383.06',
           'principalBalance' => '383.06',
           'displayOrder' => '0' },
         { '__metadata' => { 'uri' => 'http://loanpro.simnang.com/api/public/api/1/odata.svc/LoanTransactions(id=123456789)', 'type' => 'Entity.LoanTransaction' },
           'id' => 123_456_789,
           'txId' => '15100-0-fpm155',
           'entityType' => 'Entity.Loan',
           'entityId' => 15_100,
           'modId' => 0,
           'date' => '/Date(1845763200)/',
           'period' => 155,
           'periodStart' => '/Date(1844553600)/',
           'periodEnd' => '/Date(1845676800)/',
           'title' => 'Forecasted Payment: 156',
           'type' => 'forecastedPayment',
           'infoOnly' => 0,
           'infoDetails' => nil,
           'paymentId' => 0,
           'paymentDisplayId' => 0,
           'paymentAmount' => '386.98',
           'paymentInterest' => '3.92',
           'paymentPrincipal' => '383.06',
           'paymentDiscount' => '0',
           'paymentFees' => '0',
           'feesPaidDetails' => nil,
           'paymentEscrow' => '0',
           'paymentEscrowBreakdown' => '{"subsets":{"2":0,"3":0,"4":0,"5":0}}',
           'chargeAmount' => '0',
           'chargeInterest' => '0',
           'chargePrincipal' => '0',
           'chargeDiscount' => '0',
           'chargeFees' => '0',
           'chargeEscrow' => '0',
           'chargeEscrowBreakdown' => nil,
           'future' => 1,
           'principalOnly' => 0,
           'advancement' => 0,
           'payoffFee' => 0,
           'chargeOff' => 0,
           'paymentType' => 0,
           'adbDays' => 14,
           'adb' => '383.06',
           'principalBalance' => '0',
           'displayOrder' => '9' }]
    }
  end
  let(:loan_pro_loan_tx_response_parsed) do
    [
      { '__metadata' => { 'uri' => 'http://loanpro.simnang.com/api/public/api/1/odata.svc/LoanTransactions(id=2631979)', 'type' => 'Entity.LoanTransaction' },
        'id' => 2_631_979,
        'txId' => '15100-0-spm0',
        'entityType' => 'Entity.Loan',
        'entityId' => 15_100,
        'modId' => 0,
        'date' => '/Date(1658275200)/',
        'period' => 0,
        'periodStart' => '/Date(1656892800)/',
        'periodEnd' => '/Date(1658188800)/',
        'title' => 'Scheduled Payment: 1',
        'type' => 'scheduledPayment',
        'infoOnly' => 0,
        'infoDetails' => nil,
        'paymentId' => 0,
        'paymentDisplayId' => 0,
        'paymentAmount' => '100',
        'paymentInterest' => '0',
        'paymentPrincipal' => '0',
        'paymentDiscount' => '0',
        'paymentFees' => '0',
        'feesPaidDetails' => nil,
        'paymentEscrow' => '0',
        'paymentEscrowBreakdown' => nil,
        'chargeAmount' => '386.98',
        'chargeInterest' => '328.82',
        'chargePrincipal' => '58.16',
        'chargeDiscount' => '0',
        'chargeFees' => '0',
        'chargeEscrow' => '0',
        'chargeEscrowBreakdown' => '{"subsets":{"2":0,"3":0,"4":0,"5":0}}',
        'future' => 0,
        'principalOnly' => 0,
        'advancement' => 0,
        'payoffFee' => 0,
        'chargeOff' => 0,
        'paymentType' => 0,
        'adbDays' => 16,
        'adb' => '32756.62',
        'principalBalance' => '0',
        'displayOrder' => '0' },
      { '__metadata' => { 'uri' => 'http://loanpro.simnang.com/api/public/api/1/odata.svc/LoanTransactions(id=2631976)', 'type' => 'Entity.LoanTransaction' },
        'id' => 2_631_976,
        'txId' => '15100-0-info-origin',
        'entityType' => 'Entity.Loan',
        'entityId' => 15_100,
        'modId' => 0,
        'date' => '/Date(1656892800)/',
        'period' => 0,
        'periodStart' => '/Date(-62169984000)/',
        'periodEnd' => '/Date(-62169984000)/',
        'title' => 'Loan Origination',
        'type' => 'origination',
        'infoOnly' => 1,
        'infoDetails' => '{"amount":"31118.79","underwriting":"1637.83","discount":"0.00"}',
        'paymentId' => 0,
        'paymentDisplayId' => 0,
        'paymentAmount' => '200',
        'paymentInterest' => '0',
        'paymentPrincipal' => '0',
        'paymentDiscount' => '0',
        'paymentFees' => '0',
        'feesPaidDetails' => nil,
        'paymentEscrow' => '0',
        'paymentEscrowBreakdown' => nil,
        'chargeAmount' => '0',
        'chargeInterest' => '0',
        'chargePrincipal' => '0',
        'chargeDiscount' => '0',
        'chargeFees' => '0',
        'chargeEscrow' => '0',
        'chargeEscrowBreakdown' => nil,
        'future' => 0,
        'principalOnly' => 0,
        'advancement' => 0,
        'payoffFee' => 0,
        'chargeOff' => 0,
        'paymentType' => 0,
        'adbDays' => 0,
        'adb' => '0',
        'principalBalance' => '22721.92',
        'displayOrder' => '0' }
    ]
  end
  let(:loan_transactions_data) do
    {
      transactions: [
        {
          paymentAmount: '100',
          paymentInterest: '0',
          paymentPrincipal: '0',
          paymentDiscount: '0',
          paymentFees: '0',
          principalBalance: '0',
          transactionId: 2_631_979,
          transactionDate: '2022-07-20'
        },
        {
          paymentAmount: '200',
          paymentInterest: '0',
          paymentPrincipal: '0',
          paymentDiscount: '0',
          paymentFees: '0',
          principalBalance: '22721.92',
          transactionId: 2_631_976,
          transactionDate: '2022-07-04'
        }
      ]
    }
  end

  describe '.call' do
    context 'with valid loan ID' do
      subject { described_class.call(loan_id) }
      it 'makes an API call to the correct endpoint' do
        request = stub_request(:get, "#{base_url}odata.svc/Loans(#{loan_id})?$expand=Transactions&nopaging=true")
                  .to_return(headers: { content_type: 'application/json' }, body: { d: loan_pro_loan_tx_response }.to_json)

        expect { subject }.to_not raise_error
        expect(request).to have_been_requested
      end
    end

    context 'when making calls to the lp api' do
      it 'gets loan transactions data' do
        expect(LoanProService::TransactionManagement::GetLoanTransactions).to receive(:call).once
                                                                                            .with(loan_id)
                                                                                            .and_return(loan_pro_loan_tx_response_parsed)
        described_class.call(loan_id)
      end
    end

    context 'when there is a lp error' do
      it 'raises a GetLoanTransactionsSystemError and notifies' do
        allow(LoanProService::TransactionManagement::GetLoanTransactions).to receive(:call).and_raise(LoanProService::Exceptions::LoanManagementSystemError, 'Loan Management System Error!')

        expect do
          described_class.call(loan_id)
        end.to raise_error(LoanManagement::LoanTransactions::LoanTransactionsSystemError, 'Internal Server Error')
        expect_to_notify('loan_transactions', success: false, fail_reason: 'Loan Management System Error!')
      end
    end
  end
end
