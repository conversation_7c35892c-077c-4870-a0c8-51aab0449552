# frozen_string_literal: true

require 'rails_helper'
require 'support/shared/loanpro_data_common'

RSpec.describe LoanManagement::LoanUpdater do
  include NotifierHelper

  include_context 'loanpro_data_common'

  let(:params) do
    contract_date = [Date.today, Date.tomorrow].sample

    {
      cashout_amount: [0, 500, 1000, 1500, 2000].sample,
      contract_date: contract_date,
      first_payment_date: contract_date + 1.month,
      interest_rate: rand(5.0..25.0),
      loan_id: loan_id,
      number_of_payments: rand(36..72),
      origination_fee: rand(100..250),
      payment_frequency: LoanProHelpers::ABOVE_TO_LOAN_PRO_PAYMENT_FREQUENCIES.keys.sample,
      settlement_amount: rand(2500..10_000),
      loanpro_data:,
      product_type: 'UPL'
    }
  end
  let(:above_lending_loan) { create(:above_lending_loan) }
  let(:unified_id) { above_lending_loan.unified_id }
  let(:loan_pro_update_loan_data) do
    {
      id: 13_859,
      displayId: unified_id,
      setupId: 13_810,
      LoanSetup: {
        id: 13_810,
        loanId: 13_859,
        active: 0,
        apr: '20.6167',
        origFinalPaymentDate: '/Date(1758326400)/',
        firstPaymentDate: '/Date(1667174400)/',
        tilTotalOfPayments: '1119.0000',
        paymentFrequency: 'loan.frequency.monthly',
        tilFinanceCharge: '350.00',
        tilPaymentSchedule: "[{\"count\":35,\"payment\":37.5,\"startDate\":\"10\/20\/2022\"},{\"count\":1,\"payment\":37.49,\"startDate\":\"09\/20\/2025\"}]",
        isSetupValid: true
      }
    }.as_json
  end
  let!(:loan) { create(:loan, loan_number: unified_id, detail_info: build(:loan_detail, purpose: 'debt_consolidation')) }
  let(:loan_id) { loan.id }

  describe '.call' do
    before do
      allow(LoanProService::LoanManagementSystem::UpdateLoanSetup).to receive(:call).and_return(loan_pro_update_loan_data)
      allow(ActiveSupport::Notifications).to receive(:instrument).and_call_original
    end

    it 'looks up the existing Loan record in the Above Lending DB with the specified unified ID' do
      expect do
        loan_updater = described_class.call(unified_id: unified_id, params: params)

        expect(loan_updater.above_lending_loan).to eq(above_lending_loan)
      end.not_to(change { AboveLending::Loan.all.size })
    end

    it 'does not overwrite prior loan data' do
      loan_pro_loan = create(:loan_entity, display_id: unified_id)
      loan_pro_loan.loan_setup_entity

      expect do
        loan_updater = described_class.call(unified_id: unified_id, params: params)

        expect(loan_updater.above_lending_loan).to eq(above_lending_loan)
      end.not_to(change { Loan.all.size })

      updated_loan = Loan.find(loan.id)
      expect(updated_loan.detail_info.purpose).to eq('debt_consolidation')
    end

    it 'updates existing Loan record when loan_id is specified' do
      loan_pro_loan = create(:loan_entity, display_id: unified_id)
      loan_pro_loan.loan_setup_entity

      expect do
        loan_updater = described_class.call(unified_id: unified_id, params: params)

        expect(loan_updater.above_lending_loan).to eq(above_lending_loan)
      end.not_to(change { Loan.all.size })

      updated_loan = Loan.find(loan.id)

      expect(updated_loan.id).to be_present
      expect(updated_loan.product_type).to eq(params[:product_type])
      expect(updated_loan.detail_info.principal_amount).to eq params[:settlement_amount] + params[:cashout_amount] + params[:origination_fee]
      expect(updated_loan.detail_info.interest_rate).to eq params[:interest_rate]
      expect(updated_loan.detail_info.apr).to eq BigDecimal(loan_pro_update_loan_data.dig('LoanSetup', 'apr'))
      expect(updated_loan.detail_info.cashback_amount).to eq(params[:cashout_amount])
      expect(updated_loan.detail_info.term).to eq params[:number_of_payments]
      expect(updated_loan.detail_info.first_payment_on).to eq('2022-10-31'.to_date)
      expect(updated_loan.detail_info.final_payment_amount).to eq(37.49)
      expect(updated_loan.detail_info.final_payment_on).to eq('2025-09-20'.to_date)
      expect(updated_loan.detail_info.payment_amount).to eq(37.5)
      expect(updated_loan.detail_info.payment_frequency).to eq(params[:payment_frequency])
      expect(updated_loan.detail_info.finance_charge_amount).to eq(350)
      expect(updated_loan.detail_info.total_payments_amount).to eq(1119)
    end

    it 'calls the LoanPro API to update the associated loan setup record' do
      loan_pro_loan = create(:loan_entity, display_id: unified_id)
      loan_pro_loan_setup = loan_pro_loan.loan_setup_entity

      params[:payment_frequency] = 'bi_weekly'

      expected_loanpro_loan_setup_data = {
        loanAmount: params[:settlement_amount] + params[:cashout_amount],
        loanRate: params[:interest_rate],
        underwriting: params[:origination_fee],
        loanTerm: params[:number_of_payments],
        paymentFrequency: 'loan.frequency.biWeekly',
        contractDate: params[:contract_date],
        firstPaymentDate: params[:first_payment_date]
      }

      expect(LoanProService::LoanManagementSystem::UpdateLoanSetup).to receive(:call).with(loan_id: loan_pro_loan.id, loan_setup_id: loan_pro_loan_setup.id,
                                                                                           attributes: expected_loanpro_loan_setup_data).and_return(loan_pro_update_loan_data)

      described_class.call(unified_id: unified_id, params: params)
    end

    it 'defaults the payment frequency to monthly if no payment_frequency value is specified' do
      create(:loan_entity, display_id: unified_id)

      expect(LoanProService::LoanManagementSystem::UpdateLoanSetup).to receive(:call) do |loan_id:, loan_setup_id:, attributes:|
        expect(loan_id).to be_present
        expect(loan_setup_id).to be_present
        expect(attributes[:paymentFrequency]).to eq('loan.frequency.monthly')

        loan_pro_update_loan_data
      end

      described_class.call(unified_id: unified_id, params: params.merge(payment_frequency: nil))
    end

    it 'logs a detailed error if the LoanPro operations fails' do
      create(:loan_entity, display_id: unified_id)

      loan_pro_operation_data = { response: { status: 500, body: 'Test Error', request: {} } }
      expect(LoanProService::LoanManagementSystem::UpdateLoanSetup).to receive(:call).and_raise(LoanProService::Exceptions::LoanManagementSystemError.new(loan_pro_operation_data))

      expect(Rails.logger).to receive(:error).at_least(:once)

      expect { described_class.call(unified_id: unified_id, params: params) }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError)

      expect_to_notify('loan_pro_update_loan_setup_error', extra: { loan_pro_data: loan_pro_operation_data })
    end

    it 'records service attributes in an event' do
      loan_pro_loan = create(:loan_entity, display_id: unified_id)
      loan_pro_loan.loan_setup_entity

      described_class.call(unified_id: unified_id, params: params)
      expect_to_notify('LoanUpdater', success: true, extra: { above_lending_loan_id: above_lending_loan.id, loan_id: loan.id, loan_pro_loan_id: 13_859, request_id: nil, unified_id: })
    end

    context 'when the loan is already processed by arix' do
      let!(:funding) { create(:funding, loan: loan, detail_info: funding_detail) }
      let(:funding_detail) { build(:funding_detail, funding_status: funding_status) }
      let(:funding_status) { Funding::Detail::COMPLIANCE_FAILED }
      let(:loan_id) { nil }
      let(:error_message) { "ApplicationServiceBase::LoanAlreadyProcessed: Loan ID ##{loan.id} is already processed by arix" }

      it 'raises an error' do
        expect do
          expect { described_class.call(unified_id: unified_id, params: params) }.to raise_error(ApplicationServiceBase::LoanAlreadyProcessed)
        end.to not_change { Loan.all.size }
          .and not_change { AboveLending::LoanproLoan.all.size }
          .and not_change { AboveLending::Loan.all.size }

        expect_to_notify('LoanUpdater', success: false, fail_reason: error_message, extra: { above_lending_loan_id: above_lending_loan.id, unified_id: unified_id, funding_status: })
      end
    end

    describe 'process_loan' do
      it 'does not call the LoanPro API when process_loan: false and source_type != CRMM' do
        above_lending_loan.update!(source_type: 'BEYOND')
        expect(LoanProService::LoanManagementSystem::UpdateLoanSetup).not_to receive(:call)
        described_class.call(unified_id: unified_id, params: params, process_loan: false)
      end

      it 'does not skip legacy flow when process_loan: false but source_type == CRMM' do
        above_lending_loan.update!(source_type: LoanManagement::LoanBuilder::SOURCE_TYPE)
        create(:loan_entity, display_id: unified_id)
        expect(LoanProService::LoanManagementSystem::UpdateLoanSetup).to receive(:call)
        described_class.call(unified_id: unified_id, params: params, process_loan: false)
      end

      it 'does not skip legacy flow when process_loan: true but source_type == CRMM' do
        above_lending_loan.update!(source_type: 'BEYOND')
        create(:loan_entity, display_id: unified_id)
        expect(LoanProService::LoanManagementSystem::UpdateLoanSetup).to receive(:call)
        described_class.call(unified_id: unified_id, params: params)
      end

      it 'looks up the existing Loan record in the Above Lending DB with the specified unified ID' do
        expect do
          loan_updater = described_class.call(unified_id: unified_id, params: params)

          expect(loan_updater.above_lending_loan).to eq(above_lending_loan)
        end.not_to(change { AboveLending::Loan.all.size })
      end

      it 'updates existing Loan record when loan_id is specified' do
        expect do
          described_class.call(unified_id: unified_id, params: params, process_loan: false)
        end.not_to(change { Loan.all.size })

        updated_loan = Loan.find(loan.id)
        expect(updated_loan.id).to be_present
        expect(updated_loan.detail_info.principal_amount).to eq params[:settlement_amount] + params[:cashout_amount] + params[:origination_fee]
        expect(updated_loan.detail_info.interest_rate).to eq params[:interest_rate]
        expect(updated_loan.detail_info.apr).to eq BigDecimal(loanpro_data.dig(:LoanSetup, :apr))
        expect(updated_loan.detail_info.term).to eq params[:number_of_payments]
      end
    end
  end

  describe '.loan_terms' do
    it 'returns an empty hash if no loan record is assigned' do
      loan_updater = described_class.new(unified_id: unified_id, params: params, process_loan: nil)
      loan_updater.instance_variable_set(:@loan_pro_loan_data, loan_pro_update_loan_data)
      expect(loan_updater.loan_terms).to eq({})
    end

    it 'returns an empty hash if no loan pro data is available' do
      loan_updater = described_class.new(unified_id: unified_id, params: params, process_loan: nil)
      loan_updater.instance_variable_set(:@above_lending_loan, above_lending_loan)
      expect(loan_updater.loan_terms).to eq({})
    end

    describe 'with validly initialized instance variables' do
      let(:loan_updater) { described_class.call(unified_id: unified_id, params: params) }

      before do
        create(:loan_entity, display_id: unified_id)
        allow(LoanProService::LoanManagementSystem::UpdateLoanSetup).to receive(:call).and_return(loan_pro_update_loan_data)
      end

      it 'returns the expected set of attributes extracted from the Above Lending Loan record and the LoanPro loan data' do
        loan_terms = loan_updater.loan_terms

        expect(loan_terms[:unified_id]).to eq(unified_id)
        expect(loan_terms[:maturity_date]).not_to be_nil
        expect(loan_terms[:apr]).to eq(loan_pro_update_loan_data.dig('LoanSetup', 'apr').to_f)
        expect(loan_terms[:finance_charge]).to eq(loan_pro_update_loan_data.dig('LoanSetup', 'tilFinanceCharge').to_f)
        expect(loan_terms[:payment_schedule]).not_to be_nil
      end

      it 'parses the returned maturity date value properly' do
        loan_terms = loan_updater.loan_terms

        expect(loan_terms[:maturity_date]).to eq('2025-09-20'.to_date)
      end

      it 'parses the returned payment schedule properly' do
        loan_terms = loan_updater.loan_terms

        expect(loan_terms[:payment_schedule].length).to eq(2)
        expect(loan_terms[:payment_schedule].first['count']).to eq(35)
        expect(loan_terms[:payment_schedule].first['payment']).to eq(37.5)
        expect(loan_terms[:payment_schedule].first['startDate']).to eq('10/20/2022')
        expect(loan_terms[:payment_schedule].last['count']).to eq(1)
        expect(loan_terms[:payment_schedule].last['payment']).to eq(37.49)
        expect(loan_terms[:payment_schedule].last['startDate']).to eq('09/20/2025')
      end
    end

    context 'when process_loan returns false' do
      let(:above_lending_loan) { create(:above_lending_loan, source_type: 'BEYOND') }

      it 'returns the expected set of attributes when legacy flow skipped' do
        loan_builder = described_class.call(unified_id:, params:, process_loan: false)
        loan_terms = loan_builder.loan_terms

        expect(loan_terms[:unified_id]).to eq(unified_id)
      end

      it 'returns an empty hash if no loan record is assigned' do
        loan_builder = described_class.new(unified_id:, params:, process_loan: false)
        loan_builder.instance_variable_set(:@loan_pro_loan_data, loanpro_data.as_json)
        expect(loan_builder.loan_terms).to eq({})
      end

      describe 'with validly initialized instance variables' do
        let(:loan_updater) { described_class.call(unified_id: unified_id, params: params, process_loan: false) }

        it 'returns the expected set of attributes extracted from the Loan record and the loanpro_data' do
          loan_terms = loan_updater.loan_terms

          expect(loan_terms[:unified_id]).to eq(unified_id)
          expect(loan_terms[:maturity_date]).not_to be_nil
          expect(loan_terms[:apr]).to eq(loanpro_data.dig(:LoanSetup, :apr).to_f)
          expect(loan_terms[:finance_charge]).to eq(loanpro_data.dig(:LoanSetup, :tilFinanceCharge).to_f)
          expect(loan_terms[:payment_schedule]).not_to be_nil
        end

        it 'parses the returned maturity date value properly' do
          loan_terms = loan_updater.loan_terms

          expect(loan_terms[:maturity_date]).to eq('2027-05-10'.to_date)
        end

        it 'parses the returned payment schedule properly' do
          loan_terms = loan_updater.loan_terms

          expect(loan_terms[:payment_schedule].length).to eq(1)
          expect(loan_terms[:payment_schedule].first['count']).to eq(119)
          expect(loan_terms[:payment_schedule].first['payment']).to eq(145.59)
          expect(loan_terms[:payment_schedule].first['startDate']).to eq('10/31/2022')
        end
      end
    end
  end
end
