# frozen_string_literal: true

require 'rails_helper'
require 'support/shared/onboard_loan_common'

RSpec.describe LoanManagement::OnboardLoan do
  include_context 'onboard_loan_common'
  include NotifierHelper

  let(:cft_account) { onboard_params[:cft_account].to_json }
  let(:unified_id) { Faker::Number.number(digits: 8).to_s }
  let(:loan_id) { SecureRandom.uuid }
  let!(:initial_status) { create(:above_lending_loan_app_status, name: 'APPROVED') }
  let!(:onboarded_status) { create(:above_lending_loan_app_status, name: 'ONBOARDED') }
  let!(:above_lending_loan) { create(:above_lending_loan, :loanpro, unified_id: unified_id, loan_app_status_id: initial_status.id) }
  let(:update_above_lending_loan) { instance_double(LoanManagement::OnboardLoans::AboveLending::UpdateLoan) }
  let(:til) { 'CRB_TIL' }
  let(:ila) { 'CRB_INSTALLMENT_LOAN_AGREEMENT' }
  let!(:til_template) { create(:above_lending_doc_template, type: til) }
  let!(:ila_template) { create(:above_lending_doc_template, type: ila) }

  before do
    allow(ActiveSupport::Notifications).to receive(:instrument).and_call_original
    allow(CopyS3Object).to receive(:call).and_return(true)
    onboard_params[:consent_documents].each do |doc_params|
      create(:above_lending_doc_template, type: doc_params[:document_type])
    end
  end

  describe '.call' do
    subject { described_class.call(unified_id: unified_id, loan_id: loan_id, params: onboard_params) }

    it 'Updates a Loan record in the Above Lending DB with the passed attributes' do
      expect(LoanManagement::OnboardLoans::AboveLending::UpdateLoan)
        .to receive(:new)
        .with(unified_id: unified_id, params: onboard_params)
        .and_return(update_above_lending_loan)
      expect(update_above_lending_loan).to receive(:call)
      expect(update_above_lending_loan).to receive(:loan).and_return(above_lending_loan)
      expect(update_above_lending_loan).to receive(:borrower).and_return(above_lending_loan.borrower)
      expect(LoanManagement::OnboardLoans::UpdateLoan).to receive(:call).with(loan_id: loan_id, params: onboard_params)

      expect do
        expect(subject).to be_truthy
      end.to change(ExecuteLoanproOnboarding.jobs, :size).by(1)

      expect_to_notify('OnboardLoan', success: true,
                                      extra: { above_lending_loan_id: above_lending_loan.id, loan_id: loan_id, unified_id: unified_id })
    end

    it 'triggers the FetchOriginationReportsJob' do
      allow(LoanManagement::OnboardLoans::AboveLending::UpdateLoan).to receive(:new).and_return(update_above_lending_loan)
      allow(update_above_lending_loan).to receive(:call)
      allow(update_above_lending_loan).to receive(:loan).and_return(above_lending_loan)
      allow(update_above_lending_loan).to receive(:borrower).and_return(above_lending_loan.borrower)
      allow(LoanManagement::OnboardLoans::UpdateLoan).to receive(:call)

      expect(LoanManagement::OnboardLoans::FetchOriginationReportsJob).to receive(:perform_async).with(loan_id)
      expect(subject).to be_truthy

      expect_to_notify('OnboardLoan', success: true,
                                      extra: { above_lending_loan_id: above_lending_loan.id, loan_id: loan_id, unified_id: unified_id })
    end

    describe 'process_loan' do
      context 'when process_loan: false and source_type != CRMM' do
        let!(:above_lending_loan) { create(:above_lending_loan, :loanpro, unified_id: unified_id, loan_app_status_id: initial_status.id, source_type: 'BEYOND') }

        it 'skips legacy flow' do
          expect(LoanManagement::OnboardLoans::AboveLending::UpdateLoan).not_to receive(:new)
          expect(LoanManagement::OnboardLoans::UpdateLoan).to receive(:call).with(loan_id:, params: onboard_params)
          expect do
            expect(described_class.call(unified_id:, loan_id:, params: onboard_params, process_loan: '0')).to be_truthy
          end.to change(ExecuteLoanproOnboarding.jobs, :size).by(0)

          expect_to_notify('OnboardLoan', success: true,
                                          extra: { above_lending_loan_id: above_lending_loan.id, loan_id: loan_id, unified_id: unified_id })
        end
      end

      context 'when process_loan: false but source_type == CRMM' do
        let!(:above_lending_loan) { create(:above_lending_loan, :loanpro, unified_id: unified_id, loan_app_status_id: initial_status.id, source_type: LoanManagement::LoanBuilder::SOURCE_TYPE) }

        it 'does not skip legacy flow' do
          expect(LoanManagement::OnboardLoans::AboveLending::UpdateLoan).to receive(:new).and_return(update_above_lending_loan).with(unified_id:, params: onboard_params)
          expect(update_above_lending_loan).to receive(:call)
          allow(update_above_lending_loan).to receive(:loan).and_return(above_lending_loan)
          allow(update_above_lending_loan).to receive(:borrower)
          expect(LoanManagement::OnboardLoans::UpdateLoan).to receive(:call).with(loan_id:, params: onboard_params)
          expect do
            expect(described_class.call(unified_id:, loan_id:, params: onboard_params, process_loan: 'false')).to be_truthy
          end.to change(ExecuteLoanproOnboarding.jobs, :size).by(1)

          expect_to_notify('OnboardLoan', success: true,
                                          extra: { above_lending_loan_id: above_lending_loan.id, loan_id: loan_id, unified_id: unified_id })
        end
      end

      context 'when process_loan: true' do
        let!(:above_lending_loan) { create(:above_lending_loan, :loanpro, unified_id: unified_id, loan_app_status_id: initial_status.id, source_type: 'BEYOND') }

        it 'does not skip legacy flow' do
          expect(LoanManagement::OnboardLoans::AboveLending::UpdateLoan).to receive(:new).and_return(update_above_lending_loan).with(unified_id:, params: onboard_params)
          expect(update_above_lending_loan).to receive(:call)
          allow(update_above_lending_loan).to receive(:loan).and_return(above_lending_loan)
          allow(update_above_lending_loan).to receive(:borrower).and_return(above_lending_loan.borrower)
          expect(LoanManagement::OnboardLoans::UpdateLoan).to receive(:call).with(loan_id:, params: onboard_params)
          expect do
            expect(described_class.call(unified_id:, loan_id:, params: onboard_params)).to be_truthy
          end.to change(ExecuteLoanproOnboarding.jobs, :size).by(1)

          expect_to_notify('OnboardLoan', success: true,
                                          extra: { above_lending_loan_id: above_lending_loan.id, loan_id: loan_id, unified_id: unified_id })
        end
      end
    end
  end

  context 'when LoanManagement::OnboardLoans::AboveLending::UpdateLoan fails' do
    before do
      allow(LoanManagement::OnboardLoans::AboveLending::UpdateLoan)
        .to receive(:new)
        .and_return(update_above_lending_loan)
      allow(update_above_lending_loan).to receive(:call).and_raise(StandardError.new('Boom!'))
    end

    it 'should not add Borrower, notify instrumentation and return false' do
      expect do
        expect(described_class.call(unified_id: unified_id, loan_id: loan_id, params: onboard_params)).to be_falsey
      end.to not_change { Borrower.count }

      expect_to_notify('OnboardLoan', success: false, fail_reason: 'StandardError: Boom!',
                                      extra: { above_lending_loan_id: above_lending_loan.id, loan_id: loan_id, unified_id: unified_id })
    end
  end

  context 'when LoanManagement::OnboardLoans::UpdateLoan fails' do
    before do
      allow(LoanManagement::OnboardLoans::UpdateLoan)
        .to receive(:call)
        .with(loan_id: loan_id, params: onboard_params)
        .and_raise(StandardError.new('Boom!'))
    end

    it 'should not update AboveLending::Loan, notify instrumentation and return false' do
      expect do
        expect(described_class.call(unified_id: unified_id, loan_id: loan_id, params: onboard_params)).to be_falsey
        above_lending_loan.reload
      end.to not_change { above_lending_loan.loan_app_status }.from(initial_status)

      expect_to_notify('OnboardLoan', success: false, fail_reason: 'StandardError: Boom!',
                                      extra: { above_lending_loan_id: above_lending_loan.id, loan_id: loan_id, unified_id: unified_id })
    end
  end

  context 'when the LP API raises an exception' do
    before do
      allow(LoanManagement::OnboardLoans::AboveLending::UpdateLoan)
        .to receive(:new)
        .with(unified_id: unified_id, params: onboard_params)
        .and_return(update_above_lending_loan)
      allow(update_above_lending_loan).to receive(:call)
      allow(update_above_lending_loan).to receive(:loan).and_return(above_lending_loan)
      allow(update_above_lending_loan).to receive(:borrower).and_return(above_lending_loan.borrower)
      allow(LoanManagement::OnboardLoans::UpdateLoan).to receive(:call).with(loan_id: loan_id, params: onboard_params)
      allow(ExecuteLoanproOnboarding).to receive(:perform_async).and_raise(StandardError.new('Boom!'))
    end

    it 'should notify instrumentation and return false' do
      expect do
        expect(described_class.call(unified_id: unified_id, loan_id: loan_id, params: onboard_params)).to be_falsey
        above_lending_loan.reload
      end.to not_change { Borrower.count }
        .and not_change { above_lending_loan.loan_app_status }.from(initial_status)

      expect_to_notify('OnboardLoan', success: false, fail_reason: 'StandardError: Boom!',
                                      extra: { above_lending_loan_id: above_lending_loan.id, loan_id: loan_id, unified_id: unified_id })
    end
  end
end
