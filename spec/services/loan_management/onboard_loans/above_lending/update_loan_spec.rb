# frozen_string_literal: true

require 'rails_helper'
require 'support/shared/onboard_loan_common'

RSpec.describe LoanManagement::OnboardLoans::AboveLending::UpdateLoan do
  include_context 'onboard_loan_common'

  let(:offers_params) { onboard_params[:offers] }
  let(:selected_offer_params) { onboard_params[:offers].first }
  let(:borrower_params) { onboard_params[:borrower] }
  let(:application_params) { onboard_params[:application] }
  let(:cft_params) { onboard_params[:cft_account] }
  let(:til_history_params) { onboard_params[:til_history] }
  let(:unified_id) { Faker::Number.number(digits: 8).to_s }
  let(:initial_status) { create(:above_lending_loan_app_status, name: 'APPROVED') }
  let(:onboarded_status) { create(:above_lending_loan_app_status, name: 'ONBOARDED') }
  let!(:above_lending_loan) { create(:above_lending_loan, :loanpro, unified_id: unified_id, loan_app_status_id: initial_status.id, todos: []) }
  let!(:loan_id) { above_lending_loan.id }
  let(:til) { 'CRB_TIL' }
  let(:ila) { 'CRB_INSTALLMENT_LOAN_AGREEMENT' }
  let!(:til_template) { create(:above_lending_doc_template, type: til) }
  let!(:ila_template) { create(:above_lending_doc_template, type: ila) }

  before do
    # create the needed consent document templates
    LoanManagement::OnboardLoans::AboveLending::UpsertConsentDocuments::ALLOWED_DOCUMENT_TYPES.each do |document_type|
      create(:above_lending_doc_template, type: document_type)
    end
  end

  describe '.call' do
    it 'Updates a Loan record in the Above Lending DB with the passed attributes' do
      allow_any_instance_of(UpdateEmailManager).to receive(:call).and_return(true)

      expect(LoanManagement::OnboardLoans::AboveLending::CopyDocumentsToCrbAttachmentUploaderJob)
        .to receive(:perform_async)
        .with(loan_id, onboard_params[:contract_documents].first[:bucket_name])
        .and_return(true)

      # LoanPro requires that this is downcased, so we should ensure that it is downcase
      # when it's persisted to the database
      onboard_params[:application][:account_type] = 'Checking'

      expect do
        described_class.call(unified_id: unified_id, params: onboard_params)
        above_lending_loan.reload
        above_lending_loan.borrower.reload
        above_lending_loan.borrower_additional_info.reload
      end.to(
        change { above_lending_loan.bank_accounts.count }.by(1)
      )

      amount_financed = selected_offer_params[:settlement_amount] + selected_offer_params[:cashout_amount]

      expect(above_lending_loan).to have_attributes(
        anual_income: application_params[:annual_income],
        credit_score: application_params[:credit_score_number],
        dti: application_params[:debt_to_income_ratio] * 100,
        employment_status: application_params[:employment_status],
        monthly_housing_payment: application_params[:housing_payment_monthly],
        originating_party: selected_offer_params[:originating_party].upcase,
        program_duration_in_tmonths: borrower_params[:program_duration_in_tmonths],
        program_id: borrower_params[:program_id].first(12),
        decline_reasons: application_params[:decline_reason_messages],
        decline_reason_text: application_params[:decline_reason_messages].first
      )

      loan_app_status = ::AboveLending::LoanAppStatus.find_by(name: ::AboveLending::LoanAppStatus::CRM_MAPPINGS[application_params[:status]])
      expect(above_lending_loan.loan_app_status).to eq(loan_app_status)

      expect(above_lending_loan.bank_accounts.last).to have_attributes(
        account_number: application_params[:account_number],
        account_type: application_params[:account_type].downcase,
        routing_number: application_params[:routing_number],
        holder_firstname: application_params[:holder_firstname],
        holder_lastname: application_params[:holder_lastname],
        bank: application_params[:bank],
        last_four_account_number: application_params[:account_number].last(4),
        last_four_routing_number: application_params[:routing_number].last(4),
        fund_transfer_authorize: application_params[:autopay_enabled],
        enabled: true
      )

      expect(above_lending_loan.offers.length).to eq(2)
      offers_params.each_with_index do |offer_params, index|
        offer = above_lending_loan.offers.order(:created_at).all[index]
        expect(offer).to have_attributes(
          amount: offer_params[:settlement_amount] + offer_params[:cashout_amount] + offer_params[:origination_fee],
          amount_financed: offer_params[:settlement_amount] + offer_params[:cashout_amount],
          interest_rate: offer_params[:interest_rate],
          cashout_amount: offer_params[:cashout_amount],
          description: offer_params[:description],
          initial_term_payment: offer_params[:initial_term_payment],
          final_term_payment: offer_params[:final_term_payment],
          is_hero: offer_params[:is_hero],
          originating_party: offer_params[:originating_party],
          origination_fee: offer_params[:origination_fee],
          origination_fee_percent: offer_params[:origination_fee_percent],
          selected: offer_params[:selected],
          settlement_amount: offer_params[:settlement_amount],
          term: offer_params[:term]
        )
        expect(offer.expiration_date).to be_within(1.second).of Rails.application.config_for(:general).offer_expiration_days.to_i.days.from_now
      end

      expect(above_lending_loan.borrower_additional_info).to have_attributes(
        state: borrower_params[:state],
        address_street: borrower_params[:address_street],
        city: borrower_params[:city],
        phone_number: borrower_params[:phone_number],
        zip_code: borrower_params[:zip_code],
        married: borrower_params[:married]
      )

      expect(above_lending_loan.borrower).to have_attributes(
        email: borrower_params[:email],
        first_name: borrower_params[:first_name],
        last_name: borrower_params[:last_name],
        ssn: borrower_params[:ssn].gsub('-', ''),
        date_of_birth: Date.parse(borrower_params[:date_of_birth]),
        identity_id: borrower_params[:identity_id]
      )

      expect(above_lending_loan.loan_detail).to have_attributes(
        amount_financed:,
        beyond_enrollment_date: borrower_params[:beyond_enrollment_date].to_date,
        beyond_enrollment_status: 'Enrolled',
        estimated_beyond_fees: 0.0,
        estimated_cft_deposits: cft_params[:cft_account_balance],
        payment_adherence_ratio_3_months: borrower_params[:payment_adherence_ratio_3_months],
        payment_adherence_ratio_6_months: borrower_params[:payment_adherence_ratio_6_months],
        total_amount_enrolled_debt: BigDecimal(borrower_params[:total_amount_enrolled_debt]),
        consecutive_payments_count: borrower_params[:consecutive_payments_count].to_i
      )

      expect(above_lending_loan.loan_payment_detail).to have_attributes(
        beyond_payment_amount: borrower_params[:beyond_payment_amount],
        beyond_payment_frequency: nil,
        beyond_payment_dates: hash_including('dates' => [])
      )

      expect(above_lending_loan.latest_til_history).to have_attributes(
        docusign_envelope_id: til_history_params[:docusign_envelope_id],
        til_data: hash_including(
          'loan' => {
            'id' => above_lending_loan.id,
            'unified_id' => unified_id,
            'originatingParty' => selected_offer_params[:originating_party],
            'contractDate' => Date.parse(til_history_params.dig(:til_data, :loan, :contract_date)).strftime('%m/%d/%Y'),
            'agreementDate' => Date.parse(til_history_params.dig(:til_data, :loan, :agreement_date)).strftime('%m/%d/%Y'),
            'apr' => "#{format('%.2f', LoanProHelpers.parse_float(til_history_params.dig(:til_data, :loan, :apr)))}%",
            'cashoutAmount' => "$#{format('%.2f', selected_offer_params[:cashout_amount].to_f)}",
            'rawCashoutAmount' => selected_offer_params[:cashout_amount].to_f
          },
          'payment_schedule' => til_history_params.dig(:til_data, :payment_schedule).map do |s|
            {
              'amount' => s[:amount],
              'rawAmount' => s[:amount][1..].to_f,
              'number' => s[:number_of_payments].to_s,
              'rawNumber' => s[:number_of_payments].to_i,
              'due' => s[:start_date],
              'rawDueDate' => s[:raw_start_date]
            }
          end,
          'borrower' => {
            'first_name' => borrower_params[:first_name],
            'last_name' => borrower_params[:last_name],
            'address_street' => borrower_params[:address_street],
            'zip_code' => borrower_params[:zip_code]
          },
          'itemization' => {
            'amountFinanced' => "$#{format('%.2f', amount_financed)}",
            'rawAmountFinanced' => amount_financed.to_f
          }
        )
      )

      expect(above_lending_loan.todos.count).to eq(3)
      expect(above_lending_loan.todos.find { |t| t.type == AboveLending::Todo.types[:identity] }).to have_attributes(
        status: AboveLending::Todo.statuses[:approved]
      )
      expect(above_lending_loan.todos.find { |t| t.type == AboveLending::Todo.types[:bank] }).to have_attributes(
        status: AboveLending::Todo.statuses[:pending]
      )

      expect(above_lending_loan.docs.size).to eq(5)
      expect(above_lending_loan.docs.joins(:doc_template).find_by(doc_templates: { type: ila })).to be_present

      onboard_params[:consent_documents].each do |doc_params|
        expect(above_lending_loan.docs.joins(:doc_template).find_by(doc_templates: { type: doc_params[:document_type] })).to be_present
      end

      todo_docs = above_lending_loan.todos.map(&:todo_docs).flatten
      expect(todo_docs.count).to eq(3)
      expect(todo_docs.pluck(:todo_id)).to eq(above_lending_loan.todos.pluck(:id))
    end

    context 'when related records don\'t exist' do
      let!(:above_lending_loan) { AboveLending::Loan.create!(id: SecureRandom.uuid, unified_id: unified_id, amount: 10_000, request_id: SecureRandom.uuid, loan_app_status_id: initial_status.id) }

      before do
        described_class.call(unified_id: unified_id, params: onboard_params)
      end

      it 'should create the related loan payment detail' do
        expect(above_lending_loan.reload.loan_payment_detail).to be_present
      end

      it 'should create the related loan detail' do
        expect(above_lending_loan.reload.loan_detail).to be_present
      end

      it 'should create the bank account' do
        expect(above_lending_loan.bank_accounts.size).to eq(1)
      end

      it 'should create the borrower additional info' do
        expect(above_lending_loan.reload.borrower_additional_info).to be_present
      end

      it 'should create the borrower' do
        expect(above_lending_loan.reload.borrower).to be_present
      end

      it 'should create the til history' do
        expect(above_lending_loan.til_history.size).to eq(1)
      end

      it 'should create the docs' do
        expect(above_lending_loan.docs.size).to eq(5)
      end
    end

    context 'when unified_id does not exist in the database' do
      it 'should raise error' do
        expect { described_class.call(unified_id: 'x', params: onboard_params) }
          .to raise_error(RuntimeError, 'Unified ID not found: x')
      end
    end

    context 'when an attribute is malformed' do
      it 'should raise error' do
        expect { described_class.call(unified_id: unified_id, params: onboard_params.merge(borrower: { date_of_birth: 'invalid' })) }
          .to raise_error(Date::Error, 'invalid date')
      end
    end

    context 'when til history dates are malformed' do
      it 'raises an error for contract dates' do
        onboard_params[:til_history][:til_data][:loan][:contract_date] = 'invalid'
        expect { described_class.call(unified_id: unified_id, params: onboard_params) }
          .to raise_error(Date::Error, 'invalid date')
      end

      it 'raises an error for agreement dates' do
        onboard_params[:til_history][:til_data][:loan][:agreement_date] = 'invalid'
        expect { described_class.call(unified_id: unified_id, params: onboard_params) }
          .to raise_error(Date::Error, 'invalid date')
      end
    end

    context 'a loan is already onboarded' do
      before do
        above_lending_loan.update(loan_app_status: onboarded_status)
      end

      it 'should raise error' do
        expect { described_class.call(unified_id: unified_id, params: onboard_params) }
          .to raise_error(RuntimeError, "Loan already onboarded: #{unified_id}")
      end
    end

    context 'when no selected offer exists' do
      before do
        onboard_params[:offers].first[:selected] = false
      end

      it 'should raise error' do
        expect { described_class.call(unified_id: unified_id, params: onboard_params) }
          .to raise_error(RuntimeError, 'Loan has no selected offer')
      end
    end
  end
end
