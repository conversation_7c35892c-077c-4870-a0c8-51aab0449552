# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanManagement::OnboardLoans::AboveLending::UpsertConsentDocuments do
  subject { described_class.new(loan:, consent_documents_params:) }
  let(:bucket_name) { 'some-bucket' }
  let(:consent_documents_params) do
    [
      {
        document_type: 'PRIVACY_POLICY',
        s3_key: 'consent_documents/test/privacy-policy.pdf',
        s3_bucket: bucket_name,
        signed_at: Time.now.iso8601,
        ip_address: Faker::Internet.ip_v4_address
      },
      {
        document_type: 'TERMS_OF_USE',
        s3_key: 'consent_documents/test/terms-of-use.pdf',
        s3_bucket: bucket_name,
        signed_at: Time.now.iso8601,
        ip_address: Faker::Internet.ip_v4_address
      },
      {
        document_type: 'ESIGN_ACT_CONSENT',
        s3_key: 'consent_documents/test/esign-act-consent.pdf',
        s3_bucket: bucket_name,
        signed_at: Time.now.iso8601,
        ip_address: Faker::Internet.ip_v4_address
      },
      {
        document_type: 'CREDIT_PROFILE_AUTHORIZATION',
        s3_key: 'consent_documents/test/credit-profile-authorization.pdf',
        s3_bucket: bucket_name,
        signed_at: Time.now.iso8601,
        ip_address: Faker::Internet.ip_v4_address
      },
      {
        document_type: 'ELECTRONIC_FUND_TRANSFER_AUTH',
        s3_key: 'consent_documents/test/electronic_fund_transfer_auth.pdf',
        s3_bucket: bucket_name,
        signed_at: Time.now.iso8601,
        ip_address: Faker::Internet.ip_v4_address
      }
    ]
  end
  let(:loan) { create(:above_lending_loan) }

  describe '#call' do
    shared_examples 'successfully_updated_docs' do
      it 'successfully updates docs' do
        subject.call

        borrower = loan.borrower

        loan.docs.order(:created_at).each_with_index do |doc, index|
          template = doc.doc_template
          expect(template).to have_attributes(type: consent_documents_params[index][:document_type])
          expect(doc).to have_attributes(
            name: "#{template.name}_version_#{template.version}_#{borrower.first_name}_#{borrower.last_name}_#{doc.id}.pdf",
            uri: consent_documents_params[index][:s3_key],
            ip_address: doc.ip_address
          )
        end
      end
    end

    context 'when data is valid and docs do not exist' do
      before do
        consent_documents_params.each do |doc_params|
          create(:above_lending_doc_template, type: doc_params[:document_type])
        end
      end
      it 'creates new docs' do
        expect { subject.call }.to change(loan.docs, :count).by(5)
        expect(loan.docs.size).to eq(5)
      end
      include_examples 'successfully_updated_docs'
    end

    context 'when data is valid and docs already exist' do
      before do
        consent_documents_params.each do |doc_params|
          create(:above_lending_doc, loan:, doc_template: create(:above_lending_doc_template, type: doc_params[:document_type]))
        end
      end
      it 'updates existing docs' do
        expect { subject.call }.to_not change(loan.docs, :count)
        expect(loan.docs.size).to eq(5)
      end
      include_examples 'successfully_updated_docs'
    end

    context 'when missing template' do
      it 'should raise error' do
        expect { subject.call }.to raise_error("Could not find document template: #{consent_documents_params.first[:document_type]}")
      end
    end

    context 'with multiple templates' do
      before do
        consent_documents_params.each do |doc_params|
          create_list(:above_lending_doc_template, 2, type: doc_params[:document_type])
        end
      end

      it 'should use the latest version' do
        loan.docs.each do |doc|
          template = doc.doc_template
          expect(template.version).to eq(::AboveLending::DocTemplate.where(type: template.type).max(:version))
        end
      end
    end

    context 'when invalid type' do
      before { consent_documents_params.first[:document_type] = 'invalid' }

      it 'should raise error' do
        expect { subject.call }.to raise_error('Invalid document type: invalid')
      end
    end
  end
end
