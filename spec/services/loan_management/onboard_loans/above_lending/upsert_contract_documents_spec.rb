# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanManagement::OnboardLoans::AboveLending::UpsertContractDocuments do
  subject { described_class.new(contract_documents_params:, loan:, ip_address:) }

  let(:contract_documents_params) do
    [
      {
        document_type: 'TRUTH_IN_LENDING',
        signed_storage_key: "contract_documents/test/#{SecureRandom.uuid}/signed-1234567890.pdf"
      },
      {
        document_type: 'INSTALLMENT_LOAN_AGREEMENT',
        signed_storage_key: "contract_documents/test/#{SecureRandom.uuid}/signed-1234567890.pdf"
      },
      {
        document_type: 'MARYLAND_CREDIT_SERVICES_CONTRACT',
        signed_storage_key: "contract_documents/test/#{SecureRandom.uuid}/signed-1234567890.pdf"
      },
      {
        document_type: 'MARYLAND_NOTICE_OF_CANCELLATION',
        signed_storage_key: "contract_documents/test/#{SecureRandom.uuid}/signed-1234567890.pdf"
      }
    ]
  end

  let(:loan) { create(:above_lending_loan) }
  let(:ip_address) { Faker::Internet.ip_v4_address }
  let(:til) { 'CRB_TIL' }
  let(:ila) { 'CRB_INSTALLMENT_LOAN_AGREEMENT' }
  let(:mcsc) { 'CREDIT_SERVICES_CONTRACT_MARYLAND' }
  let(:mnoc) { 'NOTICE_OF_CANCELLATION_MARYLAND' }
  let!(:til_template) { create(:above_lending_doc_template, type: til) }
  let!(:ila_template) { create(:above_lending_doc_template, type: ila) }
  let!(:mcsc_template) { create(:above_lending_doc_template, type: mcsc) }
  let!(:mnoc_template) { create(:above_lending_doc_template, type: mnoc) }

  describe '#call' do
    shared_examples 'successfully_updated_docs' do
      it 'successfully updates docs' do
        subject.call

        til_document = loan.docs.joins(:doc_template).find_by(doc_templates: { type: til })
        ila_document = loan.docs.joins(:doc_template).find_by(doc_templates: { type: ila })
        mcsc_document = loan.docs.joins(:doc_template).find_by(doc_templates: { type: mcsc })
        mnoc_document = loan.docs.joins(:doc_template).find_by(doc_templates: { type: mnoc })

        borrower = loan.borrower

        expect(til_document).to have_attributes(
          template_id: til_template.id,
          name: "#{til_template.name}_version_#{til_template.version}_#{borrower.first_name}_#{borrower.last_name}_#{til_document.id}.pdf",
          uri: contract_documents_params.first[:signed_storage_key],
          ip_address:
        )

        expect(ila_document).to have_attributes(
          template_id: ila_template.id,
          name: "#{ila_template.name}_version_#{ila_template.version}_#{borrower.first_name}_#{borrower.last_name}_#{ila_document.id}.pdf",
          uri: contract_documents_params.second[:signed_storage_key],
          ip_address:
        )

        expect(mcsc_document).to have_attributes(
          template_id: mcsc_template.id,
          name: "#{mcsc_template.name}_version_#{mcsc_template.version}_#{borrower.first_name}_#{borrower.last_name}_#{mcsc_document.id}.pdf",
          uri: contract_documents_params.third[:signed_storage_key],
          ip_address:
        )

        expect(mnoc_document).to have_attributes(
          template_id: mnoc_template.id,
          name: "#{mnoc_template.name}_version_#{mnoc_template.version}_#{borrower.first_name}_#{borrower.last_name}_#{mnoc_document.id}.pdf",
          uri: contract_documents_params.fourth[:signed_storage_key],
          ip_address:
        )
      end
    end

    context 'when data is valid and docs do not exist' do
      it 'creates new docs' do
        expect { subject.call }.to change { AboveLending::Doc.count }.by(4)
        expect(loan.docs.size).to eq(4)
      end
      include_examples 'successfully_updated_docs'
    end

    context 'when data is valid and docs already exist' do
      before do
        create(:above_lending_doc, loan:, template_id: til_template.id)
        create(:above_lending_doc, loan:, template_id: ila_template.id)
        create(:above_lending_doc, loan:, template_id: mcsc_template.id)
        create(:above_lending_doc, loan:, template_id: mnoc_template.id)
      end

      it 'updates existing docs' do
        expect { subject.call }.to change { AboveLending::Doc.count }.by(0)
        expect(loan.docs.size).to eq(4)
      end
      include_examples 'successfully_updated_docs'
    end

    context 'when missing template' do
      let!(:ila_template) { nil }

      it 'should raise error' do
        expect { subject.call }.to raise_error("Could not find document template: #{ila}")
      end
    end

    context 'when invalid type' do
      before { contract_documents_params.first[:document_type] = 'invalid' }

      it 'should raise error' do
        expect { subject.call }.to raise_error('Invalid document type: invalid')
      end
    end
  end
end
