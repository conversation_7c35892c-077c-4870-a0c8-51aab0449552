# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanManagement::OnboardLoans::AboveLending::UpsertTodoManager do
  let(:loan) { create(:above_lending_loan, todos: []) }
  let(:verification_fraud_ss_card) do
    build_verification
  end
  let(:documents_ss_card) do
    build_document
  end
  let(:service_layer_url) { Rails.application.config_for(:general).service_layer_url }

  def build_verification(document_type: 'ID', status: 'accepted', type: 'fraud', logic: 'R907', rule_id: 'FS06', reason: 'N/A')
    {
      document_type:,
      verification_reasons: "#{type},#{logic},#{rule_id},#{reason}",
      status:
    }
  end

  def build_document(id: nil, tag: 'ID', mime_type: 'application/pdf', s3_bucket: 'abovelending', name: nil)
    @doc_id = id.present? ? id : @doc_id.to_i + 1
    s3_key = "#{@doc_id}-#{tag.parameterize}.pdf"
    tags = [tag]
    { id: @doc_id, name: name || tag, mime_type:, s3_bucket:, s3_key:, tags: }
  end

  context 'creates todos' do
    it 'assigns the correct statuses' do
      {
        'accepted' => 'approved',
        'rejected' => 'rejected',
        'requested' => 'submit',
        'uploaded' => 'review',
        'bad_status' => 'pending'
      }.each do |status, expected_status|
        verification_fraud_ss_card[:status] = status
        described_class.new(loan:, verifications: [verification_fraud_ss_card], verification_documents: [documents_ss_card]).call

        expect(loan.reload.todos.first.status).to eq(expected_status)
        loan.todos.destroy_all # clean up todos
      end
    end
  end

  context 'creates todo_docs' do
    it 'creates a todo_docs record' do
      verifications = [
        build_verification,
        build_verification(document_type: 'Social Security Doc', type: 'identity', logic: 'R934', rule_id: 'ID04', status: 'rejected')
      ]
      verification_documents = [
        build_document,
        build_document(name: 'SSD_test_1.pdf', tag: 'Social Security Doc'),
        build_document(name: 'SSD_test_2.pdf', tag: 'Social Security Doc')
      ]

      expect do
        described_class.new(loan:, verifications:, verification_documents:).call
      end.to change { AboveLending::TodoDoc.count }.by(3)

      todos = loan.reload.todos
      expect(todos.count).to eq(2)

      fraud_todo = todos.where(type: 'fraud').first
      identity_todo = todos.where(type: 'identity').first

      expect(fraud_todo.status).to eq('approved')
      expect(identity_todo.status).to eq('rejected')

      fraud_todo_doc = fraud_todo.todo_docs.first
      expect(fraud_todo_doc.status).to eq('approved')
      expect(fraud_todo_doc.name).to eq(verification_documents[0][:name])
      expect(fraud_todo_doc.external_id).to eq("#{verification_documents[0][:id]}-#{fraud_todo.id}")
      expect(fraud_todo_doc.mime_type).to eq(verification_documents[0][:mime_type])
      expect(fraud_todo_doc.s3_bucket).to eq(verification_documents[0][:s3_bucket])
      expect(fraud_todo_doc.s3_key).to eq(verification_documents[0][:s3_key])
      expect(fraud_todo_doc.url).to eq("#{service_layer_url}/api/todos/documents/#{fraud_todo_doc.id}")

      expect(identity_todo.todo_docs.count).to eq(2)
      identity_todo.todo_docs.sort_by(&:name).each_with_index do |todo_doc, index|
        expect(todo_doc.status).to eq('rejected')
        expect(todo_doc.name).to eq(verification_documents[index + 1][:name])
        expect(todo_doc.external_id).to eq("#{verification_documents[index + 1][:id]}-#{identity_todo.id}")
        expect(todo_doc.mime_type).to eq(verification_documents[index + 1][:mime_type])
        expect(todo_doc.s3_bucket).to eq(verification_documents[index + 1][:s3_bucket])
        expect(todo_doc.s3_key).to eq(verification_documents[index + 1][:s3_key])
        expect(todo_doc.url).to eq("#{service_layer_url}/api/todos/documents/#{todo_doc.id}")
      end
    end

    it 'creates multiple docs for multiline verification_reasons' do
      verifications = [
        {
          document_type: 'Bank Statement',
          verification_reasons: "bank,All Applications,BV01,All applications will trigger a bank account verification task\nnsf, , ,No data found",
          status: 'accepted'
        }
      ]
      verification_documents = [build_document(tag: 'Bank Statement')]

      expect do
        described_class.new(loan:, verifications:, verification_documents:).call
      end.to change { AboveLending::TodoDoc.count }.by(2)
         .and change { AboveLending::Todo.count }.by(2)

      todos = loan.reload.todos
      expect(todos.count).to eq(2)

      bank_todo = todos.where(type: 'bank').first
      nsf_todo = todos.where(type: 'nsf').first

      expect(bank_todo.status).to eq('approved')
      expect(nsf_todo.status).to eq('approved')

      [bank_todo, nsf_todo].each do |todo|
        todo_doc = todo.todo_docs.first
        expect(todo_doc.status).to eq('approved')
        expect(todo_doc.name).to eq(verification_documents[0][:name])
        expect(todo_doc.external_id).to eq("#{verification_documents[0][:id]}-#{todo.id}")
        expect(todo_doc.mime_type).to eq(verification_documents[0][:mime_type])
        expect(todo_doc.s3_bucket).to eq(verification_documents[0][:s3_bucket])
        expect(todo_doc.s3_key).to eq(verification_documents[0][:s3_key])
        expect(todo_doc.url).to eq("#{service_layer_url}/api/todos/documents/#{todo_doc.id}")
      end
    end

    it 'creates multiple docs for multiline verification_reasons' do
      verifications = [
        {
          document_type: 'Bank Statement',
          verification_reasons: "bank,All Applications,BV01,All applications will trigger a bank account verification task\nnsf, , ,No data found",
          status: 'accepted'
        }
      ]
      verification_documents = [build_document(tag: 'Bank Statement')]

      expect do
        described_class.new(loan:, verifications:, verification_documents:).call
      end.to change { AboveLending::TodoDoc.count }.by(2)
         .and change { AboveLending::Todo.count }.by(2)

      todos = loan.reload.todos
      expect(todos.count).to eq(2)

      bank_todo = todos.where(type: 'bank').first
      nsf_todo = todos.where(type: 'nsf').first

      expect(bank_todo.status).to eq('approved')
      expect(nsf_todo.status).to eq('approved')

      [bank_todo, nsf_todo].each do |todo|
        todo_doc = todo.todo_docs.first
        expect(todo_doc.status).to eq('approved')
        expect(todo_doc.name).to eq(verification_documents[0][:name])
        expect(todo_doc.external_id).to eq("#{verification_documents[0][:id]}-#{todo.id}")
        expect(todo_doc.mime_type).to eq(verification_documents[0][:mime_type])
        expect(todo_doc.s3_bucket).to eq(verification_documents[0][:s3_bucket])
        expect(todo_doc.s3_key).to eq(verification_documents[0][:s3_key])
        expect(todo_doc.url).to eq("#{service_layer_url}/api/todos/documents/#{todo_doc.id}")
      end
    end

    it 'handles single document for multiple todos' do
      verifications = [
        build_verification(document_type: 'Full Bank Account Doc', status: 'accepted', type: 'bank'),
        build_verification(document_type: 'Bank Statement', status: 'needs_review', type: 'bank')
      ]

      document1 = build_document(id: Faker::Number.number(digits: 11), tag: 'Bank Statement', name: 'Bank Doc')
      document2 = build_document(id: document1[:id], tag: 'Full Bank Account Doc', name: 'Bank Doc')
      verification_documents = [document1, document2]

      expect do
        described_class.new(loan:, verifications:, verification_documents:).call
      end.to change { AboveLending::TodoDoc.count }.by(2)
         .and change { AboveLending::Todo.count }.by(2)

      todos = loan.reload.todos
      expect(todos.count).to eq(2)

      bank_approved_todo = todos.where(type: 'bank', status: AboveLending::Todo.statuses[:approved]).first
      bank_pending_todo = todos.where(type: 'bank', status: AboveLending::Todo.statuses[:pending]).first

      expect(bank_approved_todo).to be
      expect(bank_pending_todo).to be

      [[bank_approved_todo, document2], [bank_pending_todo, document1]].each do |todo, input_doc|
        todo_doc = todo.todo_docs.first
        expect(todo_doc.status).to eq(todo_doc.status)
        expect(todo_doc.name).to eq(input_doc[:name])
        expect(todo_doc.external_id).to eq("#{input_doc[:id]}-#{todo.id}")
        expect(todo_doc.mime_type).to eq(input_doc[:mime_type])
        expect(todo_doc.s3_bucket).to eq(input_doc[:s3_bucket])
        expect(todo_doc.s3_key).to eq(input_doc[:s3_key])
        expect(todo_doc.url).to eq("#{service_layer_url}/api/todos/documents/#{todo_doc.id}")
      end
    end

    it 'handles mismatched verification documents' do
      verifications = [
        build_verification,
        build_verification(document_type: 'Social Security Doc', type: 'identity', logic: 'R934', rule_id: 'ID04', status: 'rejected')
      ]
      verification_documents = [
        build_document
      ]

      expect(Rails.logger).to receive(:error).with("UpsertTodoManager - verification_document tag 'Social Security Doc' was not found for loan #{loan.id}.")
      expect do
        described_class.new(loan:, verifications:, verification_documents:).call
      end.to change { AboveLending::TodoDoc.count }.by(1)
    end

    it 'logs an info for verification documents without tag and skips the document' do
      verifications = []
      verification_documents = [
        {
          id: Faker::Number.number(digits: 10),
          name: 'Untagged.pdf',
          mime_type: 'application/pdf',
          s3_bucket: 'abovelending',
          s3_key: 'key.pdf',
          tags: []
        }
      ]

      expect(Rails.logger).to receive(:info).with("UpsertTodoManager - Document #{verification_documents.first[:id]} does not have any tags. It will be omitted.")
      described_class.new(loan:, verifications:, verification_documents:).call
    end
  end
end
