# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanManagement::OnboardLoans::CreateBankAccount do
  subject { described_class.new(params) }
  let(:loan) { create(:loan) }
  let(:params) do
    {
      loan: loan,
      borrower: loan.borrower,
      bank_name: Faker::Bank.name,
      account_type: 'checking',
      account_number: Faker::Bank.account_number,
      routing_number: Faker::Bank.routing_number,
      enabled: true,
      allow_verify: true,
      fund_transfer_authorize: true,
      is_debt_settlement_account: Faker::Boolean.boolean,
      first_name: Faker::Name.first_name,
      last_name: Faker::Name.last_name
    }
  end

  it 'creates a new bank account' do
    expect do
      subject.call
    end.to change { BankAccount.all.size }.by(1)

    bank_account = BankAccount.find_by(loan_id: loan.id, borrower_id: loan.borrower_id)
    expect(bank_account.detail_info.bank_name).to eq(params[:bank_name])
    expect(bank_account.detail_info.account_type).to eq(params[:account_type])
    expect(bank_account.detail_info.account_number).to eq(params[:account_number])
    expect(bank_account.detail_info.routing_number).to eq(params[:routing_number])
    expect(bank_account.detail_info.is_debt_settlement_account).to eq(params[:is_debt_settlement_account])
    expect(bank_account.detail_info.account_holder.first_name).to eq(params[:first_name])
    expect(bank_account.detail_info.account_holder.last_name).to eq(params[:last_name])
  end
end
