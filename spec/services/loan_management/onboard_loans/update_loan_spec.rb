# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanManagement::OnboardLoans::UpdateLoan, type: :action do
  include ActiveJob::TestHelper
  include_context 'onboard_loan_common'

  let(:offers_params) { onboard_params[:offers] }
  let(:borrower_params) { onboard_params[:borrower] }
  let(:application_params) { onboard_params[:application] }
  let(:cft_account_params) { onboard_params[:cft_account] }
  let(:verification_params) { onboard_params[:verifications] }
  let(:verification_document_params) { onboard_params[:verification_documents] }
  let(:loan) { create(:loan) }
  let(:borrower) { loan.borrower }
  let(:loan_id) { loan&.id }

  before do
    perform_enqueued_jobs
    allow(LookupS3ObjectSize).to receive(:call) { |bucket:, key:| (bucket + key).length }
  end

  subject { described_class.new(loan_id: loan_id, params: onboard_params) }

  describe 'validations' do
    specify do
      expect(subject).to validate_presence_of(:loan_id)
      expect(subject).to validate_presence_of(:params)
    end

    context 'when no selected offer' do
      before do
        offers_params.first[:selected] = false
      end

      it 'raises an error' do
        expect { subject.call }.to raise_error(RuntimeError, '<PERSON>an has no selected offer')
      end
    end
  end

  context 'when passing valid data' do
    let!(:templates) { create_templates(multiple_versions: true) }

    it 'succeeds' do
      expected_borrower_data = {
        loan: loan,
        account_number: application_params[:account_number],
        email: borrower_params[:email],
        first_name: borrower_params[:first_name],
        last_name: borrower_params[:last_name],
        identity_id: borrower_params[:identity_id],
        ssn: borrower_params[:ssn],
        city: borrower_params[:city],
        state: borrower_params[:state],
        date_of_birth: borrower_params[:date_of_birth],
        address_street: borrower_params[:address_street],
        phone_number: borrower_params[:phone_number],
        zip_code: borrower_params[:zip_code]
      }

      expected_bank_account_data = {
        loan: loan,
        bank_name: application_params[:bank],
        borrower: borrower,
        account_type: application_params[:account_type].downcase,
        account_number: application_params[:account_number],
        routing_number: application_params[:routing_number],
        first_name: application_params[:holder_firstname],
        last_name: application_params[:holder_lastname],
        fund_transfer_authorize: application_params[:autopay_enabled],
        is_debt_settlement_account: false,
        enabled: true,
        allow_verify: nil
      }
      expected_cft_data = {
        loan: loan,
        bank_name: cft_account_params[:cft_bank_name],
        borrower: borrower,
        account_type: 'checking',
        account_number: cft_account_params[:cft_account_number],
        routing_number: cft_account_params[:cft_routing_number],
        first_name: cft_account_params[:cft_account_holder_name],
        enabled: true,
        fund_transfer_authorize: true,
        is_debt_settlement_account: true,
        allow_verify: nil
      }
      funding = create(:funding, loan: loan)

      expect(LoanManagement::OnboardLoans::UpsertBorrower)
        .to receive(:call)
        .with(expected_borrower_data)
        .and_return(borrower)

      expect(LoanManagement::OnboardLoans::CreateBankAccount)
        .to receive(:call)
        .with(expected_bank_account_data)

      expect(LoanManagement::OnboardLoans::CreateBankAccount)
        .to receive(:call)
        .with(expected_cft_data)

      expect(LoanManagement::OnboardLoans::UpsertFunding)
        .to receive(:call)
        .with(loan:, params: onboard_params)
        .and_return(funding)

      expect(LoanManagement::OnboardLoans::UpsertFundingValidation)
        .to receive(:call)
        .with(funding:, params: onboard_params)

      subject.call

      expect(loan.reload.document_info.count).to eq(5)
      expected_document_info = [
        *onboard_params[:consent_documents].map.with_index do |doc, i|
          {
            'id' => UuidValidator::REGEX,
            'template_id' => templates[i].id,
            'created_at' => an_instance_of(Time),
            'name' => /#{templates[i].detail_info.name}.*#{templates[i].detail_info.version}.*#{borrower.detail_info.first_name}.*#{borrower.detail_info.last_name}.*#{loan.document_info[i].id}/,
            'uri' => doc[:s3_key],
            'signed_at' => Time.parse(doc[:signed_at]),
            'file_size_bytes' => (doc[:s3_bucket] + doc[:s3_key]).length,
            'ip_address' => doc[:ip_address],
            'updated_at' => nil,
            'type' => doc[:document_type],
            'mime_type' => nil,
            'storage_bucket' => doc[:s3_bucket],
            'storage_key' => doc[:s3_key],
            'inputs' => anything
          }
        end,
        *onboard_params[:contract_documents].map.with_index do |doc, i|
          {
            'id' => UuidValidator::REGEX,
            'template_id' => templates[4 + i].id,
            'created_at' => an_instance_of(Time),
            'name' => /#{templates[4 + i].detail_info.name}.*#{templates[4 + i].detail_info.version}.*#{borrower.detail_info.first_name}.*#{borrower.detail_info.last_name}.*#{loan.document_info[4 + i].id}/, # rubocop:disable Layout/LineLength
            'uri' => doc[:signed_storage_key],
            'signed_at' => Time.parse(doc[:signed_at]),
            'file_size_bytes' => doc[:file_size_bytes],
            'ip_address' => onboard_params[:application][:ip_address],
            'updated_at' => nil,
            'type' => doc[:document_type],
            'mime_type' => nil,
            'storage_bucket' => doc[:bucket_name],
            'storage_key' => doc[:signed_storage_key],
            'inputs' => anything
          }
        end
      ]
      expect(loan.document_info.map(&:attributes)).to match(expected_document_info)

      # Persists document-specific inputs
      installment_loan_agreement_document = loan.document_info.find { |doc| doc.type == 'INSTALLMENT_LOAN_AGREEMENT' }
      expect(installment_loan_agreement_document.inputs.attributes.as_json).to match(installment_loan_agreement_document_params[:inputs].as_json)
    end

    context 'with no Informative credit report document' do
      it 'does not trigger the ProcessDecisionEngineDocumentsJob if no Informative credit report exists for the loan' do
        onboard_params[:decision_engine_documents].delete_if { |doc| doc[:name] == 'informative_soft_pull' }
        subject.call
        expect(LoanManagement::OnboardLoans::ProcessDecisionEngineDocumentsJob).not_to have_been_enqueued
      end
    end

    context 'with Informative credit report document' do
      it 'triggers the ProcessDecisionEngineDocumentsJob' do
        expect(onboard_params[:decision_engine_documents].find { |doc| doc[:name] == 'informative_soft_pull' }).to be_present
        expect(LoanManagement::OnboardLoans::ProcessDecisionEngineDocumentsJob).to receive(:perform_async).with(loan.id)
        subject.call
      end
    end

    context 'without cft data' do
      it 'does not create a CFT bank account' do
        expect(LoanManagement::OnboardLoans::UpsertBorrower)
          .to receive(:call)
          .and_return(borrower)

        expect(LoanManagement::OnboardLoans::CreateBankAccount)
          .to receive(:call).once

        described_class.new(loan_id: loan.id, params: onboard_params.except(:cft_account)).call
      end
    end

    Application::DebtSettlementDetail.new.payment_frequency_values.values do |value|
      context "when using '#{value}' as beyond payment frequency" do
        before do
          onboard_params[:borrower][:beyond_payment_frequency] = value
          subject.call
        end

        it "should set the debt settlement payment frequency to '#{value}'" do
          expect(loan.funding.funding_validation.debt_settlement_info.payment_frequency).to eq(value)
        end
      end
    end

    Application::Employment.new.pay_frequency_values.each_value do |value|
      context "when using '#{value}' as employment payment frequency" do
        before do
          onboard_params[:application][:employment_pay_frequency] = value
          subject.call
        end

        it "should set the applicant employment pay frequency to '#{value}'" do
          expect(loan.funding.funding_validation.application_info.applicant.employment.pay_frequency).to eq(value)
        end
      end
    end
  end

  describe 'when missing argument' do
    context 'loan_id' do
      let(:loan) { nil }

      it 'raises a validation error' do
        expect { subject.call }.to raise_error(ActiveModel::ValidationError, "Validation failed: Loan can't be blank")
      end
    end

    context 'params' do
      let(:onboard_params) { nil }

      it 'raises a validation error' do
        expect { subject.call }.to raise_error(ActiveModel::ValidationError, "Validation failed: Params can't be blank")
      end
    end
  end

  describe 'when no selected offer' do
    before do
      offers_params.first[:selected] = false
    end

    it 'raises an error' do
      expect { subject.call }.to raise_error(RuntimeError, 'Loan has no selected offer')
    end
  end
end
