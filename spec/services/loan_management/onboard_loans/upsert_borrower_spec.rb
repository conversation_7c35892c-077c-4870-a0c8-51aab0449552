# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanManagement::OnboardLoans::UpsertBorrower do
  subject { described_class.new(params) }

  context 'when valid params' do
    let(:params) do
      {
        **person_data,
        **location_data,
        **contact_data,
        loan: loan,
        account_number: Faker::Bank.account_number,
        identity_id: SecureRandom.uuid
      }
    end

    let(:person_data) do
      {
        first_name: Faker::Name.unique.first_name,
        last_name: Faker::Name.unique.initials,
        date_of_birth: Faker::Date.between(from: 60.years.ago, to: 18.years.ago).strftime('%Y-%m-%d'),
        ssn: '***********'
      }
    end

    let(:location_data) do
      {
        address_street: Faker::Address.street_address,
        city: Faker::Address.city,
        state: Faker::Address.state_abbr,
        zip_code: Faker::Address.zip.first(5)
      }
    end

    let(:contact_data) do
      {
        email: Faker::Internet.email,
        phone_number: Faker::PhoneNumber.cell_phone
      }
    end

    shared_examples 'saves_data_correctly' do
      it 'saves borrower with correct data' do
        borrower = subject.call
        expect(borrower).to be_an_instance_of(<PERSON>rrow<PERSON>)

        person_data.each do |k, v|
          expect(borrower.detail_info.send(k).to_s).to eq(v)
        end

        location_data.except(:address_street).each do |k, v|
          expect(borrower.detail_info.send(k).to_s).to eq(v)
        end
        expect(borrower.detail_info.address1).to eq(location_data[:address_street])

        expect(borrower.detail_info.email.address).to eq(contact_data[:email])
        expect(borrower.detail_info.email.type).to eq('primary')
        expect(borrower.detail_info.phone.number).to eq(contact_data[:phone_number])
        expect(borrower.detail_info.phone.type).to eq('mobile')

        expect(borrower.account_number).to eq(params[:account_number].to_i)
        expect(borrower.identity_id).to eq(params[:identity_id])
        expect(borrower.platform_id).to be_nil
        expect(borrower.platform_info.external_borrower_id).to be_nil
      end
    end

    context 'when Borrower already assigned to loan' do
      let(:loan) { create(:loan) }

      it 'creates Borrower' do
        borrower = subject.call
        expect(borrower.id).to eq(loan.borrower.id)
      end

      include_examples 'saves_data_correctly'
    end

    context 'when Borrower does not exist' do
      let(:loan) { nil }

      it 'creates Borrower' do
        expect { subject.call }.to change(Borrower, :count).by(1)
      end

      include_examples 'saves_data_correctly'
    end
  end

  context 'when invalid params' do
    let(:params) do
      {
        date_of_birth: 'xxx',
        phone_number: 'invalid',
        email: '123'
      }
    end

    it 'does not save Borrower' do
      expect { subject.call }
        .to raise_error(ActiveRecord::RecordInvalid,
                        'Validation failed: Account number is not a number, ' \
                        'Detail info {:date_of_birth=>["is not a date"], ' \
                        ':email=>[{:address=>["is invalid"]}]}')
    end

    context 'when loan type does not match' do
      let(:params) { { loan: 'not_a_loan' } }

      it 'does not save Borrower' do
        expect { subject.call }.to raise_error(ArgumentError, 'Expected type of `Loan`. Received `String`')
      end
    end
  end
end
