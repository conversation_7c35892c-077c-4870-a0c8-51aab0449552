# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanManagement::OnboardLoans::UpsertFunding do
  include_context 'onboard_loan_common'

  subject { described_class.new(loan:, params: onboard_params) }

  let(:funding_params) { onboard_params[:funding] }

  context 'when Funding is already assigned to loan' do
    let(:loan) { create(:loan) }
    let!(:funding) { create(:funding, loan:, detail_info: Funding::Detail.new(processing_system: 'Legacy')) }

    it 'does not create new Funding' do
      expect { subject.call }.not_to change(Funding, :count)
    end

    it 'updates the processing system assigned to the Funding record' do
      subject.call

      expect(funding.reload.detail_info.processing_system).to eq('Dash')
    end
  end

  context 'when Funding does not exist' do
    let(:loan) { create(:loan, funding: nil) }

    it 'creates Funding' do
      expect do
        funding = subject.call
        expect(funding.loan_id).to eq(loan.id)
      end.to change(Funding, :count).by(1)
    end

    it 'assigns the specified processing system to the Funding record' do
      subject.call

      expect(loan.funding.detail_info.processing_system).to eq('Dash')
    end
  end

  context 'when the Legacy processing system is specified' do
    let(:loan) { create(:loan, funding: nil) }
    let(:processing_system) { 'Legacy' }

    before do
      funding_params[:processing_system] = processing_system
    end

    it 'assigns this as the processing system for the Funding record' do
      subject.call

      expect(loan.funding.reload.detail_info.processing_system).to eq(processing_system)
    end
  end

  context 'when an invalid processing system is specified' do
    let(:loan) { create(:loan, funding: nil) }

    before do
      funding_params[:processing_system] = 'unknown'
    end

    it 'raises an error' do
      expect { subject.call }.to raise_error(ArgumentError, "invalid value 'unknown' is assigned")
    end
  end

  context 'when loan is missing' do
    let(:loan) { nil }

    it 'raises error' do
      expect { subject.call }.to raise_error(ActiveModel::ValidationError, "Validation failed: Loan can't be blank")
    end
  end

  context 'when loan type does not match' do
    let(:loan) { 'not_a_loan' }

    it 'raises error' do
      expect { subject.call }.to raise_error(ArgumentError, 'Expected type of `Loan`. Received `String`')
    end
  end
end
