# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanManagement::OnboardLoans::UpsertFundingValidation do
  include ActiveSupport::Testing::TimeHelpers
  include_context 'onboard_loan_common'

  subject { described_class.new(funding:, params: onboard_params) }

  let(:offers_params) { onboard_params[:offers] }
  let(:borrower_params) { onboard_params[:borrower] }
  let(:application_params) { onboard_params[:application] }
  let(:cft_account_params) { onboard_params[:cft_account] }
  let(:verifications) { onboard_params[:verifications] }
  let(:verification_documents) { onboard_params[:verification_documents] }
  let(:ip_address) { application_params[:ip_address] }
  let(:decision_engine_documents_params) { onboard_params[:decision_engine_documents] }
  let(:valid_verification_documents) do
    verification_documents.filter do |document|
      verifications.find { |v| v[:document_type] == document[:tags].first }
    end
  end
  let(:funding) { create(:funding) }

  before do
    allow(LookupS3ObjectSize).to receive(:call) { |bucket:, key:| (bucket + key).length }
  end

  context 'when funding_validation does not exist' do
    it 'creates funding_validation' do
      expect do
        funding_validation = subject.call
        expect(funding_validation.application_info).to be_present
        expect(funding_validation.offer_info.count).to eq(2)
      end.to change { FundingValidation.count }.by(1)
    end
  end

  context 'when funding_validation exists' do
    let!(:funding_validation) { create(:funding_validation, funding:) }

    it 'only updates funding_validation' do
      uuid = SecureRandom.uuid
      allow(SecureRandom).to receive(:uuid).and_return(uuid)

      freeze_time

      expect do
        subject.call
      end.to change { FundingValidation.count }.by(0)

      funding_validation.reload
      expect(funding_validation.application_info.status).to eq('ONBOARDED')
      expect(funding_validation.application_info.created_at).to eq(DateTime.parse(application_params[:created_at]))
      expect(funding_validation.application_info.submitted_at).to eq(Time.zone.now)

      applicant = funding_validation.application_info.applicant
      expect(applicant.date_of_birth.strftime('%Y-%m-%d')).to eq(borrower_params[:date_of_birth])

      decision_info = funding_validation.decision_info
      expect(decision_info.status).to eq('DECLINED')
      expect(decision_info.adverse_action.reason_text).to eq(application_params[:decline_reason_messages].first)
      expect(decision_info.adverse_action.decline_reasons).to eq(application_params[:decline_reason_messages])
      expect(decision_info.adverse_action.is_sent).to eq(application_params[:noaa_successfully_sent_date].present?)
      expect(decision_info.adverse_action.sent_at).to eq(application_params[:noaa_successfully_sent_date])
      expect(decision_info.credit.score).to eq(application_params[:credit_score_number].to_s)
      expect(decision_info.credit.score_range).to eq(application_params[:credit_score_range])
      expect(decision_info.credit.score_recorded_at).to eq(application_params[:credit_score_retrieval_date])
      expect(decision_info.credit.hard_pull_at).to eq(application_params[:hard_credit_pull_requested_at])
      expect(decision_info.credit.dti).to eq(application_params[:debt_to_income_ratio] * 100)

      expect(funding_validation.offer_info.count).to eq(2)
      funding_validation.offer_info.each_with_index do |offer_info, index|
        offer_params = offers_params[index]
        expect(offer_info.type).to eq('regular')
        expect(offer_info.description).to eq(offer_params[:description])
        expect(offer_info.is_hero).to eq(offer_params[:is_hero])
        expect(offer_info.is_selected).to eq(offer_params[:selected])
        expect(offer_info.policy_version).to eq(offer_params[:policy_version])
        expect(offer_info[:expiration_at])
          .to be_within(1.second)
          .of Rails.application.config_for(:general).offer_expiration_days.to_i.days.from_now

        term = offer_info.terms
        expect(term.origination_amount)
          .to eq(offer_params[:settlement_amount] +
                  offer_params[:cashout_amount] +
                  offer_params[:origination_fee])
        expect(term.finance_amount)
          .to eq(offer_params[:settlement_amount] +
                  offer_params[:cashout_amount])
        expect(term.cashout_amount).to eq(offer_params[:cashout_amount])
        expect(term.settlement_amount).to eq(offer_params[:settlement_amount])
        expect(term.origination_fee).to eq(offer_params[:origination_fee])
        expect(term.origination_fee_percent).to eq(offer_params[:origination_fee_percent])
        expect(term.originating_party).to eq(offer_params[:originating_party])
        expect(term.initial_term_payment).to eq(offer_params[:initial_term_payment])
        expect(term.final_term_payment).to eq(offer_params[:final_term_payment])
        expect(term.interest_rate).to eq(offer_params[:interest_rate])
        expect(term.term_length).to eq(offer_params[:term].to_i)

        debt_settlement_info = funding_validation.debt_settlement_info
        expect(debt_settlement_info).to have_attributes({
                                                          payment_frequency: '',
                                                          payment_amount: borrower_params[:beyond_payment_amount],
                                                          enrolled_debt_amount: borrower_params[:total_amount_enrolled_debt],
                                                          enrolled_at: Date.parse(borrower_params[:beyond_enrollment_date]),
                                                          enrollment_status: 'Enrolled',
                                                          debt_resolution_program_name: borrower_params[:service_entity_name]
                                                        })
        metrics = debt_settlement_info.metrics
        expect(metrics).to have_attributes({
                                             payment_adherence_ratio_3_months: borrower_params[:payment_adherence_ratio_3_months],
                                             payment_adherence_ratio_6_months: borrower_params[:payment_adherence_ratio_6_months],
                                             cft_account_balance: cft_account_params[:cft_account_balance],
                                             consecutive_payments_count: borrower_params[:consecutive_payments_count]
                                           })

        employment = funding_validation.application_info.applicant.employment
        expect(employment).to have_attributes({
                                                status: application_params[:employment_status],
                                                industry: application_params[:employment_industry],
                                                annual_income: application_params[:annual_income],
                                                pay_frequency: application_params[:employment_pay_frequency]
                                              })

        housing = funding_validation.application_info.applicant.housing
        expect(housing).to have_attributes({
                                             monthly_payment: application_params[:housing_payment_monthly],
                                             time_at_residence: application_params[:time_at_residence]
                                           })
      end

      funding_documents = funding_validation.document_info.funding_documents
      expect(funding_documents.length).to eq(6)
      expect(funding_documents.all? { |doc| doc.is_a?(FundingValidation::FundingDocument) }).to eq(true)

      expect(valid_verification_documents.length).to eq(3)
      valid_verification_documents.each do |document|
        verification = verifications.find { |v| v[:document_type] == document[:tags].first }
        type = LoanManagement::OnboardLoans::PrepareFundingDocumentFromVerification::TYPE_MAPPING[verification[:verification_reasons].split(',').first]
        funding_document = funding_documents.find { |d| d.name == document[:name] }
        expect(funding_document).to have_attributes({
                                                      id: uuid,
                                                      type:,
                                                      name: document[:name],
                                                      mime_type: document[:mime_type],
                                                      file_size_bytes: (document[:s3_bucket] + document[:s3_key]).length,
                                                      storage_bucket: document[:s3_bucket],
                                                      storage_key: document[:s3_key],
                                                      uri: document[:s3_key],
                                                      ip_address:
                                                    })
        expect(funding_document.created_at).to be_present
        expect(funding_document.updated_at).to be_present
      end

      %w[informative_soft_pull socure giact].each do |report_type|
        document = funding_documents.find { |doc| doc.name == report_type }
        matching_param = decision_engine_documents_params.find { |param| param[:name] == report_type }
        expect(document).to be_present
        expect(matching_param).to be_present

        expect(document).to have_attributes(
          created_at: matching_param[:created_at].to_time,
          file_size_bytes: matching_param[:file_size_bytes],
          mime_type: matching_param[:mime_type],
          type: LoanManagement::OnboardLoans::PrepareFundingDocumentFromDecisionEngine::TYPE_MAPPING[matching_param[:name]],
          name: matching_param[:name],
          storage_bucket: matching_param[:storage_bucket],
          storage_key: matching_param[:storage_key]
        )
      end
    end

    it 'allows finance_amount to be specified' do
      uuid = SecureRandom.uuid
      allow(SecureRandom).to receive(:uuid).and_return(uuid)

      freeze_time

      onboard_params[:offers] = onboard_params[:offers].map { |offer| { finance_amount: 100.00, **offer } }

      expect do
        subject.call
      end.to change { FundingValidation.count }.by(0)

      funding_validation.reload

      funding_validation.offer_info.each do |offer_info|
        expect(offer_info.terms.finance_amount).to eq(100.00)
      end
    end
  end

  context "when verification doc can't be connected to verification" do
    before do
      onboard_params[:verifications][2][:document_type] = 'wrong tag'
    end

    it 'should not include document' do
      funding_validation = subject.call
      # Includes three decision engine documents
      expect(funding_validation.document_info.funding_documents.length).to eq(5)
    end
  end

  context 'when calculating the verification type for each document' do
    let(:verification_id) { SecureRandom.uuid }
    let(:verification_data) do
      {
        document_type: verification_id,
        verification_reasons: nil,
        status: 'accepted'
      }
    end
    let(:verification_doc_data) do
      {
        id: '1',
        name: 'Test Document',
        mime_type: 'application/pdf',
        s3_bucket: 'abovelending',
        s3_key: '1-document-key.pdf',
        tags: [verification_id]
      }
    end

    it 'should assign the appropriate document type based on the verification reason of the associated verification record' do
      [
        { reason_key: 'bank', doc_type: FundingValidation::FundingDocument::BANK_VERIFICATION },
        { reason_key: 'identity', doc_type: FundingValidation::FundingDocument::IDENTITY_VERIFICATION },
        { reason_key: 'income', doc_type: FundingValidation::FundingDocument::INCOME_VERIFICATION },
        { reason_key: 'payment_adherence', doc_type: FundingValidation::FundingDocument::PAYMENT_ADHERENCE_VERIFICATION },
        { reason_key: 'residence', doc_type: FundingValidation::FundingDocument::RESIDENCE_VERIFICATION },
        { reason_key: 'unknown', doc_type: '' }
      ].each do |scenario|
        document_name = "#{SecureRandom.uuid}.pdf"

        onboard_params[:verifications] = [verification_data.merge(verification_reasons: "#{scenario[:reason_key]},LOGIC,RULE_ID,REASON")]
        onboard_params[:verification_documents] = [verification_doc_data.merge(name: document_name)]

        funding_validation = subject.call

        recorded_document = funding_validation.document_info.funding_documents.detect { |doc| doc.name == document_name }
        expect(recorded_document).to be_present
        expect(recorded_document.type).to eq(scenario[:doc_type])
      end
    end

    it 'gracefully handles verification records with no verification reason' do
      document_name = "#{SecureRandom.uuid}.pdf"

      onboard_params[:verifications] = [verification_data.merge(verification_reasons: nil)]
      onboard_params[:verification_documents] = [verification_doc_data.merge(name: document_name)]

      funding_validation = subject.call

      recorded_document = funding_validation.document_info.funding_documents.detect { |doc| doc.name == document_name }
      expect(recorded_document).to be_present
      expect(recorded_document.type).to eq('')
    end
  end

  context 'when empty decline_reason_messages' do
    it 'properly sets decision_info status' do
      onboard_params[:application][:decline_reason_messages] = []

      funding_validation = subject.call
      expect(funding_validation.decision_info.status).to eq('APPROVED')
    end
  end

  context 'when params are invalid' do
    it 'fails' do
      onboard_params[:borrower][:date_of_birth] = 'invalid'
      expected_error = 'Validation failed: Application info '\
                       '{:applicant=>[{:date_of_birth=>["is not a date"]}]}'
      expect { subject.call }.to raise_error(ActiveRecord::RecordInvalid, expected_error)
    end
  end

  context 'when funding is missing' do
    let(:funding) { nil }

    it 'fails' do
      expected_error = "Validation failed: Funding can't be blank"
      expect { subject.call }.to raise_error(ActiveModel::ValidationError, expected_error)
    end
  end
end
