# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanManagement::PaymentHistory do
  include NotifierHelper

  let!(:loan) { create(:above_lending_loan, :loanpro) }
  let!(:borrower) { loan.borrower }
  let!(:borrower_id) { loan.borrower.id }
  let!(:loan_id) { loan.loanpro_loan.loanpro_loan_id }

  before { allow(ActiveSupport::Notifications).to receive(:instrument).and_call_original }

  describe '.call' do
    let(:api_today1) { Date.today.to_time }
    let(:api_today2) { (Date.today - 1.day).to_time }
    let(:api_today3) { (Date.today - 2.day).to_time }
    let(:api_today4) { (Date.today - 3.day).to_time }

    before { allow(LoanProService::LoanManagementSystem::PaymentHistory).to receive(:call).with(loan_id: loan_id).and_return(lp_payments_response) }

    let(:lp_payments_response) do
      [
        { id: 19_459, amount: '610.71', date: "/Date(#{api_today3.to_i})/", status: 'payment.status.success', info: 'AutoPay: Customer Initiated Payment',
          afterPrincipalBalance: '8400.00', transaction: { paymentInterest: '210.71', paymentPrincipal: '400.00' } },
        { id: 19_462, amount: '610.71', date: "/Date(#{api_today1.to_i})/", status: 'payment.status.success', info: 'AutoPay: Customer Initiated Payment',
          afterPrincipalBalance: '8000.00', transaction: { paymentInterest: '210.71', paymentPrincipal: '400.00' } },
        { id: 19_458, amount: '610.71', date: "/Date(#{api_today2.to_i})/", status: 'payment.status.success', info: 'AutoPay: scheduled payment',
          afterPrincipalBalance: '8400.00', transaction: { paymentInterest: '210.71', paymentPrincipal: '400.00' } },
        { id: 19_458, amount: '610.71', date: "/Date(#{api_today2.to_i})/", status: 'payment.status.success', info: 'representment of payment on 2024-02-08 19:00:00 UTC - first_recycle_scheduled',
          afterPrincipalBalance: '8800.00', transaction: { paymentInterest: '210.71', paymentPrincipal: '400.00' } },
        { id: 19_460, amount: '610.71', date: "/Date(#{api_today4.to_i})/", status: 'payment.status.success', info: 'Other',
          afterPrincipalBalance: '9600.00', transaction: { paymentInterest: '210.71', paymentPrincipal: '400.00' } }
      ].map(&:deep_stringify_keys)
    end

    it 'Returns an array of pending payments' do
      payments = [
        { id: 19_462, amount: '610.71', date: api_today1.iso8601, type: 'One-Time', status: 'success', isCustomerInitiated: true,
          interest: '210.71', principal: '400.00', afterBalance: '8000.00' },
        { id: 19_458, amount: '610.71', date: api_today2.iso8601, type: 'Representment', status: 'success', isCustomerInitiated: false,
          interest: '210.71', principal: '400.00', afterBalance: '8800.00' },
        { id: 19_458, amount: '610.71', date: api_today2.iso8601, type: 'Auto Pay', status: 'success', isCustomerInitiated: false,
          interest: '210.71', principal: '400.00', afterBalance: '8400.00' }
      ]

      expect(described_class.call(limit: 3, offset: 0, loan_id: loan_id)).to eq({ payments:, count: 3, loanpro_loan_id: loan_id })
    end

    it 'Returns a second page of payments' do
      payments = [
        { id: 19_459, amount: '610.71', date: api_today3.iso8601, type: 'One-Time', status: 'success', isCustomerInitiated: true,
          interest: '210.71', principal: '400.00', afterBalance: '8400.00' },
        { id: 19_460, amount: '610.71', date: api_today4.iso8601, type: 'Unknown', status: 'success', isCustomerInitiated: false,
          interest: '210.71', principal: '400.00', afterBalance: '9600.00' }
      ]

      expect(described_class.call(limit: 2, offset: 3, loan_id: loan_id)).to eq({ payments:, count: 2, loanpro_loan_id: loan_id })
    end
  end

  describe 'service errors' do
    let!(:error_message) { 'Internal Server Error' }

    before { expect(LoanProService::LoanManagementSystem::PaymentHistory).to receive(:call).with(loan_id: loan_id).and_raise(error) }

    subject { described_class.call(loan_id: loan_id) }

    context 'when error->message->value is present in API response and status is 200' do
      let(:error) { LoanProService::Exceptions::LoanManagementSystemError.new('Internal Server Error') }

      it 'raises a LoanManagement::PaymentProfiles::PaymentProfilesSystemError with the error message' do
        expect { subject }.to raise_error(LoanManagement::PaymentHistory::PaymentHistorySystemError, error_message)
        expect_to_notify('payment_history', success: false, fail_reason: 'Internal Server Error')
      end
    end

    context 'when unexpectedly formatted error is present in API response and status is 200' do
      let(:error) { LoanProService::Exceptions::LoanManagementSystemError.new('{"test"=>"unexpected error"}') }

      it 'raises a LoanManagementSystemError' do
        expect { subject }.to raise_error(LoanManagement::PaymentHistory::PaymentHistorySystemError, error_message)
        expect_to_notify('payment_history', success: false, fail_reason: '{"test"=>"unexpected error"}')
      end
    end

    context 'when status is 4xx (ClientError) but no response body is present' do
      let(:error) { LoanProService::Exceptions::LoanManagementSystemError.new('the server responded with status 403') }

      it 'raises a LoanManagementSystemError with status' do
        expect { subject }.to raise_error(LoanManagement::PaymentHistory::PaymentHistorySystemError, error_message)
        expect_to_notify('payment_history', success: false, fail_reason: 'the server responded with status 403')
      end
    end
  end
end
