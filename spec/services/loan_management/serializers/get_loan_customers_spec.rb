# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanManagement::Serializers::GetLoanCustomersSerializer do
  context '.call' do
    let(:loan_customers) do
      [
        {
          '__metadata' => { 'uri' => 'http://loanpro.simnang.com/api/public/api/1/odata.svc/Customers(id=9507)', 'type' => 'Entity.Customer' },
          'PrimaryAddress' => { '__deferred' => { 'uri' => 'Customers(id=9507)/PrimaryAddress' } },
          'MailAddress' => { '__deferred' => { 'uri' => 'Customers(id=9507)/MailAddress' } },
          'Employer' => { '__deferred' => { 'uri' => 'Customers(id=9507)/Employer' } },
          'References' => { '__deferred' => { 'uri' => 'Customers(id=9507)/References' } },
          'PaymentAccounts' => { '__deferred' => { 'uri' => 'Customers(id=9507)/PaymentAccounts' } },
          'Phones' => { '__deferred' => { 'uri' => 'Customers(id=9507)/Phones' } },
          'CustomFieldValues' => { '__deferred' => { 'uri' => 'Customers(id=9507)/CustomFieldValues' } },
          'Documents' => { '__deferred' => { 'uri' => 'Customers(id=9507)/Documents' } },
          'CreditScore' => { '__deferred' => { 'uri' => 'Customers(id=9507)/CreditScore' } },
          'Loans' => { '__deferred' => { 'uri' => 'Customers(id=9507)/Loans' } },
          'LineOfCredits' => { '__deferred' => { 'uri' => 'Customers(id=9507)/LineOfCredits' } },
          'SocialProfiles' => { '__deferred' => { 'uri' => 'Customers(id=9507)/SocialProfiles' } },
          'Notes' => { '__deferred' => { 'uri' => 'Customers(id=9507)/Notes' } },
          'id' => 9507,
          'customId' => '9507',
          'mcId' => 5_877_616,
          'customerType' => 'customer.type.individual',
          'status' => 'Active',
          'firstName' => 'ERICA',
          'lastName' => 'LAMBERT',
          'middleName' => nil,
          'birthDate' => '/Date(**********)/',
          'gender' => 'customer.gender.unknown',
          'generationCode' => 'customer.generationCode.none',
          'email' => '<EMAIL>',
          'ssn' => '*********',
          'driverLicense' => nil,
          'companyName' => nil,
          'contactName' => nil,
          'customerIdType' => 'customer.idType.ssn',
          'customerId' => nil,
          'creditLimit' => 0,
          'accessUserName' => '<EMAIL>',
          'active' => 1,
          'ofacMatch' => 0,
          'ofacTested' => 0,
          'saleTransferPii' => 1,
          'passwordChanged' => 0,
          'hasAvatar' => 0,
          'loanRole' => 'loan.customerRole.primary',
          'created' => '/Date(**********)/',
          'lastUpdate' => '/Date(**********)/',
          'creditScoreId' => nil
        },
        {
          '__metadata' => { 'uri' => 'http://loanpro.simnang.com/api/public/api/1/odata.svc/Customers(id=16193)', 'type' => 'Entity.Customer' },
          'PrimaryAddress' => { '__deferred' => { 'uri' => 'Customers(id=16193)/PrimaryAddress' } },
          'MailAddress' => { '__deferred' => { 'uri' => 'Customers(id=16193)/MailAddress' } },
          'Employer' => { '__deferred' => { 'uri' => 'Customers(id=16193)/Employer' } },
          'References' => { '__deferred' => { 'uri' => 'Customers(id=16193)/References' } },
          'PaymentAccounts' => { '__deferred' => { 'uri' => 'Customers(id=16193)/PaymentAccounts' } },
          'Phones' => { '__deferred' => { 'uri' => 'Customers(id=16193)/Phones' } },
          'CustomFieldValues' => { '__deferred' => { 'uri' => 'Customers(id=16193)/CustomFieldValues' } },
          'Documents' => { '__deferred' => { 'uri' => 'Customers(id=16193)/Documents' } },
          'CreditScore' => { '__deferred' => { 'uri' => 'Customers(id=16193)/CreditScore' } },
          'Loans' => { '__deferred' => { 'uri' => 'Customers(id=16193)/Loans' } },
          'LineOfCredits' => { '__deferred' => { 'uri' => 'Customers(id=16193)/LineOfCredits' } },
          'SocialProfiles' => { '__deferred' => { 'uri' => 'Customers(id=16193)/SocialProfiles' } },
          'Notes' => { '__deferred' => { 'uri' => 'Customers(id=16193)/Notes' } },
          'id' => 16_193,
          'customId' => nil,
          'mcId' => 6_609_906,
          'customerType' => 'customer.type.individual',
          'status' => 'Active',
          'firstName' => 'ERICA',
          'lastName' => 'LAMBERT',
          'middleName' => nil,
          'birthDate' => '/Date(*********)/',
          'gender' => 'customer.gender.unknown',
          'generationCode' => 'customer.generationCode.none',
          'email' => '<EMAIL>',
          'ssn' => '*********',
          'driverLicense' => nil,
          'companyName' => nil,
          'contactName' => nil,
          'customerIdType' => 'customer.idType.ssn',
          'customerId' => nil,
          'creditLimit' => 0,
          'accessUserName' => '<EMAIL>',
          'active' => 1,
          'ofacMatch' => 0,
          'ofacTested' => 0,
          'saleTransferPii' => 1,
          'passwordChanged' => 0,
          'hasAvatar' => 0,
          'loanRole' => 'loan.customerRole.secondary',
          'created' => '/Date(**********)/',
          'lastUpdate' => '/Date(**********)/',
          'creditScoreId' => nil
        }
      ]
    end

    let(:address_and_phones_for_customers) do
      [
        { customer_id: 9507,
          customer_address: { '__metadata' => { 'uri' => 'http://loanpro.simnang.com/api/public/api/1/odata.svc/Address(id=30300)', 'type' => 'Entity.Address' },
                              'id' => 30_300,
                              'address1' => '409 GLENWOOD',
                              'address2' => nil,
                              'city' => 'Rapid City',
                              'state' => 'geo.state.UT',
                              'zipcode' => '94025',
                              'country' => 'company.country.usa',
                              'geoLat' => '37.458441018806',
                              'geoLon' => '-122.18514299122',
                              'created' => '/Date(1684908444)/',
                              'active' => 1,
                              'isVerified' => 0,
                              'isStandardized' => 0 },
          customer_phones: [{ '__metadata' => { 'uri' => 'http://loanpro.simnang.com/api/public/api/1/odata.svc/Phones(id=9309)', 'type' => 'Entity.Phone' },
                              'id' => 9309,
                              'entityId' => 9507,
                              'entityType' => 'Entity.Customer',
                              'phone' => '5555555555',
                              'type' => 'customer.phoneType.cell',
                              'isPrimary' => 1,
                              'isSecondary' => 0,
                              'sbtMktVerifyPIN' => nil,
                              'sbtActVerifyPIN' => nil,
                              'sbtMktVerifyPending' => 0,
                              'sbtActVerifyPending' => 0,
                              'sbtMktVerified' => 0,
                              'sbtActVerified' => 0,
                              'carrierName' => nil,
                              'carrierVerified' => 1,
                              'isLandLine' => 0,
                              'dndEnabled' => 0,
                              'active' => 1 }] },
        { customer_id: 16_193,
          customer_address: { '__metadata' => { 'uri' => 'http://loanpro.simnang.com/api/public/api/1/odata.svc/Address(id=61027)', 'type' => 'Entity.Address' },
                              'id' => 61_027,
                              'address1' => '409 GLENWOOD',
                              'address2' => nil,
                              'city' => 'Port Juanafurt',
                              'state' => 'geo.state.MA',
                              'zipcode' => '94025',
                              'country' => 'company.country.usa',
                              'geoLat' => '-1',
                              'geoLon' => '-1',
                              'created' => '/Date(**********)/',
                              'active' => 1,
                              'isVerified' => 0,
                              'isStandardized' => 0 },
          customer_phones: [{ '__metadata' => { 'uri' => 'http://loanpro.simnang.com/api/public/api/1/odata.svc/Phones(id=16029)', 'type' => 'Entity.Phone' },
                              'id' => 16_029,
                              'entityId' => 16_193,
                              'entityType' => 'Entity.Customer',
                              'phone' => '5555555555',
                              'type' => 'customer.phoneType.cell',
                              'isPrimary' => 1,
                              'isSecondary' => 0,
                              'sbtMktVerifyPIN' => nil,
                              'sbtActVerifyPIN' => nil,
                              'sbtMktVerifyPending' => 0,
                              'sbtActVerifyPending' => 0,
                              'sbtMktVerified' => 0,
                              'sbtActVerified' => 0,
                              'carrierName' => nil,
                              'carrierVerified' => 1,
                              'isLandLine' => 0,
                              'dndEnabled' => 0,
                              'active' => 1 }] }
      ]
    end

    it 'serializes the get loan customers payload' do
      payload = described_class.call(loan_customers, address_and_phones_for_customers)
      expect(payload).to eq(
        [{ 'id' => 9507,
           'firstName' => 'ERICA',
           'lastName' => 'LAMBERT',
           'email' => '<EMAIL>',
           'customerRole' => 'primary',
           'mail_address' =>
          { 'address1' => '409 GLENWOOD', 'city' => 'Rapid City', 'state' => 'geo.state.UT', 'zipcode' => '94025', 'country' => 'company.country.usa' },
           'phones' =>
          { 'phone' => '5555555555', 'id' => 9309, 'isPrimary' => 1, 'isSecondary' => 0 } },
         { 'id' => 16_193,
           'firstName' => 'ERICA',
           'lastName' => 'LAMBERT',
           'email' => '<EMAIL>',
           'customerRole' => 'secondary',
           'mail_address' =>
           { 'address1' => '409 GLENWOOD', 'city' => 'Port Juanafurt', 'state' => 'geo.state.MA', 'zipcode' => '94025', 'country' => 'company.country.usa' },
           'phones' =>
           { 'phone' => '5555555555', 'id' => 16_029, 'isPrimary' => 1, 'isSecondary' => 0 } }]
      )
    end

    it 'allows nil values in get loan customers payload' do
      loan_customers = [
        {
          '__metadata' => { 'uri' => 'http://loanpro.simnang.com/api/public/api/1/odata.svc/Customers(id=9507)', 'type' => 'Entity.Customer' },
          'id' => 9507,
          'firstName' => 'ERICA',
          'lastName' => 'LAMBERT',
          'email' => nil,
          'loanRole' => 'loan.customerRole.primary'
        }
      ]

      address_and_phones_for_customers = [
        { customer_id: 9507,
          customer_address: {
            'id' => 30_300,
            'address1' => nil,
            'address2' => nil,
            'city' => 'Rapid City',
            'state' => 'geo.state.UT',
            'zipcode' => '94025',
            'country' => 'company.country.usa'
          },
          customer_phones: [{
            'id' => 9309,
            'phone' => nil,
            'isPrimary' => 1,
            'isSecondary' => 0
          }] }
      ]

      payload = described_class.call(loan_customers, address_and_phones_for_customers)
      expect(payload).to eq(
        [{ 'id' => 9507,
           'firstName' => 'ERICA',
           'lastName' => 'LAMBERT',
           'email' => nil,
           'customerRole' => 'primary',
           'mail_address' =>
          { 'address1' => nil, 'city' => 'Rapid City', 'state' => 'geo.state.UT', 'zipcode' => '94025', 'country' => 'company.country.usa' },
           'phones' =>
          { 'phone' => nil, 'id' => 9309, 'isPrimary' => 1, 'isSecondary' => 0 } }]
      )
    end
  end
end
