# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanManagement::Serializers::LoanTransactionsSerializer do
  include NotifierHelper
  describe '.call' do
    let(:future_transaction_date) { (Date.today + 30).to_time.to_i }
    let(:past_date) { (Date.today - 30).to_time.to_i }
    let(:today) { Date.today.to_time.to_i }

    let(:past_transaction_with_zero_payment) do
      {
        'date' => "/Date(#{past_date})/",
        'id' => 2_631_974,
        'type' => 'forecastedPayment',
        'paymentAmount' => '0',
        'paymentInterest' => '0',
        'paymentPrincipal' => '0',
        'paymentDiscount' => '0',
        'paymentFees' => '0',
        'principalBalance' => '12000.00',
        'chargeAmount' => '0',
        'chargeInterest' => '0',
        'chargePrincipal' => '0',
        'chargeDiscount' => '0'
      }
    end

    let(:past_transaction) do
      {
        'date' => "/Date(#{past_date})/",
        'id' => 2_631_974,
        'type' => 'forecastedPayment',
        'paymentAmount' => '500.00',
        'paymentInterest' => '50.00',
        'paymentPrincipal' => '450.00',
        'paymentDiscount' => '5.00',
        'paymentFees' => '0',
        'principalBalance' => '12000.00',
        'chargeAmount' => '0',
        'chargeInterest' => '0',
        'chargePrincipal' => '0',
        'chargeDiscount' => '0'
      }
    end

    let(:today_transaction_with_zero_payment) do
      {
        'date' => "/Date(#{today})/",
        'id' => 2_631_975,
        'type' => 'scheduledPayment',
        'paymentAmount' => '0',
        'paymentInterest' => '0',
        'paymentPrincipal' => '0',
        'paymentDiscount' => '0',
        'paymentFees' => '0',
        'principalBalance' => '12000.00',
        'chargeAmount' => '500.00',
        'chargeInterest' => '50.00',
        'chargePrincipal' => '450.00',
        'chargeDiscount' => '5.00'
      }
    end

    let(:future_scheduled_transaction) do
      {
        'date' => "/Date(#{future_transaction_date})/",
        'id' => 2_631_976,
        'type' => 'scheduledPayment',
        'paymentAmount' => '0',
        'paymentInterest' => '0',
        'paymentPrincipal' => '0',
        'paymentDiscount' => '0',
        'paymentFees' => '0',
        'principalBalance' => '12000.00',
        'chargeAmount' => '500.00',
        'chargeInterest' => '50.00',
        'chargePrincipal' => '450.00',
        'chargeDiscount' => '5.00'
      }
    end

    let(:future_forecasted_transaction) do
      {
        'date' => "/Date(#{future_transaction_date})/",
        'id' => 2_631_977,
        'type' => 'forecastedPayment',
        'paymentAmount' => '500.00',
        'paymentInterest' => '50.00',
        'paymentPrincipal' => '450.00',
        'paymentDiscount' => '5.00',
        'paymentFees' => '0',
        'principalBalance' => '12000.00',
        'chargeAmount' => '0',
        'chargeInterest' => '0',
        'chargePrincipal' => '0',
        'chargeDiscount' => '0'
      }
    end

    context 'when the transaction contains a past transaction with a payment amount of zero' do
      let(:transactions) { [past_transaction_with_zero_payment] }

      it 'does not serialize the transaction' do
        response = described_class.call(transactions)
        expect(response).to eq({ 'transactions' => [] })
      end
    end

    context 'when the transaction contains a past transaction with a payment amount' do
      let(:transactions) { [past_transaction] }

      let(:expected_serialized_transactions) do
        [
          {
            'paymentAmount' => '500.00',
            'paymentDiscount' => '5.00',
            'paymentFees' => '0',
            'paymentInterest' => '50.00',
            'paymentPrincipal' => '450.00',
            'principalBalance' => '12000.00',
            'transactionDate' => LoanProHelpers.parse_date("/Date(#{past_date})/").iso8601,
            'transactionId' => 2_631_974
          }
        ]
      end

      it 'includes the past transaction after being serialized' do
        response = described_class.call(transactions)
        expect(response).to eq({ 'transactions' => expected_serialized_transactions })
      end
    end

    context 'when the transactions contains future transactions' do
      let(:transactions) { [future_scheduled_transaction, future_forecasted_transaction] }

      let(:expected_serialized_transactions) do
        [
          {
            'paymentAmount' => '500.00',
            'paymentInterest' => '50.00',
            'paymentPrincipal' => '450.00',
            'paymentDiscount' => '5.00',
            'paymentFees' => nil,
            'principalBalance' => '12000.00',
            'transactionId' => 2_631_976,
            'transactionDate' => LoanProHelpers.parse_date("/Date(#{future_transaction_date})/").iso8601
          }
        ]
      end

      it 'only includes the scheduled transaction and merges the charge amounts' do
        response = described_class.call(transactions)
        expect(response).to eq({ 'transactions' => expected_serialized_transactions })
      end
    end

    context 'when the transactions contains both past, present and future transactions' do
      let(:transactions) { [past_transaction, past_transaction_with_zero_payment, today_transaction_with_zero_payment, future_scheduled_transaction, future_forecasted_transaction] }

      let(:expected_serialized_transactions) do
        [
          {
            'paymentAmount' => '500.00',
            'paymentDiscount' => '5.00',
            'paymentFees' => '0',
            'paymentInterest' => '50.00',
            'paymentPrincipal' => '450.00',
            'principalBalance' => '12000.00',
            'transactionDate' => LoanProHelpers.parse_date("/Date(#{past_date})/").iso8601,
            'transactionId' => 2_631_974
          },
          {
            'paymentAmount' => '500.00',
            'paymentInterest' => '50.00',
            'paymentPrincipal' => '450.00',
            'paymentDiscount' => '5.00',
            'paymentFees' => nil,
            'principalBalance' => '12000.00',
            'transactionId' => 2_631_976,
            'transactionDate' => LoanProHelpers.parse_date("/Date(#{future_transaction_date})/").iso8601
          }
        ]
      end

      it 'includes the correct past and future transactions and excludes the current transaction with zero amount' do
        response = described_class.call(transactions)
        expect(response).to eq({ 'transactions' => expected_serialized_transactions })
      end
    end
  end
end
