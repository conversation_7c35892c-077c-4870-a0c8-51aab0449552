# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanManagement::Serializers::PaymentProfilesSerializer do
  it 'serializes payment profiles correctly' do
    payment_profiles_data = {
      'results' => [
        {
          '__metadata' => { 'uri' => 'http://example.com/api/1/odata.svc/PaymentAccounts(id=9463)', 'type' => 'Entity.PaymentAccount' },
          'CheckingAccount' => {
            '__metadata' => { 'uri' => 'http://example.com/api/1/odata.svc/CheckingAccounts(id=9483)', 'type' => 'Entity.CheckingAccount' },
            'id' => 9483,
            'accountNumber' => '**********',
            'routingNumber' => '*********',
            'bankName' => 'AXOS BANK',
            'cardHolderName' => 'Patricia Foshee'
          },
          'id' => 9463,
          'entityId' => 8110,
          'entityType' => 'Entity.Customer',
          'importId' => nil,
          'isPrimary' => 0,
          'isSecondary' => 1,
          'title' => 'CFT Account crb0151468606',
          'type' => 'paymentAccount.type.checking',
          'checkingAccountId' => 9483,
          'active' => 1,
          'visible' => 1,
          'created' => '/Date(**********)/',
          'verify' => 0
        }
      ]
    }

    expected_result = {
      'paymentProfiles' => [
        {
          'id' => 9463,
          'isPrimary' => 0,
          'isSecondary' => 1,
          'title' => 'CFT Account crb0151468606',
          'type' => 'paymentAccount.type.checking',
          'checkingAccountId' => 9483,
          'active' => 1,
          'visible' => 1,
          'bankName' => 'AXOS BANK',
          'accountNumber' => '7890',
          'routingNumber' => '*********'
        }
      ]
    }

    result = LoanManagement::Serializers::PaymentProfilesSerializer.call(payment_profiles_data)
    expect(result).to eq(expected_result)
  end
end
