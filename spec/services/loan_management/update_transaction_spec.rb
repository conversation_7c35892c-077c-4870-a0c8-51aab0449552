# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanManagement::UpdateTransaction do
  include ActiveJob::TestHelper
  include NotifierHelper

  let!(:loan) { create(:above_lending_loan, :loanpro) }
  let!(:payment) { create(:loan_autopay_entity, loan_entity: loan.loan_pro_loan_entity) }
  let!(:payment_entity) { create(:payment_entity, loan_autopay_entity: payment, reverse_date: nil) }
  let(:transaction_id) { payment.payment_info_entity&.transaction_id }

  let(:message) { Faker::Alphanumeric.alphanumeric }
  let(:status) { Faker::Alphanumeric.alphanumeric }
  let(:reason_code) { 'R01' }

  let(:loan_pro_attributes) do
    {
      transaction: {
        message: message,
        status: status,
        reason_code: reason_code
      }
    }
  end

  before do
    allow(ActiveSupport::Notifications).to receive(:instrument).and_call_original
  end

  describe '.call' do
    it 'Returns a true result without error' do
      expect(LoanProService::TransactionManagement::UpdateTransactionStatus)
        .to receive(:call).with(transaction_id: transaction_id, attributes: loan_pro_attributes).and_return(true)
      update_transaction = described_class.new(transaction_id:, message:, status:, reason_code:)
      expect(update_transaction.call).to eq(true)
      expect(update_transaction.error).to eq(nil)
    end

    context 'when any of the required parameters are blank' do
      it 'should return false' do
        expect(LoanProService::TransactionManagement::UpdateTransactionStatus).not_to receive(:call)
        expect(Rails.logger).to receive(:error).with(/UpdateTransaction payload was not valid/)
        expect(described_class.call(transaction_id: transaction_id, message: nil, status: nil, reason_code: nil)).to eq(false)
      end
    end

    context 'when the LP API raises an exception' do
      before do
        allow(LoanProService::TransactionManagement::UpdateTransactionStatus).to receive(:call).and_raise(LoanProService::Exceptions::LoanManagementSystemError, 'Boom!')
      end

      it 'should notify instrumentation and return false' do
        expect(described_class.call(transaction_id:, message:, status:, reason_code:)).to eq(false)

        expect_to_notify('update_transaction', success: false, fail_reason: /Boom!/)
      end
    end
  end
end
