# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanManagement::UploadFundingValidationDocument do
  let(:funding_validation) { build(:funding_validation, document_info: { funding_documents: [] }) }
  let(:funding) { build(:funding, funding_validation:) }
  let!(:loan) { create(:loan, funding:) }
  let(:dash_bucket) { Rails.configuration.aws_s3.dash_files_bucket }

  let(:unified_id) { loan.loan_number }
  let(:report_type) { 'giact_report' }
  let(:body) { '<some>xml</some>' }
  let(:document) do
    tempfile = Tempfile.new('giact_doc.xml').tap do |file|
      file.write(body)
      file.rewind
    end

    ActionDispatch::Http::UploadedFile.new(
      tempfile:,
      type: 'application/xml',
      filename: 'test1.xml'
    )
  end
  let(:mock_s3_client) { instance_double(Aws::S3::Client, put_object: nil) }

  let(:subject) { described_class.call(unified_id:, report_type:, document:) }
  before do
    allow(Aws::S3::Client).to receive(:new).and_return(mock_s3_client)
  end

  it 'raises an error if loan does not have a FundingValidation record' do
    loan.funding_validation.destroy

    expect { described_class.call(unified_id:, report_type:, document:) }.to raise_error(described_class::MissingFundingDataError, /No Funding Validation record/i)
  end

  it 'raises an error for invalid report types' do
    report_type = 'another_report'

    expect { described_class.call(unified_id:, report_type:, document:) }.to raise_error(described_class::InvalidReportType, /Incorrect Report Type/i)
  end

  it 'raises an error for a GIACT_REPORT already uploaded' do
    loan.funding_validation.document_info.funding_documents << FundingValidation::FundingDocument.new(type: 'GIACT_REPORT')
    loan.funding_validation.save

    expect { described_class.call(unified_id:, report_type:, document:) }.to raise_error(described_class::ReportTypeUniqueConstraintError, 'Report Type (GIACT_REPORT) is already uploaded')
  end

  it 'persists the report in S3' do
    subject

    expected_body = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n#{body}"

    expect(mock_s3_client).to have_received(:put_object).once do |bucket:, key:, body:|
      expect(bucket).to eq(dash_bucket)
      expect(key).to match(%r{test/origination_reports/#{loan.id}/[A-Z_]+-[0-9]+.xml}i)
      expect(body).to eq(expected_body)
    end
  end

  %w[decision_engine_input decision_engine_output giact_report hard_pull_credit_report informative_credit_report socure_report].each do |report_type|
    context "when report type is #{report_type}" do
      let(:report_type) { report_type }

      it 'stores the funding document' do
        subject

        report = funding_validation.reload.document_info.funding_documents.first

        expect(report.id).to be_present
        expect(report.created_at).to be_within(1.second).of(Time.now)
        expect(report.file_size_bytes).to eq(55)
        expect(report.mime_type).to eq('application/xml')
        expect(report.name).to eq("Origination Report - #{report_type.upcase}.xml")
        expect(report.storage_bucket).to eq(dash_bucket)
        expect(report.storage_key).to match(%r{test/origination_reports/#{loan.id}/[A-Z_]+-[0-9]+.xml}i)
      end
    end
  end
end
