# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanProService::ConfigHelper do
  describe '.configuration' do
    it 'creates a new instance of Configuration' do
      expect(LoanProService::Configuration).to receive(:new)
      described_class.reset
      described_class.configuration
    end
  end

  describe '.reset' do
    it 'creates and assigns a new instance of Configuration' do
      configuration = described_class.configuration
      expect(described_class.reset).not_to eq configuration
    end
  end

  describe '.configure' do
    let(:configuration_params) do
      { authorization: Faker::Alphanumeric.alphanumeric(number: 256),
        base_url: Faker::Internet.url,
        secret: Faker::Alphanumeric.alphanumeric(number: 64),
        timeout: Faker::Number.number(digits: 2) }
    end

    it 'creates a new instance of Configuration from block' do
      described_class.configure do |config|
        config.authorization = configuration_params[:authorization]
        config.base_url = configuration_params[:base_url]
        config.secret = configuration_params[:secret]
        config.timeout = configuration_params[:timeout]
      end

      expect(LoanProService::ConfigHelper.configuration).not_to be_nil
    end
  end
end
