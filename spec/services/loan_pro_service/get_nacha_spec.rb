# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanProService::GetNacha do
  let(:file_name) { "#{Faker::Superhero.power}.txt" }
  let(:file_content) { Faker::String.random }
  let(:input_stream) { StringIO.new(file_content) }
  let(:post_response) { { result: { hits: { hits: [{ _source: { uuid: Faker::Internet.uuid } }] } } } }
  let(:start_date) { Faker::Date.backward(days: 10) }
  let(:stop_date) { Faker::Date.backward(days: 5) }
  let(:subject) { described_class.call(start_date, stop_date) }
  let(:zip_entries) { double('zip_entries') }
  let(:zip_files) { double('zip_files') }

  before do
    allow(LoanProService::PostRequest).to receive(:call).and_return(post_response)
    allow(LoanProService::GetRequest).to receive(:call).and_return('')
    allow(Zip::File).to receive(:open_buffer).and_yield(zip_files)
    allow(zip_files).to receive(:entries).and_return(zip_entries)
    allow(zip_entries).to receive(:map).and_return([file_name])
    allow(zip_files).to receive(:find_entry).with(file_name).and_return(file_content)
    allow(zip_files).to receive(:get_input_stream).with(file_content).and_return(input_stream)
  end

  it 'calls process' do
    expect(LoanProService::NachaProcessor).to receive(:process)
    subject
  end
end
