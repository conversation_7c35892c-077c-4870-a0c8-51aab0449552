# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanProService::GetRequest do
  let(:subject) { described_class.call(url) }
  let(:url) { Faker::Internet.url }

  context 'when called' do
    let(:conn) { double('conn') }
    let(:response) { double('response') }
    before do
      allow_any_instance_of(described_class).to receive(:conn).and_return(conn)
      allow(conn).to receive(:get).and_return(response)
      allow(response).to receive(:body)
    end

    it 'uses the gems configuration' do
      expect(described_class.new(url).config).to eq(LoanProService::ConfigHelper.configuration)
    end

    it 'makes an http call' do
      expect(conn).to receive(:get)
      subject
    end
  end

  context 'when configuring' do
    let(:config) { double('config') }
    before do
      allow_any_instance_of(described_class).to receive(:config).and_return(config)
    end

    it 'loads the config values' do
      expect(config).to receive(:authorization)
      expect(config).to receive(:secret)
      described_class.new(url).conn
    end
  end
end
