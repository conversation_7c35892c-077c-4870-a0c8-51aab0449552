# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanProService::LoanManagementSystem::AmortizationSchedule do
  let(:loan_id) { '7849' }
  let(:headers) { { content_type: 'application/json' } }
  let(:base_url) { LoanProService::ConfigHelper.configuration.loan_management_system_url }
  let(:transactions_url) { "#{base_url}odata.svc/Loans(#{loan_id})/Transactions?nopaging=true" }

  subject { described_class.call(loan_id:) }

  describe '.call', vcr: { cassette_name: 'LoanProService_LoanManagementSystem_AmortizationSchedule/_call/api_call' } do
    it 'should not produce an error' do
      expect { subject }.to_not raise_error
    end

    it 'should return 215 transactions' do
      expect(subject.size).to eq(215)
    end
  end

  context 'errors' do
    describe '409 Loan Not Found' do
      let(:loan_id) { '7849' }
      let(:message) { "Resource of type 'Loan' with id was not found." }
      let(:body) { { error: { message:, type: 'Exception', code: 0 } } }

      before { stub_request(:get, transactions_url).to_return(status: 409, headers:, body: body.to_json) }

      it 'should extract error message and raise LoanManagementSystemError' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, message)
      end
    end

    describe '4xx client error' do
      before { stub_request(:get, transactions_url).to_return(headers:, status: 403) }

      it 'should raise LoanManagementSystemError with status' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, /status 403/)
      end
    end
  end
end
