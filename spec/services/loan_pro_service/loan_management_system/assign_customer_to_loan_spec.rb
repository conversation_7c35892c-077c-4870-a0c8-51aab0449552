# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanProService::LoanManagementSystem::AssignCustomerToLoan do
  let(:loan_attributes) do
    {
      id: Faker::Number.number(digits: 5),
      Customers: {
        results: [
          {
            __id: Faker::Number.number(digits: 5),
            __setLoanRole: 'loan.customerRole.primary'
          }
        ]
      },
      __update: true,
      __id: Faker::Number.number(digits: 5)
    }
  end

  let(:base_url) { LoanProService::ConfigHelper.configuration.loan_management_system_url }
  let(:loan_id) { Faker::Number.number(digits: 5) }
  let(:loan_base_data) { { 'id' => loan_id } }

  describe '.call' do
    context 'with valid loan attributes' do
      subject { described_class.call(loan_id:, attributes: loan_attributes) }

      it 'makes an API call to the correct endpoint' do
        request = stub_request(:put, "#{base_url}odata.svc/Loans(#{loan_id})")
                  .to_return(headers: { content_type: 'application/json' }, body: { d: loan_base_data }.to_json)

        expect { subject }.to_not raise_error
        expect(request).to have_been_requested
        expect(subject['id']).to eq(loan_id)
      end

      it 'includes the specified attributes in the request payload' do
        request = stub_request(:put, "#{base_url}odata.svc/Loans(#{loan_id})").with do |req|
          request_body = JSON.parse(req.body, symbolize_names: true)

          expect(request_body).to have_key(:id)
          expect(request_body).to have_key(:Customers)
          expect(request_body[:Customers]).to have_key(:results)
          expect(request_body[:Customers][:results].first).to have_key(:__id)
          expect(request_body[:Customers][:results].first).to have_key(:__setLoanRole)
          expect(request_body).to have_key(:__update)
          expect(request_body).to have_key(:__id)
        end.to_return(headers: { content_type: 'application/json' }, body: { d: loan_base_data }.to_json)

        expect { subject }.to_not raise_error
        expect(request).to have_been_requested
      end
    end

    context 'when error->message->value is present in API response and status is 200' do
      let(:error) { "Resource not found for the segment 'Loans'" }

      before do
        stub_request(:put, "#{base_url}odata.svc/Loans(#{loan_id})")
          .to_return(headers: { content_type: 'application/json' }, body: {
            error: { message: { lang: 'en-US', value: error } }
          }.to_json)
      end

      subject { described_class.call(loan_id:, attributes: loan_attributes) }

      # because Faraday does not raise error on 200 status,
      # - this tests raising of error when 'error' is found in respose
      # - also tests correct error message extraction
      it 'raises a LoanManagementSystemError' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, error)
      end
    end

    context 'when unexpectedly formatted error is present in API response and status is 200' do
      before do
        stub_request(:put, "#{base_url}odata.svc/Loans(#{loan_id})")
          .to_return(headers: { content_type: 'application/json' }, body: {
            error: { test: 'unexpected error' }
          }.to_json)
      end

      subject { described_class.call(loan_id:, attributes: loan_attributes) }

      # because Faraday does not raise error on 200 status,
      # - this tests raising of error when 'error' is found in respose
      # - also tests correct error message extraction
      it 'raises a LoanManagementSystemError' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, /unexpected error/i)
      end
    end

    context 'when status is 4xx (ClientError) but no response body is present' do
      before do
        stub_request(:put, "#{base_url}odata.svc/Loans(#{loan_id})")
          .to_return(headers: { content_type: 'application/json' }, status: 403)
      end

      subject { described_class.call(loan_id:, attributes: loan_attributes) }

      it 'raises a LoanManagementSystemError with status' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, /status 403/)
      end
    end
  end
end
