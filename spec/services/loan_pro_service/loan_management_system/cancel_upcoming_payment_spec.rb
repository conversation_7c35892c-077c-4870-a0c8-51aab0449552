# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanProService::LoanManagementSystem::CancelUpcomingPayment do
  let(:loan_id) { Faker::Number.number(digits: 4) }
  let(:loan_autopay_id) { Faker::Number.number(digits: 4) }
  let(:base_url) { LoanProService::ConfigHelper.configuration.loan_management_system_url }
  let(:loan_base_data) do
    {
      'id' => loan_id,
      'Autopays' => { '__deferred' => { 'uri' => "Loans(#{loan_id})/Autopays" } }
    }
  end
  let(:request_body) do
    {
      "Autopays": {
        "results": [
          {
            "id": loan_autopay_id,
            "status": 'autopay.status.cancelled',
            "__update": true
          }
        ]
      }
    }
  end

  context 'autopay successfully cancelled' do
    describe '.call' do
      subject { described_class.call(loan_id:, loan_autopay_id:) }

      before do
        stub_request(:put, "#{base_url}odata.svc/Loans(#{loan_id})")
          .with(body: request_body).to_return(headers: { content_type: 'application/json' }, body: { d: loan_base_data }.to_json)
      end

      it 'should PUT correct API endpoint with portfolio & subportfolio ID' do
        expect { subject }.to_not raise_error
        expect(subject).to eq(loan_base_data)
      end
    end
  end

  context 'errors' do
    describe '409 Autopay Not Found' do
      # 409 is returned when the autopay entity is not found(with correct payload format)
      let(:error) { 'Invalid or missing primary payment method' }
      let(:loan_id) { '7849' }
      let(:loan_autopay_id) { 28_230 }

      before do
        stub_request(:put, "#{base_url}odata.svc/Loans(#{loan_id})")
          .with(body: request_body).to_return(status: 409, headers: { content_type: 'application/json' }, body: {
            "error": {
              "message": 'Invalid or missing primary payment method',
              "type": 'EntityException',
              "code": 0
            }
          }.to_json)
      end

      subject { described_class.call(loan_id:, loan_autopay_id:) }

      it 'should extract error message and raise LoanManagementSystemError' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, error)
      end
    end

    describe '4xx client error' do
      before do
        stub_request(:put, "#{base_url}odata.svc/Loans(#{loan_id})")
          .to_return(headers: { content_type: 'application/json' }, status: 403)
      end
      subject { described_class.call(loan_id:, loan_autopay_id:) }

      it 'should raise LoanManagementSystemError with status' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, /status 403/)
      end
    end
  end
end
