# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanProService::LoanManagementSystem::CheckLoanInvestorPortfolio do
  let(:loan_id) { Faker::Number.number(digits: 4) }
  let(:base_url) { LoanProService::ConfigHelper.configuration.loan_management_system_url }
  let(:investor_portfolio_id) { LoanProService::ConfigHelper.configuration.investor_portfolio_id }
  let(:loan_base_data) do
    {
      'id' => loan_id
    }
  end

  let(:portfolios_data) do
    [{
      'id' => investor_portfolio_id,
      'title' => 'Investor',
      'active' => 1
    }]
  end

  describe '.call' do
    subject { described_class.call(loan_id) }

    context 'when investor portfolio is not present on a loan' do
      it 'should expand loan portfolio and not raise error' do
        expect(LoanProService::LoanManagementSystem::GetLoan).to receive(:call).with(loan_id, expand: 'Portfolios&nopaging').and_return(loan_base_data.merge('Portfolios' => []))
        expect { subject }.to_not raise_error
      end
    end

    context 'when investor portfolio is already present on a loan' do
      it 'should expand loan portfolio and raise error' do
        expect(LoanProService::LoanManagementSystem::GetLoan).to receive(:call)
          .with(loan_id, expand: 'Portfolios&nopaging').and_return(loan_base_data.merge('Portfolios' => portfolios_data))
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, /portfolio already exists/)
      end
    end
  end
end
