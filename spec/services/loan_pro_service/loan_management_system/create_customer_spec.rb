# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanProService::LoanManagementSystem::CreateCustomer do
  let(:customer_attributes) do
    {
      birthDate: Faker::Date.birthday(min_age: 18, max_age: 65),
      firstName: Faker::Name.first_name,
      lastName: Faker::Name.last_name,
      ssn: Faker::IdNumber.valid,
      email: Faker::Internet.email,
      Phones: {
        results: [
          {
            phone: Faker::PhoneNumber.cell_phone
          }
        ]
      },
      PrimaryAddress: {
        address1: Faker::Address.street_address,
        zipcode: Faker::Address.zip,
        city: Faker::Address.city,
        state: "geo.state.#{Faker::Address.state_abbr.upcase}"
      },
      MailAddress: {
        address1: Faker::Address.street_address,
        zipcode: Faker::Address.zip,
        city: Faker::Address.city,
        state: Faker::Address.state_abbr.upcase
      },
      PaymentAccounts: {
        results: [
          {
            CheckingAccount: {
              accountType: 'bankacct.type.checking',
              token: Faker::Alphanumeric.alphanumeric(number: 10)
            },
            title: "Personal Account #{Faker::Alphanumeric.alphanumeric(number: 10)}"
          }
        ]
      }
    }
  end

  let(:base_url) { LoanProService::ConfigHelper.configuration.loan_management_system_url }
  let(:customer_id) { Faker::Number.number(digits: 5) }
  let(:customer_base_data) { { 'id' => customer_id } }

  describe '.call' do
    context 'with valid customer attributes' do
      subject { described_class.call(attributes: customer_attributes) }

      it 'makes an API call to the correct endpoint' do
        request = stub_request(:post, "#{base_url}odata.svc/Customers")
                  .to_return(headers: { content_type: 'application/json' }, body: { d: customer_base_data }.to_json)

        expect { subject }.to_not raise_error
        expect(request).to have_been_requested
        expect(subject['id']).to eq(customer_id)
      end

      it 'includes both the specified and default attributes in the request payload' do
        request = stub_request(:post, "#{base_url}odata.svc/Customers").with do |req|
          request_body = JSON.parse(req.body, symbolize_names: true)

          expect(request_body).to have_key(:status)
          expect(request_body).to have_key(:firstName)
          expect(request_body[:PrimaryAddress]).to have_key(:country)
          expect(request_body[:PrimaryAddress]).to have_key(:address1)
          expect(request_body[:MailAddress]).to have_key(:country)
          expect(request_body[:MailAddress]).to have_key(:address1)
        end.to_return(headers: { content_type: 'application/json' }, body: { d: customer_base_data }.to_json)

        expect { subject }.to_not raise_error
        expect(request).to have_been_requested
      end

      it 'permits the default customer attributes in the requset payload to be overriden when specified directly' do
        birth_date = '01-01-2000'
        customer_attributes[:birthDate] = birth_date

        status = 'Inactive'
        customer_attributes[:status] = status

        verify = false
        customer_attributes[:PrimaryAddress][:verify] = verify

        request = stub_request(:post, "#{base_url}odata.svc/Customers").with do |req|
          request_body = JSON.parse(req.body, symbolize_names: true)
          expect(request_body[:birthDate]).to eq(birth_date)
          expect(request_body[:status]).to eq(status)
          expect(request_body[:PrimaryAddress][:verify]).to eq(verify)
        end.to_return(headers: { content_type: 'application/json' }, body: { d: customer_base_data }.to_json)

        expect { subject }.to_not raise_error
        expect(request).to have_been_requested
      end
    end

    context 'when error->message->value is present in API response and status is 200' do
      let(:error) { "Resource not found for the segment 'Customers'" }

      before do
        stub_request(:post, "#{base_url}odata.svc/Customers")
          .to_return(headers: { content_type: 'application/json' }, body: {
            error: { message: { lang: 'en-US', value: error } }
          }.to_json)
      end

      subject { described_class.call(attributes: customer_attributes) }

      # because Faraday does not raise error on 200 status,
      # - this tests raising of error when 'error' is found in respose
      # - also tests correct error message extraction
      it 'raises a LoanManagementSystemError' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, error)
      end
    end

    context 'when unexpectedly formatted error is present in API response and status is 200' do
      before do
        stub_request(:post, "#{base_url}odata.svc/Customers")
          .to_return(headers: { content_type: 'application/json' }, body: {
            error: { test: 'unexpected error' }
          }.to_json)
      end

      subject { described_class.call(attributes: customer_attributes) }

      # because Faraday does not raise error on 200 status,
      # - this tests raising of error when 'error' is found in respose
      # - also tests correct error message extraction
      it 'raises a LoanManagementSystemError' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, /unexpected error/i)
      end
    end

    context 'when status is 4xx (ClientError) but no response body is present' do
      before do
        stub_request(:post, "#{base_url}odata.svc/Customers")
          .to_return(headers: { content_type: 'application/json' }, status: 403)
      end

      subject { described_class.call(attributes: customer_attributes) }

      it 'raises a LoanManagementSystemError with status' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, /status 403/)
      end
    end
  end
end
