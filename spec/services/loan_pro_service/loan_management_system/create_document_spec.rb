# frozen_string_literal: true

require 'rails_helper'
RSpec.describe LoanProService::LoanManagementSystem::CreateDocument do
  let(:base_url) { LoanProService::ConfigHelper.configuration.loan_management_system_url }
  let(:customer_id) { Faker::Number.number(digits: 5) }
  let(:section_id) { 2 }
  let(:file_name) { 'test_file.jpg' }
  let(:custom_file_name) { 'test_file_name.jpg' }
  let(:request_body) { { sectionId: section_id, fileName: file_name, customFileName: custom_file_name } }
  let(:response_body) do
    { 'id' => 1, 'fileName' => file_name, 'uploadUrl' => 'https://example.com/upload',
      'customFileName' => custom_file_name, 'mime' => 'image/jpeg' }
  end

  describe '.call' do
    context 'with valid customer id' do
      subject { described_class.call(customer_id:, section_id:, file_name:, custom_file_name:) }

      it 'makes an API call to the correct endpoint', vcr: false do
        request = stub_request(:post, "#{base_url}Customers(#{customer_id})/document/upload")
                  .with(body: request_body)
                  .to_return(headers: { content_type: 'application/json' }, body: { d: response_body }.to_json)

        expect { subject }.to_not raise_error
        expect(request).to have_been_requested
        expect(subject['fileName']).to eq(file_name)
      end
    end

    context 'with invalid customer id' do
      subject { described_class.call(customer_id: 'invalid_customer_id', section_id:, file_name:, custom_file_name:) }
      it 'raises a LoanManagementSystemError with the correct error message' do
        error_message = "Resource not found for the segment 'Customers'"
        stub_request(:post, "#{base_url}Customers(invalid_customer_id)/document/upload")
          .to_return(
            status: 200,
            headers: { content_type: 'application/json' },
            body: {
              error: {
                message: {
                  lang: 'en-US',
                  value: error_message
                }
              }
            }.to_json
          )

        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, error_message)
      end
    end
  end
end
