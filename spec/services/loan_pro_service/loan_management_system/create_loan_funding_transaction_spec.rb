# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanProService::LoanManagementSystem::CreateLoanFundingTransaction do
  let(:attributes) do
    {
      loanId: 'loanpro_loan_id',
      customerId: 'loanpro_customer_id',
      sourceCompanyId: 'source_company_id',
      merchantProcessorGroupId: 'merchant_processor_group_id',
      date: 'contract_date',
      amount: 'amount',
      categoryId: 'category_id',
      merchantTxProcessorId: '',
      method: 'loan.funding.method.deposit',
      authorizationType: 'loan.funding.auth.ppd',
      paymentAccountId: 'payment_account_id'
    }
  end

  let(:base_url) { LoanProService::ConfigHelper.configuration.loan_management_system_url }
  let(:funding_transaction_id) { Faker::Number.number(digits: 5) }
  let(:funding_transaction_data) { { 'id' => funding_transaction_id } }

  describe '.call' do
    subject { described_class.call(attributes:) }

    context 'with valid loan funding attributes' do
      it 'makes an API call to the correct endpoint' do
        request = stub_request(:post, "#{base_url}odata.svc/LoanFundingTransactions")
                  .with(body: attributes)
                  .to_return(headers: { content_type: 'application/json' }, body: { d: funding_transaction_data }.to_json)

        expect { subject }.to_not raise_error
        expect(request).to have_been_requested
        expect(subject['id']).to eq(funding_transaction_id)
      end
    end

    context 'when loanpro returns an error' do
      let(:error_body) do
        {
          error: {
            message: 'Loan Funding was rejected with message: Unknown error',
            type: 'PostResourceActionException',
            code: 500
          }
        }
      end

      it 'raises a LoanManagementSystemError with the error message' do
        request = stub_request(:post, "#{base_url}odata.svc/LoanFundingTransactions")
                  .with(body: attributes)
                  .to_return(headers: { content_type: 'application/json' }, body: error_body.to_json, status: 409)

        expect do
          subject
        end.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, 'Loan Funding was rejected with message: Unknown error')
        expect(request).to have_been_requested
      end
    end
  end
end
