# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanProService::LoanManagementSystem::CreateLoan do
  let(:loan_attributes) do
    {
      displayId: Faker::Number.leading_zero_number(digits: 8),
      'LoanSetup' => {
        loanAmount: Faker::Number.between(from: 1000, to: 50_000),
        loanRate: Faker::Number.between(from: 5.0, to: 25.0),
        underwriting: Faker::Number.between(from: 100, to: 2500),
        loanTerm: Faker::Number.between(from: 36, to: 72),
        contractDate: DateTime.now.to_date.iso8601,
        firstPaymentDate: (DateTime.now.to_date + 30).iso8601
      }
    }
  end
  let(:base_url) { LoanProService::ConfigHelper.configuration.loan_management_system_url }
  let(:loan_id) { Faker::Number.number(digits: 5) }
  let(:loan_base_data) { { 'id' => loan_id } }

  describe '.call' do
    context 'with valid loan attributes' do
      subject { described_class.call(attributes: loan_attributes) }

      it 'makes an API call to the correct endpoint' do
        request = stub_request(:post, "#{base_url}odata.svc/Loans")
                  .to_return(headers: { content_type: 'application/json' }, body: { d: loan_base_data }.to_json)

        expect { subject }.to_not raise_error
        expect(request).to have_been_requested
        expect(subject['id']).to eq(loan_id)
      end

      it 'includes both the specified and default attributes in the request payload' do
        request = stub_request(:post, "#{base_url}odata.svc/Loans").with do |req|
          request_body = JSON.parse(req.body, symbolize_names: true)
          expect(request_body).to eq(
            {
              displayId: loan_attributes[:displayId],
              loanIdOption: 'loan.idOption.custom',
              LoanSetup: {
                active: 0,
                calcType: 'loan.calcType.simpleInterest',
                contractDate: loan_attributes['LoanSetup'][:contractDate],
                discount: 0,
                discountCalc: 'loan.discountCalc.straightLine',
                discountSplit: 1,
                firstPaymentDate: loan_attributes['LoanSetup'][:firstPaymentDate],
                loanAmount: loan_attributes['LoanSetup'][:loanAmount],
                loanClass: 'loan.class.consumer',
                loanRate: loan_attributes['LoanSetup'][:loanRate],
                loanRateType: 'loan.rateType.annually',
                loanTerm: loan_attributes['LoanSetup'][:loanTerm],
                loanType: 'loan.type.installment',
                paymentFrequency: 'loan.frequency.monthly',
                underwriting: loan_attributes['LoanSetup'][:underwriting]
              }
            }
          )
        end.to_return(headers: { content_type: 'application/json' }, body: { d: loan_base_data }.to_json)

        expect { subject }.to_not raise_error
        expect(request).to have_been_requested
      end

      it 'permits the default loan and loan setup attributes in the requset payload to be overriden when specified directly' do
        display_id = '123456789'
        loan_attributes[:displayId] = display_id

        active = 1
        loan_attributes['LoanSetup'][:active] = active

        payment_frequency = 'loan.frequency.biweekly'
        loan_attributes['LoanSetup'][:paymentFrequency] = payment_frequency

        request = stub_request(:post, "#{base_url}odata.svc/Loans").with do |req|
          request_body = JSON.parse(req.body, symbolize_names: true)
          expect(request_body[:displayId]).to eq(display_id)
          expect(request_body[:LoanSetup][:active]).to eq(active)
          expect(request_body[:LoanSetup][:paymentFrequency]).to eq(payment_frequency)
        end.to_return(headers: { content_type: 'application/json' }, body: { d: loan_base_data }.to_json)

        expect { subject }.to_not raise_error
        expect(request).to have_been_requested
      end
    end

    context 'when error->message->value is present in API response and status is 200' do
      let(:error) { "Resource not found for the segment 'Loans'" }

      before do
        stub_request(:post, "#{base_url}odata.svc/Loans")
          .to_return(headers: { content_type: 'application/json' }, body: {
            error: { message: { lang: 'en-US', value: error } }
          }.to_json)
      end

      subject { described_class.call(attributes: loan_attributes) }

      # because Faraday does not raise error on 200 status,
      # - this tests raising of error when 'error' is found in respose
      # - also tests correct error message extraction
      it 'raises a LoanManagementSystemError' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, error)
      end
    end

    context 'when unexpectedly formatted error is present in API response and status is 200' do
      before do
        stub_request(:post, "#{base_url}odata.svc/Loans")
          .to_return(headers: { content_type: 'application/json' }, body: {
            error: { test: 'unexpected error' }
          }.to_json)
      end

      subject { described_class.call(attributes: loan_attributes) }

      # because Faraday does not raise error on 200 status,
      # - this tests raising of error when 'error' is found in respose
      # - also tests correct error message extraction
      it 'raises a LoanManagementSystemError' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, /unexpected error/i)
      end
    end

    context 'when status is 4xx (ClientError) but no response body is present' do
      before do
        stub_request(:post, "#{base_url}odata.svc/Loans")
          .to_return(headers: { content_type: 'application/json' }, status: 403)
      end

      subject { described_class.call(attributes: loan_attributes) }

      it 'raises a LoanManagementSystemError with status' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, /status 403/)
      end
    end
  end
end
