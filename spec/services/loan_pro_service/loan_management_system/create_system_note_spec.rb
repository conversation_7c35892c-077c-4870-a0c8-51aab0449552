# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanProService::LoanManagementSystem::CreateSystemNote do
  let(:base_url) { LoanProService::ConfigHelper.configuration.loan_management_system_url }
  let(:loan_id) { Faker::Number.number(digits: 5) }
  let(:note_subject) { 'Automated Customer Contact' }
  let(:body) { 'An email was sent for the "payment past due" webhook event' }
  let(:expected_request_body) do
    {
      Notes: {
        results: [{
          body:,
          categoryId: Rails.application.config_for(:loan_pro_service)[:email_note_category],
          subject: note_subject,
          type: 'STANDARD'
        }]
      }
    }
  end

  describe '.call' do
    shared_examples 'a successful system note creation request' do
      it 'makes an API call to the correct endpoint' do
        request = stub_request(:put, "#{base_url}odata.svc/Loans(#{loan_id})")
                  .to_return(headers: { content_type: 'application/json' })

        expect { subject }.to_not raise_error
        expect(request).to have_been_requested
      end

      it 'includes both the specified and default attributes in the request payload' do
        request = stub_request(:put, "#{base_url}odata.svc/Loans(#{loan_id})").with do |req|
          request_body = JSON.parse(req.body, symbolize_names: true)
          expect(request_body).to eq(expected_request_body)
        end.to_return(headers: { content_type: 'application/json' })

        expect { subject }.to_not raise_error
        expect(request).to have_been_requested
      end
    end

    context 'with valid loan attributes' do
      subject { described_class.call(loan_id:, subject: note_subject, body:) }

      it_behaves_like 'a successful system note creation request'
    end

    context 'with an sms contact type' do
      let(:expected_request_body) do
        {
          Notes: {
            results: [{
              body:,
              categoryId: Rails.application.config_for(:loan_pro_service)[:sms_note_category],
              subject: note_subject,
              type: 'STANDARD'
            }]
          }
        }
      end

      subject { described_class.call(loan_id:, subject: note_subject, body:, contact_type: 'sms') }

      it_behaves_like 'a successful system note creation request'
    end

    context 'with an unknown contact type' do
      let(:expected_request_body) do
        {
          Notes: {
            results: [{
              body:,
              categoryId: LoanProService::LoanManagementSystem::CreateSystemNote::GENERIC_CONTACT_CATEGORY_ID,
              subject: note_subject,
              type: 'STANDARD'
            }]
          }
        }
      end

      subject { described_class.call(loan_id:, subject: note_subject, body:, contact_type: 'call') }

      it_behaves_like 'a successful system note creation request'
    end

    context 'when error->message->value is present in API response and status is 200' do
      let(:error) { "Resource not found for the segment 'Loans'" }

      before do
        stub_request(:put, "#{base_url}odata.svc/Loans(#{loan_id})")
          .to_return(headers: { content_type: 'application/json' }, body: {
            error: { message: { lang: 'en-US', value: error } }
          }.to_json)
      end

      subject { described_class.call(loan_id:, subject: note_subject, body:) }

      # because Faraday does not raise error on 200 status,
      # - this tests raising of error when 'error' is found in respose
      # - also tests correct error message extraction
      it 'raises a LoanManagementSystemError' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, error)
      end
    end

    context 'when unexpectedly formatted error is present in API response and status is 200' do
      before do
        stub_request(:put, "#{base_url}odata.svc/Loans(#{loan_id})")
          .to_return(headers: { content_type: 'application/json' }, body: {
            error: { test: 'unexpected error' }
          }.to_json)
      end

      subject { described_class.call(loan_id:, subject: note_subject, body:) }

      # because Faraday does not raise error on 200 status,
      # - this tests raising of error when 'error' is found in response
      # - also tests correct error message extraction
      it 'raises a LoanManagementSystemError' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, /unexpected error/i)
      end
    end

    context 'when status is 4xx (ClientError) but no response body is present' do
      before do
        stub_request(:put, "#{base_url}odata.svc/Loans(#{loan_id})")
          .to_return(headers: { content_type: 'application/json' }, status: 403)
      end

      subject { described_class.call(loan_id:, subject: note_subject, body:) }

      it 'raises a LoanManagementSystemError with status' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, /status 403/)
      end
    end
  end
end
