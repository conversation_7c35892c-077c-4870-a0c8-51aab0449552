# frozen_string_literal: true

require 'rails_helper'
RSpec.describe LoanProService::LoanManagementSystem::DueDateChange do
  let(:base_url) { LoanProService::ConfigHelper.configuration.loan_management_system_url }
  let(:loan_id) { Faker::Number.number(digits: 5) }
  let(:new_date) { Faker::Date.forward(days: 30) }
  let(:changed_date) { Faker::Date.backward(days: 30) }

  describe '.call' do
    context 'with valid loan id and dates' do
      subject { described_class.call(loan_id:, new_date:, changed_date:) }

      it 'makes an API call to the correct endpoint', vcr: false do
        request = stub_request(:post, "#{base_url}odata.svc/Loans(#{loan_id})")
                  .with(body: { 'DueDateChanges' => { 'results' => [{ 'newDate' => new_date, 'changedDate' => changed_date }] } }.to_json)
                  .to_return(headers: { content_type: 'application/json' }, body: { d: { 'id' => loan_id } }.to_json)

        expect { subject }.to_not raise_error
        expect(request).to have_been_requested
        expect(subject['id']).to eq(loan_id)
      end
    end

    context 'with invalid loan id' do
      subject { described_class.call(loan_id: 'invalid_loan_id', new_date:, changed_date:) }
      it 'raises a LoanManagementSystemError with the correct error message' do
        error_message = "Resource not found for the segment 'Loans'"
        stub_request(:post, "#{base_url}odata.svc/Loans(invalid_loan_id)")
          .to_return(
            status: 200,
            headers: { content_type: 'application/json' },
            body: { error: { message: { lang: 'en-US', value: error_message } } }.to_json
          )

        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, error_message)
      end
    end
  end
end
