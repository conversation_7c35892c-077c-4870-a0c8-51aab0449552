# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanProService::LoanManagementSystem::EditAutopay do
  let(:payment_attributes) do
    {
      applyDate: DateTime.now.to_date.iso8601,
      processDateTime: DateTime.now.to_date.iso8601
    }
  end
  let(:base_url) { LoanProService::ConfigHelper.configuration.loan_management_system_url }
  let(:loan_id) { Faker::Number.number(digits: 5) }
  let(:autopay_id) { Faker::Number.number(digits: 5) }
  let(:expected_request_body) do
    {
      Autopays: {
        results: [{
          __update: true,
          id: autopay_id,
          applyDate: payment_attributes[:applyDate],
          processDateTime: payment_attributes[:processDateTime]
        }]
      }
    }
  end

  describe '.call' do
    context 'with valid loan attributes' do
      subject { described_class.call(autopay_id:, loan_id:, attributes: payment_attributes) }

      it 'makes an API call to the correct endpoint' do
        request = stub_request(:put, "#{base_url}odata.svc/Loans(#{loan_id})")
                  .to_return(headers: { content_type: 'application/json' })

        expect { subject }.to_not raise_error
        expect(request).to have_been_requested
      end

      it 'includes both the specified and default attributes in the request payload' do
        request = stub_request(:put, "#{base_url}odata.svc/Loans(#{loan_id})").with do |req|
          request_body = JSON.parse(req.body, symbolize_names: true)
          expect(request_body).to eq(expected_request_body)
        end.to_return(headers: { content_type: 'application/json' })

        expect { subject }.to_not raise_error
        expect(request).to have_been_requested
      end

      it 'permits the default attributes to be overridden when specified directly' do
        ba_processor = rand(10..20)
        payment_attributes[:baProcessor] = ba_processor

        request = stub_request(:put, "#{base_url}odata.svc/Loans(#{loan_id})").with do |req|
          request_body = JSON.parse(req.body, symbolize_names: true)
          expect(request_body[:Autopays][:results].first[:baProcessor]).to eq(ba_processor)
        end.to_return(headers: { content_type: 'application/json' })

        expect { subject }.to_not raise_error
        expect(request).to have_been_requested
      end
    end

    context 'when error->message->value is present in API response and status is 200' do
      let(:error) { "Resource not found for the segment 'Loans'" }

      before do
        stub_request(:put, "#{base_url}odata.svc/Loans(#{loan_id})")
          .to_return(headers: { content_type: 'application/json' }, body: {
            error: { message: { lang: 'en-US', value: error } }
          }.to_json)
      end

      subject { described_class.call(autopay_id:, loan_id:, attributes: payment_attributes) }

      # because Faraday does not raise error on 200 status,
      # - this tests raising of error when 'error' is found in respose
      # - also tests correct error message extraction
      it 'raises a LoanManagementSystemError' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, error)
      end
    end

    context 'when unexpectedly formatted error is present in API response and status is 200' do
      before do
        stub_request(:put, "#{base_url}odata.svc/Loans(#{loan_id})")
          .to_return(headers: { content_type: 'application/json' }, body: {
            error: { test: 'unexpected error' }
          }.to_json)
      end

      subject { described_class.call(autopay_id:, loan_id:, attributes: payment_attributes) }

      # because Faraday does not raise error on 200 status,
      # - this tests raising of error when 'error' is found in respose
      # - also tests correct error message extraction
      it 'raises a LoanManagementSystemError' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, /unexpected error/i)
      end
    end

    context 'when status is 4xx (ClientError) but no response body is present' do
      before do
        stub_request(:put, "#{base_url}odata.svc/Loans(#{loan_id})")
          .to_return(headers: { content_type: 'application/json' }, status: 403)
      end

      subject { described_class.call(autopay_id:, loan_id:, attributes: payment_attributes) }

      it 'raises a LoanManagementSystemError with status' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, /status 403/)
      end
    end
  end
end
