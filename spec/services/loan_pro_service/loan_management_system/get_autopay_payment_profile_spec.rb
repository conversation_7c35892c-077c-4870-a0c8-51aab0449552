# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanProService::LoanManagementSystem::GetAutopayPaymentProfile do
  let(:auto_pay_id) { Faker::Number.number(digits: 4) }
  let(:account_number) { '1181011' }
  let(:base_url) { LoanProService::ConfigHelper.configuration.loan_management_system_url }
  let(:response_body) do
    {
      'PrimaryPaymentMethod' => { 'CheckingAccount' => { 'accountNumber' => account_number } }
    }
  end

  describe '.call' do
    context 'with valid auto_pay_id' do
      before do
        stub_request(:get, "#{base_url}odata.svc/Autopays(#{auto_pay_id})?nopaging=true")
          .to_return(headers: { content_type: 'application/json' }, body: { d: response_body }.to_json)
      end

      subject { described_class.call(auto_pay_id) }

      it 'should make an API call to the correct endpoint' do
        expect { subject }.to_not raise_error
        accnt_no = subject['PrimaryPaymentMethod']['CheckingAccount']['accountNumber']
        expect(accnt_no).to eq(account_number)
      end

      context 'with valid expand param' do
        before do
          stub_request(:get, "#{base_url}odata.svc/Autopays(#{auto_pay_id})?$expand=PrimaryPaymentMethod/CheckingAccount&nopaging=true")
            .to_return(headers: { content_type: 'application/json' }, body: { d: response_body }.to_json)
        end

        subject { described_class.call(auto_pay_id, expand: 'PrimaryPaymentMethod/CheckingAccount') }

        it 'should make an API call to the correct endpoint' do
          expect { subject }.to_not raise_error
          accnt_no = subject['PrimaryPaymentMethod']['CheckingAccount']['accountNumber']
          expect(accnt_no).to eq(account_number)
        end
      end
    end

    context 'when error->message->value is present in API response and status is 200' do
      let(:error) { "Resource not found for the segment 'Loans'" }
      before do
        stub_request(:get, "#{base_url}odata.svc/Autopays(#{auto_pay_id})?nopaging=true")
          .to_return(headers: { content_type: 'application/json' }, body: {
            error: { message: { lang: 'en-US', value: error } }
          }.to_json)
      end

      subject { described_class.call(auto_pay_id) }

      # because Faraday does not raise error on 200 status,
      # - this tests raising of error when 'error' is found in response
      # - also tests correct error message extraction
      it 'should raise LoanManagementSystemError' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, error)
      end
    end

    context 'when unexpectedly formatted error is present in API response and status is 200' do
      before do
        stub_request(:get, "#{base_url}odata.svc/Autopays(#{auto_pay_id})?nopaging=true")
          .to_return(headers: { content_type: 'application/json' }, body: {
            error: { test: 'unexpected error' }
          }.to_json)
      end

      subject { described_class.call(auto_pay_id) }

      # because Faraday does not raise error on 200 status,
      # - this tests raising of error when 'error' is found in response
      # - also tests correct error message extraction
      it 'should raise LoanManagementSystemError' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, /unexpected error/i)
      end
    end

    context 'when status is 4xx (ClientError) but no response body is present' do
      before do
        stub_request(:get, "#{base_url}odata.svc/Autopays(#{auto_pay_id})?nopaging=true")
          .to_return(headers: { content_type: 'application/json' }, status: 403)
      end
      subject { described_class.call(auto_pay_id) }

      it 'should raise LoanManagementSystemError with status' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, /status 403/)
      end
    end
  end
end
