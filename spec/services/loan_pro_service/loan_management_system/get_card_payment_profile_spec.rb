# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanProService::LoanManagementSystem::GetCardPaymentProfile do
  let(:base_url) { LoanProService::ConfigHelper.configuration.loan_management_system_url }
  let(:customer_id) { Faker::Number.number(digits: 5) }
  let(:customer_base_data) { { 'id' => customer_id } }
  subject { described_class.call(customer_id:) }

  describe '.call' do
    context 'with valid customer attributes' do
      it 'makes an API call to the correct endpoint for a card' do
        request = stub_request(:get, "#{base_url}odata.svc/Customers(#{customer_id})/PaymentAccounts?$expand=CreditCard")
                  .to_return(headers: { content_type: 'application/json' }, body: { d: customer_base_data }.to_json)

        expect { subject }.to_not raise_error
        expect(request).to have_been_requested
        expect(subject['id']).to eq(customer_id)
      end
    end

    context 'when error->message->value is present in API response and status is 200' do
      let(:error) { "Resource not found for the segment 'Customers'" }

      before do
        stub_request(:get, "#{base_url}odata.svc/Customers(#{customer_id})/PaymentAccounts?$expand=CreditCard")
          .to_return(headers: { content_type: 'application/json' }, body: {
            error: { message: { lang: 'en-US', value: error } }
          }.to_json)
      end

      it 'raises a LoanManagementSystemError' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, error)
      end
    end

    context 'when unexpectedly formatted error is present in API response and status is 200' do
      before do
        stub_request(:get, "#{base_url}odata.svc/Customers(#{customer_id})/PaymentAccounts?$expand=CreditCard")
          .to_return(headers: { content_type: 'application/json' }, body: {
            error: { test: 'unexpected error' }
          }.to_json)
      end

      it 'raises a LoanManagementSystemError' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, /unexpected error/i)
      end
    end

    context 'when status is 4xx (ClientError) but no response body is present' do
      before do
        stub_request(:get, "#{base_url}odata.svc/Customers(#{customer_id})/PaymentAccounts?$expand=CreditCard")
          .to_return(headers: { content_type: 'application/json' }, status: 403)
      end

      it 'raises a LoanManagementSystemError with status and logs the error' do
        expect(Rails.logger).to receive(:error).with(/GetCardPaymentProfile - Failed to find a payment profile for customer id:/)
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, /status 403/)
      end
    end
  end
end
