# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanProService::LoanManagementSystem::GetCustomer do
  let(:customer_id) { Faker::Number.number(digits: 4) }
  let(:base_url) { LoanProService::ConfigHelper.configuration.loan_management_system_url }

  let(:customer_base_data) do
    {
      'id' => customer_id
    }
  end

  let(:phones_data) do
    [{
      'id' => Faker::Number.number(digits: 4),
      'phone' => '5555555555',
      'active' => 1
    }]
  end

  describe '.call' do
    context 'with valid customer_id' do
      before do
        stub_request(:get, "#{base_url}odata.svc/Customers(#{customer_id})?nopaging=true")
          .to_return(headers: { content_type: 'application/json' }, body: { d: customer_base_data }.to_json)
      end

      subject { described_class.call(customer_id) }

      it 'should make an API call to the correct endpoint' do
        expect { subject }.to_not raise_error
        expect(subject['id']).to eq(customer_id)
      end

      context 'with valid expand param' do
        before do
          stub_request(:get, "#{base_url}odata.svc/Customers(#{customer_id})?$expand=Phones&nopaging=true")
            .to_return(headers: { content_type: 'application/json' }, body: { d: customer_base_data.merge('Phones' => phones_data) }.to_json)
        end

        subject { described_class.call(customer_id, expand: 'Phones') }

        it 'should make an API call to the correct endpoint' do
          expect { subject }.to_not raise_error
          expect(subject['id']).to eq(customer_id)
        end
      end
    end

    context 'when error->message->value is present in API response and status is 200' do
      let(:error) { "Resource not found for the segment 'Loans'" }
      before do
        stub_request(:get, "#{base_url}odata.svc/Customers(#{customer_id})?nopaging=true")
          .to_return(headers: { content_type: 'application/json' }, body: {
            error: { message: { lang: 'en-US', value: error } }
          }.to_json)
      end

      subject { described_class.call(customer_id) }

      # because Faraday does not raise error on 200 status,
      # - this tests raising of error when 'error' is found in respose
      # - also tests correct error message extraction
      it 'should raise LoanManagementSystemError' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, error)
      end
    end

    context 'when unexpectedly formatted error is present in API response and status is 200' do
      before do
        stub_request(:get, "#{base_url}odata.svc/Customers(#{customer_id})?nopaging=true")
          .to_return(headers: { content_type: 'application/json' }, body: {
            error: { test: 'unexpected error' }
          }.to_json)
      end

      subject { described_class.call(customer_id) }

      # because Faraday does not raise error on 200 status,
      # - this tests raising of error when 'error' is found in respose
      # - also tests correct error message extraction
      it 'should raise LoanManagementSystemError' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, /unexpected error/i)
      end
    end

    context 'when status is 4xx (ClientError) but no response body is present' do
      before do
        stub_request(:get, "#{base_url}odata.svc/Customers(#{customer_id})?nopaging=true")
          .to_return(headers: { content_type: 'application/json' }, status: 403)
      end
      subject { described_class.call(customer_id) }

      it 'should raise LoanManagementSystemError with status' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, /status 403/)
      end
    end
  end
end
