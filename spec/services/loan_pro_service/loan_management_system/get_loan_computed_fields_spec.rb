# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanProService::LoanManagementSystem::GetLoanComputedFields do
  let(:loan_id) { Faker::Number.number(digits: 4) }
  let(:base_url) { LoanProService::ConfigHelper.configuration.loan_management_system_url }
  let(:investor_portfolio_id) { LoanProService::ConfigHelper.configuration.investor_portfolio_id }
  let(:computed_fields_base_data) do
    {
      'results' => {
        '1' => '2023-07-02',
        '2' => 3503.90***********,
        '3' => 2764.8499999999999,
        '4' => 921.**************,
        '8' => {
          '1' => {
            'bucket-number' => 1,
            'scheduled-payment' => 921.**************,
            'date' => '2023-07-17',
            'past-due-amount' => 1842.90***********,
            'new-due-amount' => 2764.8499999999999,
            'amount-due' => 921.94999999999982,
            'days-past-due' => 9
          },
          '2' => {
            'bucket-number' => 2,
            'scheduled-payment' => 921.**************,
            'date' => '2023-07-07',
            'past-due-amount' => 0,
            'new-due-amount' => 1842.90***********,
            'amount-due' => 1842.90***********,
            'days-past-due' => 19
          },
          'LARGEST-BUCKET' => {
            'bucket-number' => 2,
            'scheduled-payment' => 921.**************,
            'date' => '2023-07-07',
            'past-due-amount' => 0,
            'new-due-amount' => 1842.90***********,
            'amount-due' => 1842.90***********,
            'days-past-due' => 19
          }
        },
        '12' => 36_479.************,
        '13' => 1842.*************,
        '16' => 'Cross River Bank',
        '17' => 1
      }
    }
  end

  describe '.call' do
    context 'with valid loan_id' do
      before do
        stub_request(:get, "#{base_url}computation(#{loan_id})?serverless=true")
          .to_return(headers: { content_type: 'application/json' }, body: { d: computed_fields_base_data }.to_json)
      end

      subject { described_class.call(loan_id) }

      it 'should make an API call to the correct endpoint' do
        expect { subject }.to_not raise_error
        expect(subject.dig('results', '17')).to eq(1)
      end
    end

    context 'when error->message->value is present in API response and status is 200' do
      let(:error) { "Resource not found for the segment 'Loans'" }
      before do
        stub_request(:get, "#{base_url}computation(#{loan_id})?serverless=true")
          .to_return(headers: { content_type: 'application/json' }, body: {
            error: { message: { lang: 'en-US', value: error } }
          }.to_json)
      end

      subject { described_class.call(loan_id) }

      # because Faraday does not raise error on 200 status,
      # - this tests raising of error when 'error' is found in respose
      # - also tests correct error message extraction
      it 'should raise LoanManagementSystemError' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, error)
      end
    end

    context 'when unexpectedly formatted error is present in API response and status is 200' do
      before do
        stub_request(:get, "#{base_url}computation(#{loan_id})?serverless=true")
          .to_return(headers: { content_type: 'application/json' }, body: {
            error: { test: 'unexpected error' }
          }.to_json)
      end

      subject { described_class.call(loan_id) }

      # because Faraday does not raise error on 200 status,
      # - this tests raising of error when 'error' is found in respose
      # - also tests correct error message extraction
      it 'should raise LoanManagementSystemError' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, /unexpected error/i)
      end
    end

    context 'when status is 4xx (ClientError) but no response body is present' do
      before do
        stub_request(:get, "#{base_url}computation(#{loan_id})?serverless=true")
          .to_return(headers: { content_type: 'application/json' }, status: 403)
      end
      subject { described_class.call(loan_id) }

      it 'should raise LoanManagementSystemError with status' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, /status 403/)
      end
    end
  end
end
