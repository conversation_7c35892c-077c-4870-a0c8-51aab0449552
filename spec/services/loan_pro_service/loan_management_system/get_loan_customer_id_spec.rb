# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanProService::LoanManagementSystem::GetLoanCustomerId do
  let(:loan_id) { Faker::Number.number(digits: 4) }
  let!(:customer_id) { Faker::Number.number(digits: 5) }
  let(:base_url) { LoanProService::ConfigHelper.configuration.loan_management_system_url }
  let!(:customer_base_data) do
    {
      'Customers' => {
        'results' => [
          { 'id' => customer_id }
        ]
      }
    }
  end
  subject { described_class.call(loan_id) }

  describe '.call' do
    context 'with valid loan_id' do
      it 'should make an API call to the correct endpoint' do
        request = stub_request(:get, "#{base_url}odata.svc/Loans(#{loan_id})?$expand=Customers")
                  .to_return(headers: { content_type: 'application/json' }, body: { d: customer_base_data }.to_json)

        expect { subject }.to_not raise_error
        expect(subject).to eq(customer_id)
        expect(request).to have_been_requested
      end
    end

    context 'when error->message->value is present in API response and status is 200' do
      let(:error) { "Resource not found for the segment 'Loans'" }
      before do
        stub_request(:get, "#{base_url}odata.svc/Loans(#{loan_id})?$expand=Customers")
          .to_return(headers: { content_type: 'application/json' }, body: {
            error: { message: { lang: 'en-US', value: error } }
          }.to_json)
      end

      it 'raises LoanManagementSystemError' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, error)
      end
    end

    context 'when unexpectedly formatted error is present in API response and status is 200' do
      before do
        stub_request(:get, "#{base_url}odata.svc/Loans(#{loan_id})?$expand=Customers")
          .to_return(headers: { content_type: 'application/json' }, body: {
            error: { test: 'unexpected error' }
          }.to_json)
      end

      it 'raises LoanManagementSystemError' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, /unexpected error/i)
      end
    end

    context 'when status is 4xx (ClientError) but no response body is present' do
      before do
        stub_request(:get, "#{base_url}odata.svc/Loans(#{loan_id})?$expand=Customers")
          .to_return(headers: { content_type: 'application/json' }, status: 403)
      end

      it 'raises a LoanManagementSystemError with status and logs the error' do
        expect(Rails.logger).to receive(:error).with(/GetLoanCustomerId - Unable to find customer id for loan id:/)
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, /status 403/)
      end
    end
  end
end
