# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanProService::LoanManagementSystem::GetLoanCustomers do
  let(:loan_id) { Faker::Number.number(digits: 4) }
  let!(:customer_id) { Faker::Number.number(digits: 5) }
  let(:base_url) { LoanProService::ConfigHelper.configuration.loan_management_system_url }
  let(:customer_base_data) do
    {
      '__metadata' => { 'uri' => 'http://loanpro.simnang.com/api/public/api/1/odata.svc/Loans(id=20821)', 'type' => 'Entity.Loan' },
      'LoanSettings' => { '__deferred' => { 'uri' => 'Loans(id=20821)/LoanSettings' } },
      'LoanSetup' => { '__deferred' => { 'uri' => 'Loans(id=20821)/LoanSetup' } },
      'Customers' => [
        {
          '__metadata' => { 'uri' => 'http://loanpro.simnang.com/api/public/api/1/odata.svc/Customers(id=9507)', 'type' => 'Entity.Customer' },
          'PrimaryAddress' => { '__deferred' => { 'uri' => 'Customers(id=9507)/PrimaryAddress' } },
          'MailAddress' => { '__deferred' => { 'uri' => 'Customers(id=9507)/MailAddress' } },
          'Employer' => { '__deferred' => { 'uri' => 'Customers(id=9507)/Employer' } },
          'References' => { '__deferred' => { 'uri' => 'Customers(id=9507)/References' } },
          'PaymentAccounts' => { '__deferred' => { 'uri' => 'Customers(id=9507)/PaymentAccounts' } },
          'Phones' => { '__deferred' => { 'uri' => 'Customers(id=9507)/Phones' } },
          'CustomFieldValues' => { '__deferred' => { 'uri' => 'Customers(id=9507)/CustomFieldValues' } },
          'Documents' => { '__deferred' => { 'uri' => 'Customers(id=9507)/Documents' } },
          'CreditScore' => { '__deferred' => { 'uri' => 'Customers(id=9507)/CreditScore' } },
          'Loans' => { '__deferred' => { 'uri' => 'Customers(id=9507)/Loans' } },
          'LineOfCredits' => { '__deferred' => { 'uri' => 'Customers(id=9507)/LineOfCredits' } },
          'SocialProfiles' => { '__deferred' => { 'uri' => 'Customers(id=9507)/SocialProfiles' } },
          'Notes' => { '__deferred' => { 'uri' => 'Customers(id=9507)/Notes' } },
          'id' => 9507,
          'customId' => '9507',
          'mcId' => 5_877_616,
          'customerType' => 'customer.type.individual',
          'status' => 'Active',
          'firstName' => 'ERICA',
          'lastName' => 'LAMBERT',
          'middleName' => nil,
          'birthDate' => '/Date(**********)/',
          'gender' => 'customer.gender.unknown',
          'generationCode' => 'customer.generationCode.none',
          'email' => '<EMAIL>',
          'ssn' => '*********',
          'driverLicense' => nil,
          'companyName' => nil,
          'contactName' => nil,
          'customerIdType' => 'customer.idType.ssn',
          'customerId' => nil,
          'creditLimit' => 0,
          'accessUserName' => '<EMAIL>',
          'active' => 1,
          'ofacMatch' => 0,
          'ofacTested' => 0,
          'saleTransferPii' => 1,
          'passwordChanged' => 0,
          'hasAvatar' => 0,
          'loanRole' => 'loan.customerRole.primary',
          'created' => '/Date(**********)/',
          'lastUpdate' => '/Date(**********)/',
          'creditScoreId' => nil
        },
        {
          '__metadata' => { 'uri' => 'http://loanpro.simnang.com/api/public/api/1/odata.svc/Customers(id=16193)', 'type' => 'Entity.Customer' },
          'PrimaryAddress' => { '__deferred' => { 'uri' => 'Customers(id=16193)/PrimaryAddress' } },
          'MailAddress' => { '__deferred' => { 'uri' => 'Customers(id=16193)/MailAddress' } },
          'Employer' => { '__deferred' => { 'uri' => 'Customers(id=16193)/Employer' } },
          'References' => { '__deferred' => { 'uri' => 'Customers(id=16193)/References' } },
          'PaymentAccounts' => { '__deferred' => { 'uri' => 'Customers(id=16193)/PaymentAccounts' } },
          'Phones' => { '__deferred' => { 'uri' => 'Customers(id=16193)/Phones' } },
          'CustomFieldValues' => { '__deferred' => { 'uri' => 'Customers(id=16193)/CustomFieldValues' } },
          'Documents' => { '__deferred' => { 'uri' => 'Customers(id=16193)/Documents' } },
          'CreditScore' => { '__deferred' => { 'uri' => 'Customers(id=16193)/CreditScore' } },
          'Loans' => { '__deferred' => { 'uri' => 'Customers(id=16193)/Loans' } },
          'LineOfCredits' => { '__deferred' => { 'uri' => 'Customers(id=16193)/LineOfCredits' } },
          'SocialProfiles' => { '__deferred' => { 'uri' => 'Customers(id=16193)/SocialProfiles' } },
          'Notes' => { '__deferred' => { 'uri' => 'Customers(id=16193)/Notes' } },
          'id' => 16_193,
          'customId' => nil,
          'mcId' => 6_609_906,
          'customerType' => 'customer.type.individual',
          'status' => 'Active',
          'firstName' => 'ERICA',
          'lastName' => 'LAMBERT',
          'middleName' => nil,
          'birthDate' => '/Date(*********)/',
          'gender' => 'customer.gender.unknown',
          'generationCode' => 'customer.generationCode.none',
          'email' => '<EMAIL>',
          'ssn' => '*********',
          'driverLicense' => nil,
          'companyName' => nil,
          'contactName' => nil,
          'customerIdType' => 'customer.idType.ssn',
          'customerId' => nil,
          'creditLimit' => 0,
          'accessUserName' => '<EMAIL>',
          'active' => 1,
          'ofacMatch' => 0,
          'ofacTested' => 0,
          'saleTransferPii' => 1,
          'passwordChanged' => 0,
          'hasAvatar' => 0,
          'loanRole' => 'loan.customerRole.secondary',
          'created' => '/Date(**********)/',
          'lastUpdate' => '/Date(**********)/',
          'creditScoreId' => nil
        },
        {
          '__metadata' => { 'uri' => 'http://loanpro.simnang.com/api/public/api/1/odata.svc/Customers(id=16194)', 'type' => 'Entity.Customer' },
          'PrimaryAddress' => { '__deferred' => { 'uri' => 'Customers(id=16194)/PrimaryAddress' } },
          'MailAddress' => { '__deferred' => { 'uri' => 'Customers(id=16194)/MailAddress' } },
          'Employer' => { '__deferred' => { 'uri' => 'Customers(id=16194)/Employer' } },
          'References' => { '__deferred' => { 'uri' => 'Customers(id=16194)/References' } },
          'PaymentAccounts' => { '__deferred' => { 'uri' => 'Customers(id=16194)/PaymentAccounts' } },
          'Phones' => { '__deferred' => { 'uri' => 'Customers(id=16194)/Phones' } },
          'CustomFieldValues' => { '__deferred' => { 'uri' => 'Customers(id=16194)/CustomFieldValues' } },
          'Documents' => { '__deferred' => { 'uri' => 'Customers(id=16194)/Documents' } },
          'CreditScore' => { '__deferred' => { 'uri' => 'Customers(id=16194)/CreditScore' } },
          'Loans' => { '__deferred' => { 'uri' => 'Customers(id=16194)/Loans' } },
          'LineOfCredits' => { '__deferred' => { 'uri' => 'Customers(id=16194)/LineOfCredits' } },
          'SocialProfiles' => { '__deferred' => { 'uri' => 'Customers(id=16194)/SocialProfiles' } },
          'Notes' => { '__deferred' => { 'uri' => 'Customers(id=16194)/Notes' } },
          'id' => 16_194,
          'customId' => nil,
          'mcId' => 6_609_916,
          'customerType' => 'customer.type.individual',
          'status' => 'Active',
          'firstName' => 'ERICA',
          'lastName' => 'LAMBERT',
          'middleName' => nil,
          'birthDate' => '/Date(-17193600)/',
          'gender' => 'customer.gender.unknown',
          'generationCode' => 'customer.generationCode.none',
          'email' => '<EMAIL>',
          'ssn' => '*********',
          'driverLicense' => nil,
          'companyName' => nil,
          'contactName' => nil,
          'customerIdType' => 'customer.idType.ssn',
          'customerId' => nil,
          'creditLimit' => 0,
          'accessUserName' => '<EMAIL>',
          'active' => 1,
          'ofacMatch' => 0,
          'ofacTested' => 0,
          'saleTransferPii' => 1,
          'passwordChanged' => 0,
          'hasAvatar' => 0,
          'loanRole' => 'loan.customerRole.additional',
          'created' => '/Date(**********)/',
          'lastUpdate' => '/Date(**********)/',
          'creditScoreId' => nil
        }
      ],
      'Portfolios' => { '__deferred' => { 'uri' => 'Loans(id=20821)/Portfolios' } },
      'LoanFundingTransactions' => { '__deferred' => { 'uri' => 'Loans(id=20821)/LoanFundingTransactions' } },
      'id' => 20_821,
      'displayId' => '********',
      'title' => '********',
      'settingsId' => 20_616,
      'setupId' => 20_711,
      'insurancePolicyId' => nil,
      'collateralId' => 0,
      'linkedLoan' => nil,
      'modId' => nil,
      'modTotal' => 0,
      'humanActivityDate' => '/Date(**********)/',
      'created' => '/Date(**********)/',
      'lastMaintRun' => '/Date(**********)/',
      'createdBy' => 6546,
      'active' => 1,
      'archived' => 0,
      'loanAlert' => nil,
      'temporaryAccount' => 0,
      'deleted' => 0,
      'deletedAt' => nil,
      '_relatedMetadata' => nil,
      '_dynamicProperties' => nil
    }
  end
  subject { described_class.call(loan_id) }

  describe '.call' do
    context 'with valid loan_id' do
      it 'should make an API call to the correct endpoint' do
        request = stub_request(:get, "#{base_url}odata.svc/Loans(#{loan_id})?$expand=Customers&nopaging=true")
                  .to_return(headers: { content_type: 'application/json' }, body: { d: customer_base_data }.to_json)

        expect { subject }.to_not raise_error
        expect(subject).to eq(customer_base_data['Customers'])
        expect(request).to have_been_requested
      end
    end

    context 'when error->message->value is present in API response and status is 200' do
      let(:error) { "Resource not found for the segment 'Loans'" }
      before do
        stub_request(:get, "#{base_url}odata.svc/Loans(#{loan_id})?$expand=Customers&nopaging=true")
          .to_return(headers: { content_type: 'application/json' }, body: {
            error: { message: { lang: 'en-US', value: error } }
          }.to_json)
      end

      it 'raises LoanManagementSystemError' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, error)
      end
    end

    context 'when unexpectedly formatted error is present in API response and status is 200' do
      before do
        stub_request(:get, "#{base_url}odata.svc/Loans(#{loan_id})?$expand=Customers&nopaging=true")
          .to_return(headers: { content_type: 'application/json' }, body: {
            error: { test: 'unexpected error' }
          }.to_json)
      end

      it 'raises LoanManagementSystemError' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, /unexpected error/i)
      end
    end

    context 'when status is 4xx (ClientError) but no response body is present' do
      before do
        stub_request(:get, "#{base_url}odata.svc/Loans(#{loan_id})?$expand=Customers&nopaging=true")
          .to_return(headers: { content_type: 'application/json' }, status: 403)
      end

      it 'raises a LoanManagementSystemError with status and logs the error' do
        expect(Rails.logger).to receive(:error).with(/GetLoanCustomers - Unable to find customers for loan id:/)
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, /status 403/)
      end
    end
  end
end
