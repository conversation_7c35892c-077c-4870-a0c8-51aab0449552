# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanProService::LoanManagementSystem::GetLoanPastDueBreakdown do
  let(:loan_id) { Faker::Number.number(digits: 4) }
  let(:base_url) { LoanProService::ConfigHelper.configuration.loan_management_system_url }
  let(:investor_portfolio_id) { LoanProService::ConfigHelper.configuration.investor_portfolio_id }
  let(:past_due_breakdown_data) do
    {
      'success' => true,
      'breakdown' => [
        {
          'date' => '2022-11-15',
          'totalDue' => 873.91,
          'pastDue' => 0,
          'newDue' => 873.91,
          'DaysPastDue' => 258,
          'dueFees' => 0,
          'periodPayment' => 873.91,
          'hasApdAdjustment' => false,
          'payoff' => 21_167.89
        },
        {
          'date' => '2022-12-15',
          'totalDue' => 1747.82,
          'pastDue' => 873.91,
          'newDue' => 873.91,
          'DaysPastDue' => 228,
          'dueFees' => 0,
          'periodPayment' => 873.91,
          'hasApdAdjustment' => false,
          'payoff' => 20_632.2
        },
        {
          'date' => '2023-01-15',
          'totalDue' => 2621.73,
          'pastDue' => 1747.82,
          'newDue' => 873.9100000000001,
          'DaysPastDue' => 197,
          'dueFees' => 0,
          'periodPayment' => 873.91,
          'hasApdAdjustment' => false,
          'payoff' => 20_098.94
        }
      ]
    }
  end

  describe '.call' do
    context 'with valid loan_id' do
      before do
        stub_request(:get, "#{base_url}Loans(#{loan_id})/Autopal.GetPastDueBreakdown()")
          .to_return(headers: { content_type: 'application/json' }, body: { d: past_due_breakdown_data }.to_json)
      end

      subject { described_class.call(loan_id) }

      it 'should make an API call to the correct endpoint' do
        expect { subject }.to_not raise_error
        expect(subject['success']).to eq(true)
      end
    end

    context 'when error->message->value is present in API response and status is 200' do
      let(:error) { "Resource not found for the segment 'Loans'" }
      before do
        stub_request(:get, "#{base_url}Loans(#{loan_id})/Autopal.GetPastDueBreakdown()")
          .to_return(headers: { content_type: 'application/json' }, body: {
            error: { message: { lang: 'en-US', value: error } }
          }.to_json)
      end

      subject { described_class.call(loan_id) }

      # because Faraday does not raise error on 200 status,
      # - this tests raising of error when 'error' is found in respose
      # - also tests correct error message extraction
      it 'should raise LoanManagementSystemError' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, error)
      end
    end

    context 'when unexpectedly formatted error is present in API response and status is 200' do
      before do
        stub_request(:get, "#{base_url}Loans(#{loan_id})/Autopal.GetPastDueBreakdown()")
          .to_return(headers: { content_type: 'application/json' }, body: {
            error: { test: 'unexpected error' }
          }.to_json)
      end

      subject { described_class.call(loan_id) }

      # because Faraday does not raise error on 200 status,
      # - this tests raising of error when 'error' is found in respose
      # - also tests correct error message extraction
      it 'should raise LoanManagementSystemError' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, /unexpected error/i)
      end
    end

    context 'when status is 4xx (ClientError) but no response body is present' do
      before do
        stub_request(:get, "#{base_url}Loans(#{loan_id})/Autopal.GetPastDueBreakdown()")
          .to_return(headers: { content_type: 'application/json' }, status: 403)
      end
      subject { described_class.call(loan_id) }

      it 'should raise LoanManagementSystemError with status' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, /status 403/)
      end
    end
  end
end
