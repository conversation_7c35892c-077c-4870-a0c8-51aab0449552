# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanProService::LoanManagementSystem::GetLoanPayoffs do
  let(:loan_id) { Faker::Number.number(digits: 4) }
  let(:start_date) { '2023-11-01' }
  let(:base_url) { LoanProService::ConfigHelper.configuration.loan_management_system_url }
  let(:payoff_data) do
    {
      '2023-11-01' => {
        'date' => '2023-11-01',
        'payoff' => 12_988.89,
        'change' => 1.34,
        'dailyInterest' => 1.3404980794520542,
        'details' => {
          'principal' => 12_909.809999999994,
          'fees' => 0,
          'interest' => 79.08444430410965,
          'escrow' => 0,
          'escrowAlt' => 0,
          'totalEscrow' => 0,
          'escrowSubsets' => {
            '2' => { 'escrow' => 0, 'escrowAlt' => 0, 'totalEscrow' => 0 },
            '3' => { 'escrow' => 0, 'escrowAlt' => 0, 'totalEscrow' => 0 },
            '4' => { 'escrow' => 0, 'escrowAlt' => 0, 'totalEscrow' => 0 },
            '5' => { 'escrow' => 0, 'escrowAlt' => 0, 'totalEscrow' => 0 }
          },
          'finalInterest' => 0,
          'paymentsPending' => 0
        }
      },
      '2023-11-02' => {
        'date' => '2023-11-02',
        'payoff' => 12_990.23,
        'change' => 1.34,
        'dailyInterest' => 1.3404980794520542,
        'details' => {
          'principal' => 12_909.809999999994,
          'fees' => 0,
          'interest' => 80.42494238356171,
          'escrow' => 0,
          'escrowAlt' => 0,
          'totalEscrow' => 0,
          'escrowSubsets' => {
            '2' => { 'escrow' => 0, 'escrowAlt' => 0, 'totalEscrow' => 0 },
            '3' => { 'escrow' => 0, 'escrowAlt' => 0, 'totalEscrow' => 0 },
            '4' => { 'escrow' => 0, 'escrowAlt' => 0, 'totalEscrow' => 0 },
            '5' => { 'escrow' => 0, 'escrowAlt' => 0, 'totalEscrow' => 0 }
          },
          'finalInterest' => 0,
          'paymentsPending' => 0
        }
      }
    }
  end

  describe '.call' do
    context 'when valid' do
      before do
        stub_request(:get, "#{base_url}Loans(#{loan_id})/Autopal.GetLoanPayoff(#{start_date})")
          .to_return(headers: { content_type: 'application/json' }, body: { d: payoff_data }.to_json)
      end

      subject { described_class.call(loanpro_loan_id: loan_id, start_date:) }

      it 'should make an API call to the correct endpoint' do
        expect { subject }.to_not raise_error
        expect(subject).to eq(payoff_data)
      end
    end

    context 'when error->message->value is present in API response and status is 200' do
      let(:error) { "Resource not found for the segment 'Loans'" }
      before do
        stub_request(:get, "#{base_url}Loans(#{loan_id})/Autopal.GetLoanPayoff(#{start_date})")
          .to_return(headers: { content_type: 'application/json' }, body: {
            error: { message: { lang: 'en-US', value: error } }
          }.to_json)
      end

      subject { described_class.call(loanpro_loan_id: loan_id, start_date:) }

      # because Faraday does not raise error on 200 status,
      # - this tests raising of error when 'error' is found in respose
      # - also tests correct error message extraction
      it 'should raise LoanManagementSystemError' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, error)
      end
    end

    context 'when unexpectedly formatted error is present in API response and status is 200' do
      before do
        stub_request(:get, "#{base_url}Loans(#{loan_id})/Autopal.GetLoanPayoff(#{start_date})")
          .to_return(headers: { content_type: 'application/json' }, body: {
            error: { test: 'unexpected error' }
          }.to_json)
      end

      subject { described_class.call(loanpro_loan_id: loan_id, start_date:) }

      # because Faraday does not raise error on 200 status,
      # - this tests raising of error when 'error' is found in respose
      # - also tests correct error message extraction
      it 'should raise LoanManagementSystemError' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, /unexpected error/i)
      end
    end

    context 'when status is 4xx (ClientError) but no response body is present' do
      before do
        stub_request(:get, "#{base_url}Loans(#{loan_id})/Autopal.GetLoanPayoff(#{start_date})")
          .to_return(headers: { content_type: 'application/json' }, status: 403)
      end
      subject { described_class.call(loanpro_loan_id: loan_id, start_date:) }

      it 'should raise LoanManagementSystemError with status' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, /status 403/)
      end
    end
  end
end
