# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanProService::LoanManagementSystem::GetPaymentProfile do
  let(:base_url) { LoanProService::ConfigHelper.configuration.loan_management_system_url }
  let(:customer_id) { Faker::Number.number(digits: 5) }
  let(:customer_base_data) { { 'id' => customer_id } }

  describe '.call' do
    context 'with valid customer attributes' do
      context 'primary payment profile ' do
        let!(:is_primary) { true }
        let!(:include_bank_acct_info) { true }
        subject { described_class.call(customer_id: customer_id, is_primary: is_primary, include_bank_acct_info: include_bank_acct_info) }

        it 'makes an API call to the correct endpoint for a primary' do
          request = stub_request(:get, "#{base_url}odata.svc/Customers(#{customer_id})/PaymentAccounts?$expand=CheckingAccount&$filter=isPrimary%20eq%201&all=true")
                    .to_return(headers: { content_type: 'application/json' }, body: { d: customer_base_data }.to_json)

          expect { subject }.to_not raise_error
          expect(request).to have_been_requested
          expect(subject['id']).to eq(customer_id)
        end
      end

      context 'secondary payment profile ' do
        let!(:is_primary) { false }
        let!(:include_bank_acct_info) { true }

        subject { described_class.call(customer_id: customer_id, is_primary: is_primary, include_bank_acct_info: include_bank_acct_info) }
        it 'makes an API call to the correct endpoint for a secondary' do
          request = stub_request(:get, "#{base_url}odata.svc/Customers(#{customer_id})/PaymentAccounts?$expand=CheckingAccount&$filter=isPrimary%20eq%200&all=true")
                    .to_return(headers: { content_type: 'application/json' }, body: { d: customer_base_data }.to_json)

          expect { subject }.to_not raise_error
          expect(request).to have_been_requested
          expect(subject['id']).to eq(customer_id)
        end
      end

      context 'all payment profiles' do
        let!(:is_primary) { nil }
        let!(:include_bank_acct_info) { true }

        subject { described_class.call(customer_id: customer_id, is_primary: is_primary, include_bank_acct_info: include_bank_acct_info) }

        it 'makes an API for all payment profiles' do
          request = stub_request(:get, "https://loanpro.simnang.com/api/public/api/1/odata.svc/Customers(#{customer_id})/PaymentAccounts?$expand=CheckingAccount&all=true")
                    .to_return(headers: { content_type: 'application/json' }, body: { d: customer_base_data }.to_json)

          expect { subject }.to_not raise_error
          expect(request).to have_been_requested
          expect(subject['id']).to eq(customer_id)
        end
      end
    end

    context 'when error->message->value is present in API response and status is 200' do
      let!(:error) { "Resource not found for the segment 'Customers'" }
      let!(:include_bank_acct_info) { true }
      let!(:is_primary) { true }
      before do
        stub_request(:get, "#{base_url}odata.svc/Customers(#{customer_id})/PaymentAccounts?$expand=CheckingAccount&$filter=isPrimary%20eq%201&all=true")
          .to_return(headers: { content_type: 'application/json' }, body: {
            error: { message: { lang: 'en-US', value: error } }
          }.to_json)
      end

      subject { described_class.call(customer_id:, is_primary:, include_bank_acct_info:) }

      # because Faraday does not raise error on 200 status,
      # - this tests raising of error when 'error' is found in response
      # - also tests correct error message extraction
      it 'raises a LoanManagementSystemError' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, error)
      end
    end

    context 'when unexpectedly formatted error is present in API response and status is 200' do
      let!(:include_bank_acct_info) { true }
      let!(:is_primary) { true }
      before do
        stub_request(:get, "#{base_url}odata.svc/Customers(#{customer_id})/PaymentAccounts?$expand=CheckingAccount&$filter=isPrimary%20eq%201&all=true")
          .to_return(headers: { content_type: 'application/json' }, body: {
            error: { test: 'unexpected error' }
          }.to_json)
      end

      subject { described_class.call(customer_id:, is_primary:, include_bank_acct_info:) }

      # because Faraday does not raise error on 200 status,
      # - this tests raising of error when 'error' is found in response
      # - also tests correct error message extraction
      it 'raises a LoanManagementSystemError' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, /unexpected error/i)
      end
    end

    context 'when status is 4xx (ClientError) but no response body is present' do
      let!(:include_bank_acct_info) { true }
      let!(:is_primary) { true }
      before do
        stub_request(:get, "#{base_url}odata.svc/Customers(#{customer_id})/PaymentAccounts?$expand=CheckingAccount&$filter=isPrimary%20eq%201&all=true")
          .to_return(headers: { content_type: 'application/json' }, status: 403)
      end

      subject { described_class.call(customer_id:, is_primary:, include_bank_acct_info:) }

      it 'raises a LoanManagementSystemError with status' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, /status 403/)
      end
    end
  end
end
