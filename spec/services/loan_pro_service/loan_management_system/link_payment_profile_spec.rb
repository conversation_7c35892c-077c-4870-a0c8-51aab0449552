# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanProService::LoanManagementSystem::LinkPaymentProfile do
  let(:first_name) { Faker::Name.first_name }
  let(:account_number_last4) { Faker::Bank.account_number[-4..] }
  let!(:customer_id) { Faker::Number.number(digits: 5) }
  let!(:token) { Faker::Alphanumeric.alphanumeric(number: 20).to_s }
  let(:date) { Date.today.iso8601 }
  let!(:title) { "Link Payment Profile for Debit Card Payment on #{date.to_datetime}" }
  let(:base_url) { LoanProService::ConfigHelper.configuration.loan_management_system_url }
  let(:customer_base_data) { { 'id' => customer_id } }
  let(:payload) do
    { attributes: {
        'PaymentAccounts' => {
          'results' => [
            {
              'CreditCard' => { 'token' => token.to_s },
              'active' => 1,
              'isPrimary' => 1,
              'isSecondary' => 0,
              'title' => title,
              'type' => 'paymentAccount.type.credit'
            }
          ]
        }
      },
      customer_id: customer_id,
      query: 'Customers' }
  end

  describe '.call' do
    subject { described_class.call(customer_id:, payload:) }

    context 'with valid attributes' do
      it 'makes an API call to the correct endpoint' do
        request = stub_request(:put, "#{base_url}odata.svc/Customers(#{customer_id})")
                  .to_return(headers: { content_type: 'application/json' }, body: { d: customer_base_data }.to_json)

        expect { subject }.to_not raise_error
        expect(request).to have_been_requested
        expect(subject['id']).to eq(customer_id)
      end

      it 'includes the specified attributes in the request payload' do
        request = stub_request(:put, "#{base_url}odata.svc/Customers(#{customer_id})").with do |req|
          request_body = JSON.parse(req.body, symbolize_names: true)
          expect(request_body).to have_key(:attributes)
          expect(request_body).to have_key(:customer_id)

          payment_accounts = request_body.dig(:attributes, :PaymentAccounts)
          expect(payment_accounts).to have_key(:results)

          results = request_body.dig(:attributes, :PaymentAccounts, :results, 0)
          expect(results).to have_key(:CreditCard)
          expect(results).to have_key(:active)
          expect(results).to have_key(:isPrimary)
          expect(results).to have_key(:isSecondary)
          expect(results).to have_key(:type)

          card_attributes = request_body.dig(:attributes, :PaymentAccounts, :results, 0, :CreditCard)
          expect(card_attributes).to have_key(:token)
        end.to_return(headers: { content_type: 'application/json' }, body: { d: customer_base_data }.to_json)

        expect { subject }.to_not raise_error
        expect(request).to have_been_requested
      end
    end

    context 'when error->message->value is present in API response and status is 200' do
      let(:error) { "Resource not found for the segment 'Customers'" }

      before do
        stub_request(:put, "#{base_url}odata.svc/Customers(#{customer_id})")
          .to_return(headers: { content_type: 'application/json' }, body: {
            error: { message: { lang: 'en-US', value: error } }
          }.to_json)
      end

      it 'raises a LoanManagementSystemError' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, error)
      end
    end

    context 'when unexpectedly formatted error is present in API response and status is 200' do
      before do
        stub_request(:put, "#{base_url}odata.svc/Customers(#{customer_id})")
          .to_return(headers: { content_type: 'application/json' }, body: {
            error: { test: 'unexpected error' }
          }.to_json)
      end

      it 'raises a LoanManagementSystemError' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, /unexpected error/i)
      end
    end

    context 'when status is 4xx (ClientError) but no response body is present' do
      before do
        stub_request(:put, "#{base_url}odata.svc/Customers(#{customer_id})")
          .to_return(headers: { content_type: 'application/json' }, status: 403)
      end

      it 'raises a LoanManagementSystemError with status' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, /status 403/)
      end
    end
  end
end
