# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanProService::LoanManagementSystem::LoanStatusArchive do
  let(:loan_id) { Faker::Number.number(digits: 4) }
  let(:loan_sub_status_id) { 1 }
  let(:base_url) { LoanProService::ConfigHelper.configuration.loan_management_system_url }
  let(:charge_off_date) { '/Date(1673049600)/' }
  let(:charge_off_balance) { '1678.11' }
  let(:loan_pro_loan_data) do
    {
      'results' => [
        {
          'date' => charge_off_date,
          'loanStatusText' => 'Closed',
          'loanSubStatusText' => 'Closed - Charged Off',
          'netChargeOff' => charge_off_balance
        }
      ]
    }
  end
  let(:subject) { described_class.call(loan_id, loan_sub_status_id) }

  describe '.call' do
    context 'with valid loan_id and loan_sub_status_id' do
      it 'should make an API call to the correct endpoint' do
        request = stub_request(:get, "#{base_url}odata.svc/LoanStatusArchive?&$select=date,loanStatusText," \
          "loanSubStatusText,netChargeOff&$top=1&$orderby=date asc&$filter=loanId eq #{loan_id} and " \
          "loanSubStatusId eq #{loan_sub_status_id}")
                  .to_return(
                    headers: { content_type: 'application/json' },
                    body: { d: loan_pro_loan_data }.to_json
                  )
        expect { subject }.to_not raise_error
        expect(request).to have_been_requested
      end
    end

    context 'when error->message->value is present in API response and status is 200' do
      let(:error) { "Resource not found for the segment 'Customers'" }

      before do
        stub_request(:get, "#{base_url}odata.svc/LoanStatusArchive?&$select=date,loanStatusText," \
          "loanSubStatusText,netChargeOff&$top=1&$orderby=date asc&$filter=loanId eq #{loan_id} and " \
          "loanSubStatusId eq #{loan_sub_status_id}")
          .to_return(
            headers: { content_type: 'application/json' },
            body: { error: { message: { lang: 'en-US', value: error } } }.to_json
          )
      end

      it 'raises a LoanManagementSystemError' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, error)
      end
    end
  end
end
