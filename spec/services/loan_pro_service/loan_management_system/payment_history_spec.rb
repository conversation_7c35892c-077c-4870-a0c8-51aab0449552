# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanProService::LoanManagementSystem::PaymentHistory do
  let(:loan_id) { '7849' }
  let(:headers) { { content_type: 'application/json' } }
  let(:base_url) { LoanProService::ConfigHelper.configuration.loan_management_system_url }
  let(:payments_url) { "#{base_url}odata.svc/Loans(#{loan_id})/Payments?nopaging=true" }
  let(:transactions_url) { "#{base_url}odata.svc/Loans(#{loan_id})/Transactions?nopaging=true" }

  subject { described_class.call(loan_id:) }

  describe '.call', vcr: { cassette_name: 'LoanProService_LoanManagementSystem_PaymentHistory/_call/api_call' } do
    it 'should not produce an error' do
      expect { subject }.to_not raise_error
    end

    it 'should return 3 payments' do
      expect(subject.size).to eq(3)
    end

    it 'must contain matching payments and transactions' do
      index = subject.index_by { |payment| payment['id'] }

      expect(index[790_219]['transaction']['id']).to eq(11_452_324)
      expect(index[808_704]['transaction']['id']).to eq(12_278_431)
      expect(index[827_317]['transaction']['id']).to eq(13_120_259)
    end
  end

  context 'errors' do
    describe '409 Loan Not Found' do
      let(:loan_id) { '7849' }
      let(:message) { "Resource of type 'Loan' with id was not found." }
      let(:body) { { error: { message:, type: 'Exception', code: 0 } } }

      before { stub_request(:get, payments_url).to_return(status: 409, headers:, body: body.to_json) }

      it 'should extract error message and raise LoanManagementSystemError' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, message)
      end
    end

    describe '4xx client error' do
      before { stub_request(:get, payments_url).to_return(headers:, status: 403) }

      it 'should raise LoanManagementSystemError with status' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, /status 403/)
      end
    end
  end
end
