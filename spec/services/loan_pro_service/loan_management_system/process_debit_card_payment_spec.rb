# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanProService::LoanManagementSystem::ProcessDebitCardPayment do
  let(:base_url) { LoanProService::ConfigHelper.configuration.loan_management_system_url }
  let(:loan_id) { Faker::Number.number(digits: 5) }
  let(:date) { Date.today.iso8601 }
  let!(:title) { "Debit Card Payment on #{Date.today.iso8601.to_datetime}" }
  let(:debit_card_id) { Faker::Number.number(digits: 5) }
  let(:payload) do
    {
      attributes: {
        'Payments' => {
          'results' => [
            {
              'selectedProcessor' => 0,
              'paymentMethodId' => 3,
              'early' => 1,
              'echeckAuthType' => 'payment.echeckauth.WEB',
              'amount' => Faker::Number.decimal(l_digits: 2, r_digits: 2),
              'date' => date,
              'info' => "#{date} #{title}",
              'paymentTypeId' => 1,
              'active' => 1,
              'resetPastDue' => 0,
              'payoffPayment' => false,
              '_saveProfile' => 1,
              'extra' => 'payment.extra.tx.principal',
              'paymentAccountId' => debit_card_id,
              'chargeFeeType' => 'loan.cardfee.types.0',
              'chargeFeeAmount' => 0,
              'chargeFeePercentage' => 0
            }
          ]
        }
      },
      loan_id: loan_id,
      query: 'Loans'
    }
  end
  let(:process_new_payment_response) do
    {
      'id' => loan_id,
      'displayId' => loan_id.to_s,
      'title' => loan_id.to_s,
      'settingsId' => 8750,
      'setupId' => 9100,
      'collateralId' => 0,
      'linkedLoan' => 0,
      'modId' => 0,
      'modTotal' => 0,
      'created' => '/Date(**********)/',
      'lastMaintRun' => '/Date(**********)/',
      'createdBy' => 9678,
      'active' => 1,
      'archived' => 0,
      'temporaryAccount' => 0,
      'deleted' => 0
    }
  end

  describe '.call' do
    subject { described_class.call(loan_id:, payload:) }

    context 'with valid attributes' do
      it 'makes an API call to the correct endpoint' do
        request = stub_request(:put, "#{base_url}odata.svc/Loans(#{loan_id})")
                  .with(body: payload).to_return(headers: { content_type: 'application/json' }, body: { d: process_new_payment_response }.to_json)

        expect { subject }.to_not raise_error
        expect(request).to have_been_requested
        expect(subject).to eq(process_new_payment_response)
      end

      it 'includes the specified attributes in the request payload' do
        request = stub_request(:put, "#{base_url}odata.svc/Loans(#{loan_id})").with do |req|
          request_body = JSON.parse(req.body, symbolize_names: true)

          expect(request_body).to have_key(:attributes)
          expect(request_body).to have_key(:loan_id)

          payments = request_body.dig(:attributes, :Payments)
          expect(payments).to have_key(:results)

          results = request_body.dig(:attributes, :Payments, :results, 0)
          expect(results).to have_key(:info)
          expect(results).to have_key(:date)
          expect(results).to have_key(:amount)
          expect(results).to have_key(:paymentAccountId)
        end.to_return(headers: { content_type: 'application/json' }, body: { d: process_new_payment_response }.to_json)

        expect { subject }.to_not raise_error
        expect(request).to have_been_requested
      end
    end

    context 'when error->message->value is present in API response and status is 200' do
      let(:error) { "Resource not found for the segment 'Loans'" }

      it 'raises a LoanManagementSystemError with the error message' do
        request = stub_request(:put, "#{base_url}odata.svc/Loans(#{loan_id})")
                  .with(body: payload)
                  .to_return(headers: { content_type: 'application/json' }, body: { error: { message: { lang: 'en-US', value: error } } }
          .to_json)

        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, error)
        expect(request).to have_been_requested
      end
    end

    context 'when unexpectedly formatted error is present in API response and status is 200' do
      it 'raises a LoanManagementSystemError' do
        stub_request(:put, "#{base_url}odata.svc/Loans(#{loan_id})")
          .with(body: payload)
          .to_return(headers: { content_type: 'application/json' }, body: { error: { test: 'unexpected error' } }
          .to_json)

        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, /unexpected error/i)
      end
    end

    context 'when status is 4xx (ClientError) but no response body is present' do
      it 'raises a LoanManagementSystemError with status' do
        stub_request(:put, "#{base_url}odata.svc/Loans(#{loan_id})")
          .to_return(headers: { content_type: 'application/json' }, status: 403)

        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, /status 403/)
      end
    end
  end
end
