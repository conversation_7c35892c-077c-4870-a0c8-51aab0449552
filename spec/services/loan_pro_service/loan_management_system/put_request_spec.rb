# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanProService::LoanManagementSystem::PutRequest do
  let(:base_url) { LoanProService::ConfigHelper.configuration.loan_management_system_url }
  let(:loan_id) { Faker::Number.number(digits: 5) }
  let(:loan_query) { 'Loans' }
  let!(:customer_id) { Faker::Number.number(digits: 5) }
  let(:customer_query) { 'Customers' }
  let(:date) { Date.today.iso8601 }
  let!(:title) { "Debit Card Payment on #{Date.today.iso8601.to_datetime}" }
  let(:debit_card_id) { Faker::Number.number(digits: 5) }
  let(:payment_profile_token) { Faker::Alphanumeric.alphanumeric(number: 20) }
  let(:link_payment_profile_response) do
    {
      'id' => customer_id,
      'mcId' => 3_609_465,
      'status' => 'Active',
      'firstName' => 'John',
      'lastName' => 'Doe',
      'active' => 1,
      'created' => '/Date(**********)/',
      'lastUpdate' => '/Date(**********)/',
      'PaymentAccounts' => {
        'results' => [
          {
            '__metadata' => {
              'uri' => "http =>//loanpro.simnang.com/api/public/api/1/odata.svc/PaymentAccounts(id=#{debit_card_id})",
              'type' => 'Entity.PaymentAccount'
            },
            'id' => debit_card_id,
            'entityId' => customer_id,
            'entityType' => 'Entity.Customer',
            'isPrimary' => 1,
            'isSecondary' => 0,
            'title' => title,
            'type' => 'paymentAccount.type.credit',
            'creditCardId' => 46,
            'checkingAccountId' => 0,
            'active' => 1,
            'visible' => 1,
            'verify' => 0,
            'CheckingAccount' => { '__deferred' => { 'uri' => "PaymentAccounts(#{debit_card_id})/CheckingAccount" } },
            'CreditCard' => { '__deferred' => { 'uri' => "PaymentAccounts(#{debit_card_id})/CreditCard" } }
          }
        ]
      }
    }
  end
  let(:link_payment_profile_payload) do
    {
      attributes: {
        'PaymentAccounts' => {
          'results' => [
            {
              'CreditCard' => { 'token' => payment_profile_token.to_s },
              'active' => 1,
              'isPrimary' => 0,
              'isSecondary' => 0,
              'title' => title,
              'type' => 'paymentAccount.type.credit'
            }
          ]
        }
      },
      id: customer_id,
      query: 'Customers'
    }
  end
  let(:process_new_payment_payload) do
    {
      attributes: {
        'Payments' => {
          'results' => [
            {
              'selectedProcessor' => 0,
              'paymentMethodId' => 3,
              'early' => 1,
              'echeckAuthType' => 'payment.echeckauth.WEB',
              'amount' => Faker::Number.decimal(l_digits: 2, r_digits: 2),
              'date' => date,
              'info' => "#{date} #{title}",
              'paymentTypeId' => 1,
              'active' => 1,
              'resetPastDue' => 0,
              'payoffPayment' => false,
              '_saveProfile' => 1,
              'extra' => 'payment.extra.tx.principal',
              'paymentAccountId' => debit_card_id,
              'chargeFeeType' => 'loan.cardfee.types.0',
              'chargeFeeAmount' => 0,
              'chargeFeePercentage' => 0
            }
          ]
        }
      },
      id: loan_id,
      query: loan_query
    }
  end
  let(:process_new_payment_response) do
    {
      'id' => loan_id,
      'displayId' => loan_id.to_s,
      'title' => loan_id.to_s,
      'settingsId' => 8750,
      'setupId' => 9100,
      'collateralId' => 0,
      'linkedLoan' => 0,
      'modId' => 0,
      'modTotal' => 0,
      'created' => '/Date(**********)/',
      'lastMaintRun' => '/Date(**********)/',
      'createdBy' => 9678,
      'active' => 1,
      'archived' => 0,
      'temporaryAccount' => 0,
      'deleted' => 0
    }
  end

  describe '.call' do
    context 'with customers url' do
      subject { described_class.call(id: customer_id, payload: link_payment_profile_payload, query: customer_query) }
      it 'makes an API call with valid attributes' do
        request = stub_request(:put, "#{base_url}odata.svc/#{customer_query}(#{customer_id})")
                  .with(body: link_payment_profile_payload).to_return(headers: { content_type: 'application/json' }, body: { d: link_payment_profile_response }.to_json)

        expect { subject }.to_not raise_error
        expect(request).to have_been_requested
        expect(subject).to eq(link_payment_profile_response)
      end

      it 'includes the specified attributes in the request payload' do
        stub_request(:put, "#{base_url}odata.svc/#{customer_query}(#{customer_id})").with do |req|
          request_body = JSON.parse(req.body, symbolize_names: true)
          expect(request_body).to have_key(:attributes)
          expect(request_body).to have_key(:id)

          payments = request_body.dig(:attributes, :PaymentAccounts)
          expect(payments).to have_key(:results)

          results = request_body.dig(:attributes, :PaymentAccounts, :results, 0)
          expect(results).to have_key(:CreditCard)
          expect(results).to have_key(:active)
          expect(results).to have_key(:isPrimary)
          expect(results).to have_key(:isSecondary)
          expect(results).to have_key(:type)

          card_attributes = request_body.dig(:attributes, :PaymentAccounts, :results, 0, :CreditCard)
          expect(card_attributes).to have_key(:token)
        end.to_return(headers: { content_type: 'application/json' }, body: { d: link_payment_profile_response }.to_json)
      end
    end

    context 'with loans url' do
      subject { described_class.call(id: loan_id, payload: process_new_payment_payload, query: loan_query) }
      it 'makes an API call with valid attributes' do
        request = stub_request(:put, "#{base_url}odata.svc/#{loan_query}(#{loan_id})")
                  .with(body: process_new_payment_payload)
                  .to_return(headers: { content_type: 'application/json' }, body: { d: process_new_payment_response }.to_json)

        expect { subject }.to_not raise_error
        expect(request).to have_been_requested
        expect(subject).to eq(process_new_payment_response)
      end

      it 'includes the specified attributes in the request payload' do
        stub_request(:put, "#{base_url}odata.svc/#{loan_query}(#{loan_id})").with do |req|
          request_body = JSON.parse(req.body, symbolize_names: true)

          expect(request_body).to have_key(:attributes)
          expect(request_body).to have_key(:loan_id)

          payments = request_body.dig(:attributes, :Payments)
          expect(payments).to have_key(:results)

          results = request_body.dig(:attributes, :Payments, :results, 0)
          expect(results).to have_key(:info)
          expect(results).to have_key(:date)
          expect(results).to have_key(:amount)
          expect(results).to have_key(:paymentAccountId)
        end.to_return(headers: { content_type: 'application/json' }, body: { d: process_new_payment_response }.to_json)
      end
    end

    subject { described_class.call(id: loan_id, payload: process_new_payment_payload, query: loan_query) }
    let(:error) { "Resource not found for the segment 'Loans'" }

    context 'when error->message->value is present in API response and status is 200' do
      it 'raises a LoanManagementSystemError with the error message' do
        request = stub_request(:put, "#{base_url}odata.svc/#{loan_query}(#{loan_id})")
                  .with(body: process_new_payment_payload)
                  .to_return(headers: { content_type: 'application/json' }, body: { error: { message: { lang: 'en-US', value: error } } }
          .to_json)

        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, error)
        expect(request).to have_been_requested
      end
    end

    context 'when unexpectedly formatted error is present in API response and status is 200' do
      it 'raises a LoanManagementSystemError' do
        stub_request(:put, "#{base_url}odata.svc/#{loan_query}(#{loan_id})")
          .with(body: process_new_payment_payload)
          .to_return(headers: { content_type: 'application/json' }, body: { error: { test: 'unexpected error' } }
          .to_json)

        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, /unexpected error/i)
      end
    end

    context 'when status is 4xx (ClientError) but no response body is present' do
      it 'raises a LoanManagementSystemError with status' do
        stub_request(:put, "#{base_url}odata.svc/#{loan_query}(#{loan_id})")
          .to_return(headers: { content_type: 'application/json' }, status: 403)

        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, /status 403/)
      end
    end
  end
end
