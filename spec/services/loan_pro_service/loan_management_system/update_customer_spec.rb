# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanProService::LoanManagementSystem::UpdateCustomer do
  let(:customer_attributes) do
    {
      status: 'Active',
      customerType: 'customer.type.individual',
      customerIdType: 'customer.idType.ssn',
      birthDate: Faker::Date.birthday(min_age: 18, max_age: 65),
      firstName: Faker::Name.first_name,
      lastName: Faker::Name.last_name,
      ssn: Faker::IdNumber.valid,
      email: Faker::Internet.email,
      driverLicense: Faker::Alphanumeric.alphanumeric(number: 7),
      generationCode: 'customer.generationCode.none',
      gender: 'customer.gender.male'
    }
  end

  let(:base_url) { LoanProService::ConfigHelper.configuration.loan_management_system_url }
  let(:customer_id) { Faker::Number.number(digits: 5) }
  let(:customer_base_data) { { 'id' => customer_id } }

  describe '.call' do
    context 'with valid customer attributes' do
      subject { described_class.call(customer_id:, attributes: customer_attributes) }

      it 'makes an API call to the correct endpoint' do
        request = stub_request(:put, "#{base_url}odata.svc/Customers(#{customer_id})")
                  .to_return(headers: { content_type: 'application/json' }, body: { d: customer_base_data }.to_json)

        expect { subject }.to_not raise_error
        expect(request).to have_been_requested
        expect(subject['id']).to eq(customer_id)
      end

      it 'includes the specified attributes in the request payload' do
        request = stub_request(:put, "#{base_url}odata.svc/Customers(#{customer_id})").with do |req|
          request_body = JSON.parse(req.body, symbolize_names: true)

          expect(request_body).to have_key(:status)
          expect(request_body).to have_key(:customerType)
          expect(request_body).to have_key(:customerIdType)
          expect(request_body).to have_key(:birthDate)
          expect(request_body).to have_key(:firstName)
          expect(request_body).to have_key(:lastName)
          expect(request_body).to have_key(:ssn)
          expect(request_body).to have_key(:email)
          expect(request_body).to have_key(:driverLicense)
          expect(request_body).to have_key(:generationCode)
          expect(request_body).to have_key(:gender)
        end.to_return(headers: { content_type: 'application/json' }, body: { d: customer_base_data }.to_json)

        expect { subject }.to_not raise_error
        expect(request).to have_been_requested
      end
    end

    context 'when error->message->value is present in API response and status is 200' do
      let(:error) { "Resource not found for the segment 'Customers'" }

      before do
        stub_request(:put, "#{base_url}odata.svc/Customers(#{customer_id})")
          .to_return(headers: { content_type: 'application/json' }, body: {
            error: { message: { lang: 'en-US', value: error } }
          }.to_json)
      end

      subject { described_class.call(customer_id:, attributes: customer_attributes) }

      # because Faraday does not raise error on 200 status,
      # - this tests raising of error when 'error' is found in respose
      # - also tests correct error message extraction
      it 'raises a LoanManagementSystemError' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, error)
      end
    end

    context 'when unexpectedly formatted error is present in API response and status is 200' do
      before do
        stub_request(:put, "#{base_url}odata.svc/Customers(#{customer_id})")
          .to_return(headers: { content_type: 'application/json' }, body: {
            error: { test: 'unexpected error' }
          }.to_json)
      end

      subject { described_class.call(customer_id:, attributes: customer_attributes) }

      # because Faraday does not raise error on 200 status,
      # - this tests raising of error when 'error' is found in respose
      # - also tests correct error message extraction
      it 'raises a LoanManagementSystemError' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, /unexpected error/i)
      end
    end

    context 'when status is 4xx (ClientError) but no response body is present' do
      before do
        stub_request(:put, "#{base_url}odata.svc/Customers(#{customer_id})")
          .to_return(headers: { content_type: 'application/json' }, status: 403)
      end

      subject { described_class.call(customer_id:, attributes: customer_attributes) }

      it 'raises a LoanManagementSystemError with status' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, /status 403/)
      end
    end
  end
end
