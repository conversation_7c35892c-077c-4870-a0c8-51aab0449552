# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanProService::LoanManagementSystem::UpdateLoanSettings do
  let(:loan_settings_attributes) do
    {
      cardFeeAmount: Faker::Number.between(from: 2, to: 5)
    }
  end
  let(:custom_field_id) { Faker::Number.number(digits: 3) }
  let(:custom_field_value) { Faker::Alphanumeric.alpha }
  let(:base_url) { LoanProService::ConfigHelper.configuration.loan_management_system_url }
  let(:loan_id) { Faker::Number.number(digits: 5) }
  let(:loan_settings_id) { Faker::Number.number(digits: 5) }
  let(:loan_base_data) { { 'id' => loan_id, 'LoanSettings' => { 'id' => loan_settings_id } } }

  describe '.call' do
    context 'with valid loan settings and custom field attributes' do
      subject { described_class.call(loan_id:, loan_settings_id:, attributes: loan_settings_attributes, custom_fields: { custom_field_id => custom_field_value }) }

      it 'makes an API call to the correct endpoint' do
        request = stub_request(:put, "#{base_url}odata.svc/Loans(#{loan_id})")
                  .to_return(headers: { content_type: 'application/json' }, body: { d: loan_base_data }.to_json)

        expect { subject }.to_not raise_error
        expect(request).to have_been_requested
        expect(subject['id']).to eq(loan_id)
        expect(subject['LoanSettings']['id']).to eq(loan_settings_id)
      end

      it 'includes both the specified and default attributes in the request payload' do
        request = stub_request(:put, "#{base_url}odata.svc/Loans(#{loan_id})").with do |req|
          request_body = JSON.parse(req.body, symbolize_names: true)
          expect(request_body).to eq(
            {
              LoanSettings: {
                __id: loan_settings_id,
                __update: true,
                cardFeeAmount: loan_settings_attributes[:cardFeeAmount],
                CustomFieldValues: {
                  results: [
                    {
                      customFieldId: custom_field_id,
                      customFieldValue: custom_field_value
                    }
                  ]
                }
              }
            }
          )
        end.to_return(headers: { content_type: 'application/json' }, body: { d: loan_base_data }.to_json)

        expect { subject }.to_not raise_error
        expect(request).to have_been_requested
      end

      it 'does not permit the default operation attributes to be overridden' do
        loan_settings_attributes[:__id] = 987
        loan_settings_attributes[:__update] = false

        request = stub_request(:put, "#{base_url}odata.svc/Loans(#{loan_id})").with do |req|
          request_body = JSON.parse(req.body, symbolize_names: true)
          expect(request_body[:LoanSettings][:__id]).to eq(loan_settings_id)
          expect(request_body[:LoanSettings][:__update]).to eq(true)
        end.to_return(headers: { content_type: 'application/json' }, body: { d: loan_base_data }.to_json)

        expect { subject }.to_not raise_error
        expect(request).to have_been_requested
      end
    end

    context 'with valid loan settings, but no custom field attributes' do
      let(:loan_settings_attributes) { { autopayEnabled: 1 } }
      let(:request_body) { { LoanSettings: { __id: loan_settings_id, __update: true, **loan_settings_attributes } } }

      subject { described_class.call(loan_id:, loan_settings_id:, attributes: loan_settings_attributes) }

      it 'makes an API call to the correct endpoint' do
        request = stub_request(:put, "#{base_url}odata.svc/Loans(#{loan_id})")
                  .to_return(headers: { content_type: 'application/json' }, body: { d: loan_base_data }.to_json)

        expect { subject }.to_not raise_error
        expect(request).to have_been_requested
        expect(subject['id']).to eq(loan_id)
        expect(subject['LoanSettings']['id']).to eq(loan_settings_id)
      end

      it 'includes only the provided attributes' do
        request = stub_request(:put, "#{base_url}odata.svc/Loans(#{loan_id})").with do |req|
          expect(JSON.parse(req.body, symbolize_names: true)).to eq(request_body)
        end.to_return(headers: { content_type: 'application/json' }, body: { d: loan_base_data }.to_json)

        expect { subject }.to_not raise_error
        expect(request).to have_been_requested
      end
    end

    context 'when error->message->value is present in API response and status is 200' do
      let(:error) { "Resource not found for the segment 'Loans'" }

      before do
        stub_request(:put, "#{base_url}odata.svc/Loans(#{loan_id})")
          .to_return(headers: { content_type: 'application/json' }, body: {
            error: { message: { lang: 'en-US', value: error } }
          }.to_json)
      end

      subject { described_class.call(loan_id:, loan_settings_id:, attributes: loan_settings_attributes) }

      # because Faraday does not raise error on 200 status,
      # - this tests raising of error when 'error' is found in respose
      # - also tests correct error message extraction
      it 'raises a LoanManagementSystemError' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, error)
      end
    end

    context 'when unexpectedly formatted error is present in API response and status is 200' do
      before do
        stub_request(:put, "#{base_url}odata.svc/Loans(#{loan_id})")
          .to_return(headers: { content_type: 'application/json' }, body: {
            error: { test: 'unexpected error' }
          }.to_json)
      end

      subject { described_class.call(loan_id:, loan_settings_id:, attributes: loan_settings_attributes) }

      # because Faraday does not raise error on 200 status,
      # - this tests raising of error when 'error' is found in respose
      # - also tests correct error message extraction
      it 'raises a LoanManagementSystemError' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, /unexpected error/i)
      end
    end

    context 'when status is 4xx (ClientError) but no response body is present' do
      before do
        stub_request(:put, "#{base_url}odata.svc/Loans(#{loan_id})")
          .to_return(headers: { content_type: 'application/json' }, status: 403)
      end

      subject { described_class.call(loan_id:, loan_settings_id:, attributes: loan_settings_attributes) }

      it 'raises a LoanManagementSystemError with status' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, /status 403/)
      end
    end
  end
end
