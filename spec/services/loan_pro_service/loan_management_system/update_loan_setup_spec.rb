# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanProService::LoanManagementSystem::UpdateLoanSetup do
  let(:loan_setup_attributes) do
    {
      contractDate: DateTime.now.to_date.iso8601,
      firstPaymentDate: (DateTime.now.to_date + 30).iso8601,
      loanAmount: Faker::Number.between(from: 1000, to: 50_000),
      loanRate: Faker::Number.between(from: 5.0, to: 25.0),
      loanTerm: Faker::Number.between(from: 36, to: 72),
      underwriting: Faker::Number.between(from: 100, to: 2500)
    }
  end
  let(:base_url) { LoanProService::ConfigHelper.configuration.loan_management_system_url }
  let(:loan_id) { Faker::Number.number(digits: 5) }
  let(:loan_setup_id) { Faker::Number.number(digits: 5) }
  let(:loan_base_data) { { 'id' => loan_id, 'LoanSetup' => { 'id' => loan_setup_id } } }

  describe '.call' do
    context 'with valid loan attributes' do
      subject { described_class.call(loan_id:, loan_setup_id:, attributes: loan_setup_attributes) }

      it 'makes an API call to the correct endpoint' do
        request = stub_request(:put, "#{base_url}odata.svc/Loans(#{loan_id})")
                  .to_return(headers: { content_type: 'application/json' }, body: { d: loan_base_data }.to_json)

        expect { subject }.to_not raise_error
        expect(request).to have_been_requested
        expect(subject['id']).to eq(loan_id)
        expect(subject['LoanSetup']['id']).to eq(loan_setup_id)
      end

      it 'includes both the specified and default attributes in the request payload' do
        request = stub_request(:put, "#{base_url}odata.svc/Loans(#{loan_id})").with do |req|
          request_body = JSON.parse(req.body, symbolize_names: true)
          expect(request_body).to eq(
            {
              LoanSetup: {
                __id: loan_setup_id,
                __update: true,
                contractDate: loan_setup_attributes[:contractDate],
                firstPaymentDate: loan_setup_attributes[:firstPaymentDate],
                loanAmount: loan_setup_attributes[:loanAmount],
                loanRate: loan_setup_attributes[:loanRate],
                loanTerm: loan_setup_attributes[:loanTerm],
                underwriting: loan_setup_attributes[:underwriting]
              }
            }
          )
        end.to_return(headers: { content_type: 'application/json' }, body: { d: loan_base_data }.to_json)

        expect { subject }.to_not raise_error
        expect(request).to have_been_requested
      end

      it 'does not permit the default operation attributes to be overridden' do
        loan_setup_attributes[:__id] = 987
        loan_setup_attributes[:__update] = false

        request = stub_request(:put, "#{base_url}odata.svc/Loans(#{loan_id})").with do |req|
          request_body = JSON.parse(req.body, symbolize_names: true)
          expect(request_body[:LoanSetup][:__id]).to eq(loan_setup_id)
          expect(request_body[:LoanSetup][:__update]).to eq(true)
        end.to_return(headers: { content_type: 'application/json' }, body: { d: loan_base_data }.to_json)

        expect { subject }.to_not raise_error
        expect(request).to have_been_requested
      end
    end

    context 'when error->message->value is present in API response and status is 200' do
      let(:error) { "Resource not found for the segment 'Loans'" }

      before do
        stub_request(:put, "#{base_url}odata.svc/Loans(#{loan_id})")
          .to_return(headers: { content_type: 'application/json' }, body: {
            error: { message: { lang: 'en-US', value: error } }
          }.to_json)
      end

      subject { described_class.call(loan_id:, loan_setup_id:, attributes: loan_setup_attributes) }

      # because Faraday does not raise error on 200 status,
      # - this tests raising of error when 'error' is found in respose
      # - also tests correct error message extraction
      it 'raises a LoanManagementSystemError' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, error)
      end
    end

    context 'when unexpectedly formatted error is present in API response and status is 200' do
      before do
        stub_request(:put, "#{base_url}odata.svc/Loans(#{loan_id})")
          .to_return(headers: { content_type: 'application/json' }, body: {
            error: { test: 'unexpected error' }
          }.to_json)
      end

      subject { described_class.call(loan_id:, loan_setup_id:, attributes: loan_setup_attributes) }

      # because Faraday does not raise error on 200 status,
      # - this tests raising of error when 'error' is found in respose
      # - also tests correct error message extraction
      it 'raises a LoanManagementSystemError' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, /unexpected error/i)
      end
    end

    context 'when status is 4xx (ClientError) but no response body is present' do
      before do
        stub_request(:put, "#{base_url}odata.svc/Loans(#{loan_id})")
          .to_return(headers: { content_type: 'application/json' }, status: 403)
      end

      subject { described_class.call(loan_id:, loan_setup_id:, attributes: loan_setup_attributes) }

      it 'raises a LoanManagementSystemError with status' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, /status 403/)
      end
    end
  end
end
