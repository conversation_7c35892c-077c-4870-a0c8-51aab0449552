# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanProService::LoanManagementSystem::UpdateLoan do
  let(:attributes) do
    {
      cardFeeAmount: '123.45'
    }
  end

  let(:loan_id) { Faker::Number.number(digits: 5) }
  let(:base_url) { LoanProService::ConfigHelper.configuration.loan_management_system_url }

  describe('.call') do
    subject { described_class.call(loan_id:, attributes:) }

    context('with valid attributes') do
      it('makes an API request as expected') do
        request = stub_request(:put, "#{base_url}odata.svc/Loans(#{loan_id})").with(body: attributes)
                                                                              .to_return(headers: { content_type: 'application/json' }, body: { d: attributes }.to_json)

        expect { subject }.to_not raise_error
        expect(request).to have_been_requested
        expect(subject['cardFeeAmount']).to eq('123.45')
      end
    end

    context 'when loan<PERSON><PERSON> returns an error' do
      let(:error_body) do
        {
          error: {
            message: 'Loan update was rejected with message: Unknown error',
            type: 'PostResourceActionException',
            code: 500
          }
        }
      end

      it 'raises a LoanManagementSystemError with the error message' do
        request = stub_request(:put, "#{base_url}odata.svc/Loans(#{loan_id})")
                  .with(body: attributes)
                  .to_return(headers: { content_type: 'application/json' }, body: error_body.to_json, status: 409)

        expect do
          subject
        end.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, 'Loan update was rejected with message: Unknown error')
        expect(request).to have_been_requested
      end
    end
  end
end
