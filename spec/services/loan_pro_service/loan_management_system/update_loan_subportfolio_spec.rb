# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanProService::LoanManagementSystem::UpdateLoanSubportfolio do
  let(:loan_id) { Faker::Number.number(digits: 4) }
  let(:base_url) { LoanProService::ConfigHelper.configuration.loan_management_system_url }
  let(:investor_portfolio_id) { LoanProService::ConfigHelper.configuration.investor_portfolio_id }
  let(:investor_subportfolio_id) { Faker::Number.number(digits: 2) }
  let(:loan_base_data) do
    {
      'id' => loan_id
    }
  end

  let(:portfolios_data) do
    [{
      'id' => investor_portfolio_id,
      'title' => 'Investor',
      'active' => 1
    }]
  end

  let(:destroy) { false }

  let(:request_body) do
    {
      'Portfolios' => {
        results: [{
          '__id' => investor_portfolio_id,
          '__destroy' => destroy
        }]
      },
      'SubPortfolios' => {
        results: [{
          '__id' => investor_subportfolio_id,
          '__destroy' => destroy
        }]
      }
    }
  end

  describe '.call' do
    subject { described_class.call(loan_id:, subportfolio_id: investor_subportfolio_id) }

    context 'adding an investor to a loan' do
      before do
        stub_request(:put, "#{base_url}odata.svc/Loans(#{loan_id})")
          .with(body: request_body).to_return(headers: { content_type: 'application/json' }, body: { d: loan_base_data }.to_json)
      end

      context 'when investor portfolio is not present' do
        it 'should call CheckLoanInvestorPortfolio' do
          expect(LoanProService::LoanManagementSystem::CheckLoanInvestorPortfolio).to receive(:call).with(loan_id)
          subject
        end

        it 'should PUT correct API endpoint with portfolio & subportfolio ID' do
          allow(LoanProService::LoanManagementSystem::CheckLoanInvestorPortfolio).to receive(:call).with(loan_id).and_return(nil)
          expect { subject }.to_not raise_error
          expect(subject['id']).to eq(loan_id)
        end
      end

      context 'when investor portfolio is already present' do
        it 'should call CheckLoanInvestorPortfolio and raise error' do
          expect(LoanProService::LoanManagementSystem::CheckLoanInvestorPortfolio).to receive(:call)
            .with(loan_id).and_raise(LoanProService::Exceptions::LoanManagementSystemError, 'Investor portfolio already exists')
          expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, /portfolio already exists/)
        end
      end
    end

    context 'when PUT returns 4xx with error->message present' do
      let(:error) { 'Invalid destroy operation. Probably an invalid child id was provided.' }
      let(:destroy) { true }
      before do
        stub_request(:put, "#{base_url}odata.svc/Loans(#{loan_id})")
          .with(body: request_body).to_return(status: 409, headers: { content_type: 'application/json' }, body: {
            error: {
              message: error,
              type: 'EntityException',
              code: 0
            }
          }.to_json)
      end

      subject { described_class.call(loan_id:, subportfolio_id: investor_subportfolio_id, destroy: true, check_investor: false) }

      # because the base error for 4xx message is FaradayError,
      # - this tests rescue_from error conversion and error message extraction
      it 'should extract error message and raise LoanManagementSystemError' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, error)
      end
    end
  end
end
