# frozen_string_literal: true

require 'rails_helper'
RSpec.describe LoanProService::LoanManagementSystem::UploadDocument do
  let(:customer_id) { Faker::Number.number(digits: 5) }
  let(:section_id) { 2 }
  let(:file_name) { 'test_file.jpg' }
  let(:custom_file_name) { 'test_file_name.jpg' }
  let(:file) { fixture_file_upload('spec/fixtures/files/test_file.jpg', 'image/jpeg') }
  let(:upload_url) { 'https://example.com/upload' }
  let(:create_document_response) do
    { 'id' => 1, 'fileName' => file_name, 'uploadUrl' => upload_url, 'customFileName' => custom_file_name, 'mime' => 'image/jpeg' }
  end

  describe '.call' do
    subject { described_class.call(customer_id: customer_id, section_id: section_id, file_name: file_name, custom_file_name: custom_file_name, file: file) }

    before do
      allow(LoanProService::LoanManagementSystem::CreateDocument).to receive(:call).and_return(create_document_response)
      stub_request(:put, upload_url).to_return(status: 200)
    end

    it 'calls CreateDocument with correct parameters' do
      expect(LoanProService::LoanManagementSystem::CreateDocument).to receive(:call).with(
        customer_id: customer_id,
        section_id: section_id,
        file_name: file_name,
        custom_file_name: custom_file_name
      )
      subject
    end

    it 'uploads the file to the provided URL' do
      expect { subject }.to_not raise_error
      expect(a_request(:put, upload_url)).to have_been_made.once
    end

    context 'when uploadurl is nil' do
      before do
        allow(LoanProService::LoanManagementSystem::CreateDocument).to receive(:call).and_return({ 'uploadUrl' => nil })
      end

      it 'logs the error and raises LoanManagementSystemError' do
        expect(Rails.logger).to receive(:error).with('uploadUrl is missing', hash_including(customer_id: customer_id, file_name: file_name))
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, 'Upload URL is missing from the CreateDocument response')
      end
    end

    context 'when file upload fails' do
      before do
        allow(LoanProService::LoanManagementSystem::CreateDocument).to receive(:call).and_raise(Faraday::ClientError.new('Error', { status: 500, body: 'Internal Server Error' }))
      end

      it 'logs the error and raises it' do
        expect(Rails.logger).to receive(:error).with('UploadDocument - Failed to upload document', hash_including(customer_id: customer_id, file_name: file_name))
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError)
      end
    end
  end
end
