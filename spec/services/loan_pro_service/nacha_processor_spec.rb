# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanProService::NachaProcessor do
  let(:record) { "#{record_type_code}#{Faker::Number.number(digits: 93)}" }
  let(:subject) { described_class.process(record) }
  context 'when processing a header record' do
    let(:record_type_code) { '1' }
    it 'parses the record' do
      expect(subject[0][:record_type_code]).to eq(record_type_code)
    end
  end
  context 'when processing a batch header record' do
    let(:record_type_code) { '5' }
    it 'parses the record' do
      expect(subject[0][:record_type_code]).to eq(record_type_code)
    end
  end
  context 'when processing a header record' do
    let(:record_type_code) { '6' }
    it 'parses the record' do
      expect(subject[0][:record_type_code]).to eq(record_type_code)
    end
  end
end
