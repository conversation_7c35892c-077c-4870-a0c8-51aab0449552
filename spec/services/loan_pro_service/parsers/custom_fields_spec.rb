# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanProService::Parsers::CustomFields do
  let(:custom_field_values) do
    [
      { 'customFieldId' => 125, 'customFieldValue' => SecureRandom.uuid },
      { 'customFieldId' => 126, 'customFieldValue' => "abovelending-sandbox#{SecureRandom.uuid}" },
      { 'customFieldId' => 127, 'customFieldValue' => 'itemid1' },
      { 'customFieldId' => 128, 'customFieldValue' => Faker::Bank.account_number }
    ]
  end

  subject { described_class.call(custom_field_values:) }

  it 'parses custom fields into a structured hash' do
    expected_hash = {
      checking_account_entity_ids: custom_field_values[0]['customFieldValue'],
      plaid_tokens: custom_field_values[1]['customFieldValue'],
      plaid_item_ids: custom_field_values[2]['customFieldValue'],
      plaid_account_ids: custom_field_values[3]['customFieldValue']
    }

    expect(subject).to eq(expected_hash)
  end

  context 'when a field is missing from the configuration' do
    let(:custom_field_values) do
      [
        { 'customFieldId' => 125, 'customFieldValue' => SecureRandom.uuid },
        { 'customFieldId' => 999, 'customFieldValue' => SecureRandom.uuid }
      ]
    end

    it 'returns only the available matching fields' do
      expected_hash = {
        checking_account_entity_ids: custom_field_values[0]['customFieldValue']
      }

      results = subject
      expect(results.count).to eq(1)
      expect(results).to eq(expected_hash)
    end
  end
end
