# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanProService::PaymentProfileSystem::CreatePaymentProfile do
  let(:payment_attributes) do
    {
      "checking-account": {
        "accountholder_name": Faker::Name.name,
        "account_number": Faker::Bank.account_number,
        "routing_number": Faker::Bank.routing_number,
        "bank_name": Faker::Bank.name,
        "eft_branch_number": '',
        "eft_institution_number": '',
        "address": Faker::Address.street_address,
        "city": Faker::Address.city,
        "country": 'USA',
        "zipcode": Faker::Address.zip,
        "state": Faker::Address.state_abbr.upcase,
        "account_type": 'checking'
      }
    }
  end

  let(:base_url) { LoanProService::ConfigHelper.configuration.payment_profile_system_url }
  let(:profile_token) { Faker::Alphanumeric.alphanumeric(number: 64) }
  let(:profile_base_data) { { 'token' => profile_token } }

  describe '.call' do
    context 'with valid attributes' do
      before(:each) do
        obo_token = { 'token' => Faker::Alphanumeric.alphanumeric(number: 64) }

        stub_request(:post, "#{LoanProService::ConfigHelper.configuration.payment_profile_system_url}/users/obo-token")
          .to_return(headers: { content_type: 'application/json' }, body: obo_token.to_json)
      end

      subject { described_class.call(attributes: payment_attributes) }

      it 'makes an API call to the correct endpoint' do
        request = stub_request(:post, "#{base_url}/checking-account")
                  .to_return(headers: { content_type: 'application/json' }, body: profile_base_data.to_json)

        expect { subject }.to_not raise_error
        expect(request).to have_been_requested
        expect(subject['token']).to eq(profile_token)
      end

      context 'when status is 401 Unauthorized' do
        before do
          stub_request(:post, "#{base_url}/checking-account")
            .to_return(headers: { content_type: 'application/json' }, status: 401)
        end

        subject { described_class.call(attributes: payment_attributes) }

        it 'raises a LoanManagementSystemError with status' do
          expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, /status 401/)
        end
      end

      context 'when body contains error' do
        before do
          stub_request(:post, "#{base_url}/checking-account")
            .to_return(headers: { content_type: 'application/json' }, body: { 'error': 'Boom!' }.to_json)
        end

        subject { described_class.call(attributes: payment_attributes) }

        it 'raises a LoanManagementSystemError with status' do
          expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, 'Boom!')
        end
      end
    end
  end
end
