# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanProService::PostRequest do
  let(:body) { { some_content: 'content' } }
  let(:subject) { described_class.call(body, url) }
  let(:url) { Faker::Internet.url }

  describe '#call' do
    let(:conn) { double('conn') }
    let(:body) { JSON({ foo: 'bar' }) }
    let(:response) { double('response') }
    before do
      allow_any_instance_of(described_class).to receive(:conn).and_return(conn)
      allow(conn).to receive(:post).and_return(response)
      allow(response).to receive(:body).and_return(body)
    end

    it 'uses the gems configuration' do
      expect(described_class.new(body, url).config).to eq(LoanProService::ConfigHelper.configuration)
    end

    it 'makes an http call' do
      expect(conn).to receive(:post)
      subject
    end

    context 'when configuring the request' do
      let(:req) { double('req') }
      let(:options) { double('options') }
      before do
        allow_any_instance_of(described_class).to receive(:conn).and_return(conn)
        allow(conn).to receive(:post).and_return(response).and_yield(req)
      end

      it 'adds the body and timeout option' do
        expect(req).to receive(:body=)
        expect(req).to receive(:options).and_return(options)
        expect(options).to receive(:timeout=)
        subject
      end
    end
  end

  context 'when configuring' do
    let(:config) { double('config') }
    before do
      allow_any_instance_of(described_class).to receive(:config).and_return(config)
    end

    it 'loads the config values' do
      expect(config).to receive(:authorization)
      expect(config).to receive(:secret)
      described_class.new(body, url).conn
    end
  end
end
