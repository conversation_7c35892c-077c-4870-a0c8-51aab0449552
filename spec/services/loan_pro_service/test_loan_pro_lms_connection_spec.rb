# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanProService::TestLoanProLmsConnection do
  let(:base_url) { LoanProService::ConfigHelper.configuration.loan_management_system_url }
  let(:response) do
    { 'results' => [
      { 'id' => 'company.date.format',
        'label' => 'Date Formatting',
        'type' => 'STRING',
        'value' => 'MM/dd/yyyy',
        'group' => 'company.date',
        'modified' => '0',
        'modified_by' => '0',
        'sequence' => 0 }
    ] }
  end
  describe '#call' do
    before do
      stub_request(:get, "#{base_url}tenant.settings(company.date)")
        .to_return(headers: { content_type: 'application/json' }, body: { d: response }.to_json)
    end

    subject { described_class.call }

    it 'should make an API call to the correct endpoint' do
      expect { subject }.to_not raise_error
      expect(subject).to eq(response)
    end
  end

  context 'when error->message->value is present in API response and status is 200' do
    let(:error) { 'error' }
    before do
      stub_request(:get, "#{base_url}tenant.settings(company.date)")
        .to_return(headers: { content_type: 'application/json' }, body: {
          error: { message: { lang: 'en-US', value: error } }
        }.to_json)
    end

    subject { described_class.call }

    # because Faraday does not raise error on 200 status,
    # - this tests raising of error when 'error' is found in response
    # - also tests correct error message extraction
    it 'should raise LoanManagementSystemError' do
      expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, error)
    end
  end

  context 'when status is 401 Unauthorized' do
    before do
      stub_request(:get, "#{base_url}tenant.settings(company.date)")
        .to_return(headers: { content_type: 'application/json' }, status: 401)
    end

    subject { described_class.call }

    it 'raises a LoanManagementSystemError with status' do
      expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, /status 401/)
    end
  end

  context 'when unexpectedly formatted error is present in API response and status is 200' do
    before do
      stub_request(:get, "#{base_url}tenant.settings(company.date)")
        .to_return(headers: { content_type: 'application/json' }, body: {
          error: { test: 'unexpected error' }
        }.to_json)
    end

    subject { described_class.call }

    # because Faraday does not raise error on 200 status,
    # - this tests raising of error when 'error' is found in response
    # - also tests correct error message extraction
    it 'should raise LoanManagementSystemError' do
      expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, /unexpected error/i)
    end
  end

  context 'when status is 4xx (ClientError) but no response body is present' do
    before do
      stub_request(:get, "#{base_url}tenant.settings(company.date)")
        .to_return(headers: { content_type: 'application/json' }, status: 403)
    end
    subject { described_class.call }

    it 'should raise LoanManagementSystemError with status' do
      expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, /status 403/)
    end
  end
end
