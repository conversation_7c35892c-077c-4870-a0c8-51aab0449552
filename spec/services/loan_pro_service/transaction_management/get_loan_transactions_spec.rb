# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanProService::TransactionManagement::GetLoanTransactions do
  let(:loan_id) { Faker::Number.number(digits: 5) }
  let(:base_url) { LoanProService::ConfigHelper.configuration.loan_management_system_url }
  let(:loan_pro_loan_tx_response) do
    {
      'Transactions' =>
        [{ '__metadata' => { 'uri' => 'http://loanpro.simnang.com/api/public/api/1/odata.svc/LoanTransactions(id=123456789)', 'type' => 'Entity.LoanTransaction' },
           'id' => 2_632_350,
           'txId' => '15100-0-spm155',
           'entityType' => 'Entity.Loan',
           'entityId' => 15_100,
           'modId' => 0,
           'date' => '/Date(1845763200)/',
           'period' => 155,
           'periodStart' => '/Date(1844553600)/',
           'periodEnd' => '/Date(1845676800)/',
           'title' => 'Scheduled Payment: 156',
           'type' => 'scheduledPayment',
           'infoOnly' => 0,
           'infoDetails' => nil,
           'paymentId' => 0,
           'paymentDisplayId' => 0,
           'paymentAmount' => '0',
           'paymentInterest' => '0',
           'paymentPrincipal' => '0',
           'paymentDiscount' => '0',
           'paymentFees' => '0',
           'feesPaidDetails' => nil,
           'paymentEscrow' => '0',
           'paymentEscrowBreakdown' => nil,
           'chargeAmount' => '386.98',
           'chargeInterest' => '3.92',
           'chargePrincipal' => '383.06',
           'chargeDiscount' => '0',
           'chargeFees' => '0',
           'chargeEscrow' => '0',
           'chargeEscrowBreakdown' => '{"subsets":{"2":0,"3":0,"4":0,"5":0}}',
           'future' => 1,
           'principalOnly' => 0,
           'advancement' => 0,
           'payoffFee' => 0,
           'chargeOff' => 0,
           'paymentType' => 0,
           'adbDays' => 14,
           'adb' => '383.06',
           'principalBalance' => '383.06',
           'displayOrder' => '0' },
         { '__metadata' => { 'uri' => 'http://loanpro.simnang.com/api/public/api/1/odata.svc/LoanTransactions(id=123456789)', 'type' => 'Entity.LoanTransaction' },
           'id' => 123_456_789,
           'txId' => '15100-0-fpm155',
           'entityType' => 'Entity.Loan',
           'entityId' => 15_100,
           'modId' => 0,
           'date' => '/Date(1845763200)/',
           'period' => 155,
           'periodStart' => '/Date(1844553600)/',
           'periodEnd' => '/Date(1845676800)/',
           'title' => 'Forecasted Payment: 156',
           'type' => 'forecastedPayment',
           'infoOnly' => 0,
           'infoDetails' => nil,
           'paymentId' => 0,
           'paymentDisplayId' => 0,
           'paymentAmount' => '386.98',
           'paymentInterest' => '3.92',
           'paymentPrincipal' => '383.06',
           'paymentDiscount' => '0',
           'paymentFees' => '0',
           'feesPaidDetails' => nil,
           'paymentEscrow' => '0',
           'paymentEscrowBreakdown' => '{"subsets":{"2":0,"3":0,"4":0,"5":0}}',
           'chargeAmount' => '0',
           'chargeInterest' => '0',
           'chargePrincipal' => '0',
           'chargeDiscount' => '0',
           'chargeFees' => '0',
           'chargeEscrow' => '0',
           'chargeEscrowBreakdown' => nil,
           'future' => 1,
           'principalOnly' => 0,
           'advancement' => 0,
           'payoffFee' => 0,
           'chargeOff' => 0,
           'paymentType' => 0,
           'adbDays' => 14,
           'adb' => '383.06',
           'principalBalance' => '0',
           'displayOrder' => '9' }]
    }
  end
  subject { described_class.call(loan_id) }

  describe '.call' do
    context 'with valid loan_id' do
      before do
        stub_request(:get, "#{base_url}odata.svc/Loans(#{loan_id})?$expand=Transactions&nopaging=true")
          .to_return(headers: { content_type: 'application/json' }, body: { d: loan_pro_loan_tx_response }.to_json)
      end

      subject { described_class.call(loan_id) }

      it 'should make an API call to the correct endpoint' do
        expect { subject }.to_not raise_error
        expect(subject).to eq(loan_pro_loan_tx_response['Transactions'])
      end
    end

    context 'when error->message->value is present in API response and status is 200' do
      let(:error) { "Resource not found for the segment 'Loans'" }
      before do
        stub_request(:get, "#{base_url}odata.svc/Loans(#{loan_id})?$expand=Transactions&nopaging=true")
          .to_return(headers: { content_type: 'application/json' }, body: {
            error: { message: { lang: 'en-US', value: error } }
          }.to_json)
      end

      it 'raises LoanManagementSystemError' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, error)
      end
    end

    context 'when body contains an error' do
      before do
        stub_request(:get, "#{base_url}odata.svc/Loans(#{loan_id})?$expand=Transactions&nopaging=true")
          .to_return(headers: { content_type: 'application/json' }, body: { 'error': 'Boom!' }.to_json)
      end

      it 'raises a LoanManagementSystemError with status' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, 'Boom!')
      end
    end

    context 'when unexpectedly formatted error is present in API response and status is 200' do
      before do
        stub_request(:get, "#{base_url}odata.svc/Loans(#{loan_id})?$expand=Transactions&nopaging=true")
          .to_return(headers: { content_type: 'application/json' }, body: {
            error: { test: 'unexpected error' }
          }.to_json)
      end

      it 'raises LoanManagementSystemError' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, /unexpected error/i)
      end
    end

    context 'when status is 4xx (ClientError) but no response body is present' do
      before do
        stub_request(:get, "#{base_url}odata.svc/Loans(#{loan_id})?$expand=Transactions&nopaging=true")
          .to_return(headers: { content_type: 'application/json' }, status: 403)
      end

      it 'raises a LoanManagementSystemError with status and logs the error' do
        expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, /status 403/)
      end
    end
  end
end
