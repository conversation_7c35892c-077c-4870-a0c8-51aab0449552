# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanProService::TransactionManagement::UpdateTransactionStatus do
  let(:transaction_id) { Faker::Alphanumeric.alphanumeric }
  let(:base_url) { LoanProService::ConfigHelper.configuration.payment_profile_system_url }
  let(:request_body) do
    {
      "transaction": {
        "message": 'Transaction Failed',
        "status": 'FAILED',
        "reason_code": 'R01'
      }
    }
  end

  describe '.call' do
    context 'with valid attributes' do
      subject { described_class.call(transaction_id:, attributes: request_body) }

      it 'makes an API call to the correct endpoint' do
        request = stub_request(:put, "#{base_url}/transactions/#{transaction_id}")
                  .with(body: request_body)
                  .to_return(headers: { content_type: 'application/json' }, body: request_body.to_json)

        expect { subject }.to_not raise_error
        expect(request).to have_been_requested
      end

      context 'when status is 401 Unauthorized' do
        before do
          stub_request(:put, "#{base_url}/transactions/#{transaction_id}")
            .to_return(headers: { content_type: 'application/json' }, status: 401)
        end

        it 'raises a LoanManagementSystemError with status' do
          expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, /status 401/)
        end
      end
      context 'when body contains an error' do
        before do
          stub_request(:put, "#{base_url}/transactions/#{transaction_id}")
            .to_return(headers: { content_type: 'application/json' }, body: { 'error': 'Boom!' }.to_json)
        end

        it 'raises a LoanManagementSystemError with status' do
          expect { subject }.to raise_error(LoanProService::Exceptions::LoanManagementSystemError, 'Boom!')
        end
      end
    end
  end
end
