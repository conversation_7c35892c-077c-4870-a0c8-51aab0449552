# frozen_string_literal: true

require 'rails_helper'

class TestIncluder
  include LoanProService::Utilities
end

RSpec.describe LoanProService::Utilities do
  let(:body) { { some_content: 'content' } }
  let(:subject) { described_class.call(body, url) }
  let(:url) { Faker::Internet.url }

  describe '.present?' do
    it 'returns true for nil values' do
      expect(TestIncluder.new.present?(nil)).to eq(false)
    end

    it 'returns true for empty string values' do
      expect(TestIncluder.new.present?('')).to eq(false)
    end

    it 'returns true for empty array values' do
      expect(TestIncluder.new.present?([])).to eq(false)
    end

    it 'returns true for empty hash values' do
      expect(TestIncluder.new.present?({})).to eq(false)
    end

    it 'returns false for false boolean values' do
      expect(TestIncluder.new.present?(false)).to eq(false)
      expect(TestIncluder.new.present?('abc' == 123)).to eq(false)
    end

    it 'returns false for integer values' do
      expect(TestIncluder.new.present?(1)).to eq(true)
      expect(TestIncluder.new.present?(0)).to eq(true)
      expect(TestIncluder.new.present?(-1)).to eq(true)
      expect(TestIncluder.new.present?(0 / 0.0)).to eq(true) # NaN
    end

    it 'returns false for non-empty string values' do
      expect(TestIncluder.new.present?('test')).to eq(true)
      expect(TestIncluder.new.present?(' ')).to eq(true)
      expect(TestIncluder.new.present?("\n")).to eq(true)
    end

    it 'returns false for non-empty array values' do
      expect(TestIncluder.new.present?([''])).to eq(true)
      expect(TestIncluder.new.present?([nil])).to eq(true)
      expect(TestIncluder.new.present?([{}])).to eq(true)
      expect(TestIncluder.new.present?([[]])).to eq(true)
    end

    it 'returns false for non-empty hash values' do
      expect(TestIncluder.new.present?({ nil => nil })).to eq(true)
      expect(TestIncluder.new.present?({ '' => false })).to eq(true)
      expect(TestIncluder.new.present?({ test: nil })).to eq(true)
    end

    it 'returns false for true boolean values' do
      expect(TestIncluder.new.present?(true)).to eq(true)
      expect(TestIncluder.new.present?(2 - 1 == 1)).to eq(true)
    end

    it 'returns false for object values' do
      expect(TestIncluder.new.present?(Object.new)).to eq(true)
      expect(TestIncluder.new.present?(StandardError.new)).to eq(true)
    end
  end

  describe '.safe_dig' do
    it 'returns the string value at the deepest specified key' do
      expected_string = 'This is a test'
      hash = { test: { foo: { bar: expected_string } } }
      keys = %i[test foo bar]
      expect(TestIncluder.new.safe_dig(hash, keys)).to eq(expected_string)
    end

    it 'returns the value at the deepest matching key' do
      expected_string = 'This is a test'
      hash = { test: { foo: { other: expected_string } } }
      keys = %i[test foo bar]
      expect(TestIncluder.new.safe_dig(hash, keys)).to eq({ other: expected_string })
    end

    it 'does not permit symbols and strings to be used interchangeably' do
      expected_string = 'This is a test'
      hash = { test: { 'foo' => { bar: expected_string } } }

      expect(TestIncluder.new.safe_dig(hash, %i[test foo bar])).to eq({ 'foo' => { bar: expected_string } })
      expect(TestIncluder.new.safe_dig(hash, %w[test foo bar])).to be_nil
      expect(TestIncluder.new.safe_dig(hash, [:test, 'foo', :bar])).to eq(expected_string)
    end

    it 'gracefully handles when an intermediate value is not a hash nor nil' do
      expected_string = 'This is a test'
      hash = { test: { foo: expected_string } }
      keys = %i[test foo bar]
      expect(TestIncluder.new.safe_dig(hash, keys)).to eq(expected_string)
    end
  end
end
