# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanproOnboarding::AutopayResolver do
  let(:first_payment_date) { DateTime.new(2022, 6, 5) }
  let(:loanpro_loan_data) do
    {
      'LoanSetup' => {
        'firstPaymentDate' => "/Date(#{first_payment_date.to_i})/",
        'paymentFrequency' => 'loan.frequency.monthly',
        'tilPaymentSchedule' => "[{\"count\":23,\"payment\":605.**************,\"startDate\":\"11\/21\/2022\"}, {\"count\":1,\"payment\":606,\"startDate\":\"03\/21\/2023\"}]"
      }
    }
  end
  let(:loanpro_customer_data) do
    LoanproOnboarding::OrchestrateOnboarding::LoanproCustomerData.new(
      0o000, # customer_id
      1234, # personal_payment_profile_id
      5678 # cft_payment_profile_id
    )
  end

  let!(:loan) { create(:above_lending_loan, :loanpro) }
  let!(:bank_account) { create(:above_lending_bank_account, account_type: 'checking', loan: loan) }

  describe '.call' do
    it 'delegates to the autopay serializer with correct attributes' do
      expect(LoanproOnboarding::Serializers::OnboardingAutopay).to receive(:call).with(
        first_payment_date: Date.new(2022, 6, 5),
        is_bank_day_scheduling: true,
        number_of_payments: 29,
        payment_amount: 605.**************,
        payment_method_account_type: 'bankacct.type.checking',
        payment_profile_id: 1234,
        payment_type: 1,
        process_date: Date.new(2022, 6, 4),
        recurring_frequency: 'autopay.recurringFrequency.monthly'
      )

      LoanproOnboarding::AutopayResolver.call(loanpro_loan_data: loanpro_loan_data, loanpro_customer_data: loanpro_customer_data, bank_account: bank_account)
    end

    it 'will us a savings account for autopay' do
      bank_account.update!(account_type: 'savings')

      expect(LoanproOnboarding::Serializers::OnboardingAutopay).to receive(:call).with(
        first_payment_date: Date.new(2022, 6, 5),
        is_bank_day_scheduling: true,
        number_of_payments: 29,
        payment_amount: 605.**************,
        payment_method_account_type: 'bankacct.type.savings',
        payment_profile_id: 1234,
        payment_type: 1,
        process_date: Date.new(2022, 6, 4),
        recurring_frequency: 'autopay.recurringFrequency.monthly'
      )

      LoanproOnboarding::AutopayResolver.call(loanpro_loan_data: loanpro_loan_data, loanpro_customer_data: loanpro_customer_data, bank_account: bank_account)
    end

    it 'sets the recurring payment frequency to match the payment frequency assigned to the loan' do
      payment_frequency, recurring_frequency = LoanProHelpers::LOAN_PRO_PAYMENT_TO_RECURRING_FREQUENCIES.to_a.sample
      loanpro_loan_data['LoanSetup']['paymentFrequency'] = payment_frequency

      expect(LoanproOnboarding::Serializers::OnboardingAutopay).to receive(:call).with(
        first_payment_date: Date.new(2022, 6, 5),
        is_bank_day_scheduling: true,
        number_of_payments: 29,
        payment_amount: 605.**************,
        payment_method_account_type: 'bankacct.type.checking',
        payment_profile_id: 1234,
        payment_type: 1,
        process_date: Date.new(2022, 6, 4),
        recurring_frequency: recurring_frequency
      )

      LoanproOnboarding::AutopayResolver.call(loanpro_loan_data: loanpro_loan_data, loanpro_customer_data: loanpro_customer_data, bank_account: bank_account)
    end
  end
end
