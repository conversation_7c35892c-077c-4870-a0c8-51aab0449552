# frozen_string_literal: true

require 'rails_helper'
require 'support/shared/onboard_loan_common'

RSpec.describe LoanproOnboarding::OrchestrateOnboarding do
  let(:unified_id) { Faker::Number.number(digits: 8).to_s }
  let(:identity_id) { SecureRandom.uuid }
  let(:loanpro_customer_id) { 12_345_678 }
  let(:primary_payment_account_id) { 1111 }
  let(:secondary_payment_account_id) { 2222 }

  let!(:initial_status) { create(:above_lending_loan_app_status, name: 'APPROVED') }
  let!(:onboarded_status) { create(:above_lending_loan_app_status, name: 'ONBOARDED') }

  let!(:til_history) { create(:til_history, loan:) }
  let!(:loan) { create(:above_lending_loan, :loanpro, unified_id: unified_id, loan_app_status_id: initial_status.id) }
  let!(:loanpro_loan_id) { loan.loan_pro_loan_entity.id }
  let!(:offer) { create(:above_lending_offer, loan: loan, settlement_amount: 1000, cashout_amount: 100, originating_party: 'DIRECT_LICENSES') }
  let!(:borrower) { create(:borrower, identity_id:) }
  let!(:above_lending_borrower) { create(:above_lending_borrower, identity_id:) }
  let!(:borrower_additional_info) { create(:borrower_additional_info, borrower: above_lending_borrower) }
  let!(:bank_account) { create(:above_lending_bank_account, borrower: above_lending_borrower, loan: loan, fund_transfer_authorize: true) }
  let!(:cft_account) do
    ExecuteLoanproOnboarding::CftBankAccount.new(
      'checking',
      'cft_bank_name',
      'cft_account_holder_name',
      nil,
      'cft_routing_number',
      'cft_account_number'
    )
  end

  let(:contract_date) { DateTime.new(2022, 6, 1) }
  let(:first_payment_date) { DateTime.new(2022, 6, 5) }
  let(:loanpro_loan_data) do
    {
      'id' => loan.loan_pro_loan_entity.id,
      'LoanSetup' => {
        'contractDate' => "/Date(#{contract_date.to_i})/",
        'firstPaymentDate' => "/Date(#{first_payment_date.to_i})/",
        'paymentFrequency' => 'loan.frequency.monthly',
        'tilPaymentSchedule' => "[{\"count\":23,\"payment\":605.**************,\"startDate\":\"11\/21\/2022\"}, {\"count\":1,\"payment\":606,\"startDate\":\"03\/21\/2023\"}]",
        'underwriting' => 432.1
      }
    }
  end

  describe '#perform' do
    before do
      allow(ActiveSupport::Notifications).to receive(:instrument)

      allow(LoanProService::LoanManagementSystem::ActivateLoan).to receive(:call)
      allow(LoanProService::LoanManagementSystem::AssignCustomerToLoan).to receive(:call)
      allow(LoanProService::LoanManagementSystem::CreateAutopay).to receive(:call)
      allow(LoanProService::PaymentProfileSystem::CreatePaymentProfile).to receive(:call).and_wrap_original do |_m, *args|
        if args.first.dig(:attributes, :'checking-account', :bank_name) == 'cft_bank_name'
          { 'token' => 'loanpro-cft-token' }
        else
          { 'token' => 'loanpro-personal-token' }
        end
      end
      allow(LoanProService::LoanManagementSystem::CreateCustomer).to receive(:call).and_return('id' => loanpro_customer_id)
      allow(LoanProService::LoanManagementSystem::CreateLoanFundingTransaction).to receive(:call)
      allow(LoanProService::LoanManagementSystem::GetLoan).to receive(:call).and_return(loanpro_loan_data)
      allow(LoanProService::LoanManagementSystem::UpdateCustomer).to receive(:call).and_wrap_original do |_m, *args|
        customer_id = args.first[:customer_id]

        # Simulate that LoanPro inserts payment account records into its remote databsae
        args.first.dig(:attributes, :PaymentAccounts, :results).each do |result|
          LoanPro::PaymentAccountEntity.create!(
            id: result[:isPrimary] == 1 ? primary_payment_account_id : secondary_payment_account_id,
            entity_id: customer_id,
            is_primary: result[:isPrimary],
            is_secondary: result[:isSecondary],
            active: true,
            checking_account_entity: LoanPro::CheckingAccountEntity.create!
          )
        end
      end
      allow(LoanProService::LoanManagementSystem::UpdateLoan).to receive(:call)
    end

    # Orchestrating in LoanPro involves quite a few calls which would make parsing one test kinda hard.
    # Instead we'll separate out each "onboarding" behavior in a separate test in this context.
    # Note the use of "allow to receive" and "expect to have received."
    context 'when orchestrating loan onboarding' do
      before do
        described_class.call(
          borrower: above_lending_borrower,
          borrower_additional_info: borrower_additional_info,
          bank_account: bank_account,
          cft_account: cft_account,
          loan: loan,
          offer: offer
        )
      end

      it 'requests details of the loanpro loan' do
        expect(LoanProService::LoanManagementSystem::GetLoan).to have_received(:call).with(loanpro_loan_id, expand: 'LoanSetup')
      end

      it 'creates a customer account' do
        customer_attributes = hash_including(
          ssn: above_lending_borrower.ssn
        )
        expect(LoanProService::LoanManagementSystem::CreateCustomer).to have_received(:call).once.with(attributes: customer_attributes)
      end

      it 'persists a local reference to the loanpro customer in AboveLending DB' do
        expect(AboveLending::LoanproCustomer.exists?(borrower_id: above_lending_borrower.id, loanpro_customer_id: loanpro_customer_id)).to eq(true)
      end

      it 'persists a local reference to the loanpro customer in Dash DB' do
        expect(Borrower.where(identity_id: above_lending_borrower.identity_id)
                       .where("platform_info->>'external_borrower_id' = ?", loanpro_customer_id.to_s)
                       .exists?)
          .to eq(true)
      end

      it 'creates payment profiles for the borrower and cft accounts' do
        personal_bank_account = hash_including(account_number: bank_account.account_number)
        expect(LoanProService::PaymentProfileSystem::CreatePaymentProfile).to have_received(:call).once.with(attributes: { 'checking-account': personal_bank_account })

        cft_bank_account = hash_including(account_number: cft_account.account_number)
        expect(LoanProService::PaymentProfileSystem::CreatePaymentProfile).to have_received(:call).once.with(attributes: { 'checking-account': cft_bank_account })
      end

      it 'ensures payment accounts exist for the customer' do
        assignment_attributes = {
          PaymentAccounts: {
            results: array_including(
              hash_including(
                CheckingAccount: {
                  accountType: 'bankacct.type.checking',
                  token: 'loanpro-personal-token'
                },
                active: 1,
                isPrimary: 1
              ),
              hash_including(
                CheckingAccount: {
                  accountType: 'bankacct.type.checking',
                  token: 'loanpro-cft-token'
                },
                active: 1,
                isPrimary: 0
              )
            )
          }
        }
        expect(LoanProService::LoanManagementSystem::UpdateCustomer).to have_received(:call).with(customer_id: loanpro_customer_id, attributes: assignment_attributes)
      end

      it 'associates the customer to the loan' do
        assignment_params = {
          attributes: {
            Customers: {
              results: [{ __id: loanpro_customer_id, __setLoanRole: 'loan.customerRole.primary' }]
            },
            __id: loanpro_loan_id,
            __update: true,
            id: loanpro_loan_id
          },
          loan_id: loanpro_loan_id
        }

        expect(LoanProService::LoanManagementSystem::AssignCustomerToLoan).to have_received(:call).with(assignment_params)
      end

      it 'activates the loan' do
        expect(LoanProService::LoanManagementSystem::ActivateLoan).to have_received(:call).with(loan_id: loanpro_loan_id)
      end

      it 'adds portfolios and subportfolios to the loan' do
        loan_update_params = {
          loan_id: loanpro_loan_id,
          attributes: {
            'Portfolios' => {
              results: array_including(hash_including({ '__id' => anything }))
            },
            'SubPortfolios' => {
              results: array_including(hash_including({ '__id' => anything }))
            }
          }
        }
        expect(LoanProService::LoanManagementSystem::UpdateLoan).to have_received(:call).once.with(loan_update_params)
      end

      it 'sets up an autopay for a loan' do
        autopay_payload = {
          attributes: hash_including(
            PaymentType: {
              __metadata: {
                type: 'Entity.CustomPaymentType',
                uri: '/api/1/odata.svc/CustomPaymentTypes(id=1)'
              }
            },
            PrimaryPaymentMethod: {
              __metadata: {
                type: 'Entity.PaymentAccount',
                uri: "http:# loanpro.simnang.com/api/public/api/1/odata.svc/PaymentAccounts(id=#{primary_payment_account_id})"
              },
              id: primary_payment_account_id,
              type: 'paymentAccount.type.checking'
            },
            amount: 605.4,
            applyDate: '2022-06-05',
            baProcessor: '87',
            lastDayOfMonthEnabled: false,
            paymentMethodAccountType: 'bankacct.type.checking',
            processDate: '2022-06-04',
            processDateCondition: 'bankingDays',
            processDateTime: '2022-06-04 19:00:00',
            recurringFrequency: 'autopay.recurringFrequency.monthly',
            recurringPeriods: 29,
            schedulingType: 'autopay.schedulingType.bankingDayPrior',
            type: 'autopay.type.recurring'
          ),
          loan_id: loanpro_loan_id
        }
        expect(LoanProService::LoanManagementSystem::CreateAutopay).to have_received(:call).once.with(autopay_payload)
      end

      it 'updates the local loan reference status to onaboarded' do
        expect(loan.reload.loan_app_status.name).to eq('ONBOARDED')
      end

      it 'updates the local latest_til_history of the loan' do
        expect(til_history.reload.til_data['itemization']['prepaidFinanceCharge']).to eq("$#{format('%.2f', loanpro_loan_data['LoanSetup']['underwriting'])}")
      end
    end

    context 'with alternative steps' do
      it 'does not create a personal funding transaction when loan has no cash back' do
        offer.update!(cashout_amount: 0)

        described_class.call(
          borrower: above_lending_borrower,
          borrower_additional_info: borrower_additional_info,
          bank_account: bank_account,
          cft_account: cft_account,
          loan: loan,
          offer: offer
        )

        cft_funding_transaction = { attributes: hash_including(paymentAccountId: secondary_payment_account_id) }
        expect(LoanProService::LoanManagementSystem::CreateLoanFundingTransaction).to have_received(:call).once.with(cft_funding_transaction)

        personal_funding_transaction = { attributes: hash_including(paymentAccountId: primary_payment_account_id) }
        expect(LoanProService::LoanManagementSystem::CreateLoanFundingTransaction).not_to have_received(:call).with(personal_funding_transaction)
      end

      it 'does not create an autopay when not enabled' do
        bank_account.update!(fund_transfer_authorize: false)

        described_class.call(
          borrower: above_lending_borrower,
          borrower_additional_info: borrower_additional_info,
          bank_account: bank_account,
          cft_account: cft_account,
          loan: loan,
          offer: offer
        )

        expect(LoanProService::LoanManagementSystem::CreateAutopay).not_to have_received(:call)
      end
    end

    context 'for direct license loans' do
      it 'creates cft and personal funding transactions' do
        offer.update!(originating_party: 'DIRECT_LICENSES')

        described_class.call(
          borrower: above_lending_borrower,
          borrower_additional_info: borrower_additional_info,
          bank_account: bank_account,
          cft_account: cft_account,
          loan: loan,
          offer: offer
        )

        cft_funding_transaction = { attributes: hash_including(loanId: loanpro_loan_id, amount: 1000, categoryId: 2, customerId: loanpro_customer_id,
                                                               paymentAccountId: secondary_payment_account_id) }
        expect(LoanProService::LoanManagementSystem::CreateLoanFundingTransaction).to have_received(:call).once.with(cft_funding_transaction)

        personal_funding_transaction = { attributes: hash_including(loanId: loanpro_loan_id, amount: 100, categoryId: 1, customerId: loanpro_customer_id,
                                                                    paymentAccountId: primary_payment_account_id) }
        expect(LoanProService::LoanManagementSystem::CreateLoanFundingTransaction).to have_received(:call).once.with(personal_funding_transaction)
      end
    end

    context 'for CRB loans' do
      it 'does NOT create cft and personal funding transactions' do
        offer.update!(originating_party: AboveLending::Offer::CRB_ORIGINATING_PARTY)

        described_class.call(
          borrower: above_lending_borrower,
          borrower_additional_info: borrower_additional_info,
          bank_account: bank_account,
          cft_account: cft_account,
          loan: loan,
          offer: offer
        )

        expect(LoanProService::LoanManagementSystem::CreateLoanFundingTransaction).not_to have_received(:call)
      end
    end

    context 'when previous data exists' do
      it 'does not recreate customer when already created' do
        AboveLending::LoanproCustomer.create!(id: SecureRandom.uuid, borrower_id: above_lending_borrower.id, loanpro_customer_id: loanpro_customer_id)

        described_class.call(
          borrower: above_lending_borrower,
          borrower_additional_info: borrower_additional_info,
          bank_account: bank_account,
          cft_account: cft_account,
          loan: loan,
          offer: offer
        )

        # Checking `.count` on LoanproCustomer relation causes malformed SQL for some reason, so we'll stick to .size.
        expect(AboveLending::LoanproCustomer.where(borrower_id: above_lending_borrower.id).size).to eq(1)
        expect(LoanProService::LoanManagementSystem::CreateCustomer).not_to have_received(:call)
      end

      it 'does not recreate a personal payment account when already created' do
        LoanPro::PaymentAccountEntity.create!(
          id: primary_payment_account_id,
          entity_id: loanpro_customer_id,
          is_primary: true,
          is_secondary: false,
          active: true,
          checking_account_entity: LoanPro::CheckingAccountEntity.create!
        )

        described_class.call(
          borrower: above_lending_borrower,
          borrower_additional_info: borrower_additional_info,
          bank_account: bank_account,
          cft_account: cft_account,
          loan: loan,
          offer: offer
        )

        assignment_attributes = {
          PaymentAccounts: {
            results: array_including(
              hash_including(
                CheckingAccount: {
                  accountType: 'bankacct.type.checking',
                  token: 'loanpro-cft-token'
                },
                active: 1,
                isPrimary: 0
              )
            )
          }
        }

        personal_bank_account = hash_including(account_number: bank_account.account_number)
        expect(LoanProService::PaymentProfileSystem::CreatePaymentProfile).not_to have_received(:call).with(attributes: { 'checking-account': personal_bank_account })

        cft_bank_account = hash_including(account_number: cft_account.account_number)
        expect(LoanProService::PaymentProfileSystem::CreatePaymentProfile).to have_received(:call).once.with(attributes: { 'checking-account': cft_bank_account })

        expect(LoanProService::LoanManagementSystem::UpdateCustomer).to have_received(:call).with(customer_id: loanpro_customer_id, attributes: assignment_attributes)
      end

      it 'does not recreate a cft payment account when already created' do
        LoanPro::PaymentAccountEntity.create!(
          id: secondary_payment_account_id,
          entity_id: loanpro_customer_id,
          is_primary: false,
          is_secondary: true,
          active: true,
          checking_account_entity: LoanPro::CheckingAccountEntity.create!
        )

        described_class.call(
          borrower: above_lending_borrower,
          borrower_additional_info: borrower_additional_info,
          bank_account: bank_account,
          cft_account: cft_account,
          loan: loan,
          offer: offer
        )

        assignment_attributes = {
          PaymentAccounts: {
            results: array_including(
              hash_including(
                CheckingAccount: {
                  accountType: 'bankacct.type.checking',
                  token: 'loanpro-personal-token'
                },
                active: 1,
                isPrimary: 1
              )
            )
          }
        }

        personal_bank_account = hash_including(account_number: bank_account.account_number)
        expect(LoanProService::PaymentProfileSystem::CreatePaymentProfile).to have_received(:call).once.with(attributes: { 'checking-account': personal_bank_account })

        cft_bank_account = hash_including(account_number: cft_account.account_number)
        expect(LoanProService::PaymentProfileSystem::CreatePaymentProfile).not_to have_received(:call).with(attributes: { 'checking-account': cft_bank_account })

        expect(LoanProService::LoanManagementSystem::UpdateCustomer).to have_received(:call).with(customer_id: loanpro_customer_id, attributes: assignment_attributes)
      end

      it 'does not update payment accounts when both have already been created' do
        LoanPro::PaymentAccountEntity.create!(
          id: primary_payment_account_id,
          entity_id: loanpro_customer_id,
          is_primary: true,
          is_secondary: false,
          active: true,
          checking_account_entity: LoanPro::CheckingAccountEntity.create!
        )
        LoanPro::PaymentAccountEntity.create!(
          id: secondary_payment_account_id,
          entity_id: loanpro_customer_id,
          is_primary: false,
          is_secondary: true,
          active: true,
          checking_account_entity: LoanPro::CheckingAccountEntity.create!
        )

        described_class.call(
          borrower: above_lending_borrower,
          borrower_additional_info: borrower_additional_info,
          bank_account: bank_account,
          cft_account: cft_account,
          loan: loan,
          offer: offer
        )

        expect(LoanProService::PaymentProfileSystem::CreatePaymentProfile).not_to have_received(:call)
        expect(LoanProService::LoanManagementSystem::UpdateCustomer).not_to have_received(:call)
      end

      it 'does not create a cft funding transaction when it has already been initiated' do
        LoanPro::LoanFundingTransactionEntity.create!(loan_id: loanpro_loan_id, category_id: 2)

        described_class.call(
          borrower: above_lending_borrower,
          borrower_additional_info: borrower_additional_info,
          bank_account: bank_account,
          cft_account: cft_account,
          loan: loan,
          offer: offer
        )

        cft_funding_transaction = { attributes: hash_including(paymentAccountId: secondary_payment_account_id) }
        expect(LoanProService::LoanManagementSystem::CreateLoanFundingTransaction).not_to have_received(:call).with(cft_funding_transaction)

        personal_funding_transaction = { attributes: hash_including(paymentAccountId: primary_payment_account_id) }
        expect(LoanProService::LoanManagementSystem::CreateLoanFundingTransaction).to have_received(:call).with(personal_funding_transaction)
      end

      it 'does not create a personal funding transaction when it has already been initiated' do
        LoanPro::LoanFundingTransactionEntity.create!(loan_id: loanpro_loan_id, category_id: 1)

        described_class.call(
          borrower: above_lending_borrower,
          borrower_additional_info: borrower_additional_info,
          bank_account: bank_account,
          cft_account: cft_account,
          loan: loan,
          offer: offer
        )

        cft_funding_transaction = { attributes: hash_including(paymentAccountId: secondary_payment_account_id) }
        expect(LoanProService::LoanManagementSystem::CreateLoanFundingTransaction).to have_received(:call).with(cft_funding_transaction)

        personal_funding_transaction = { attributes: hash_including(paymentAccountId: primary_payment_account_id) }
        expect(LoanProService::LoanManagementSystem::CreateLoanFundingTransaction).not_to have_received(:call).with(personal_funding_transaction)
      end

      it 'does not create an an autopay when one already exists' do
        LoanPro::LoanAutopayEntity.create!(loan_id: loanpro_loan_id)

        described_class.call(
          borrower: above_lending_borrower,
          borrower_additional_info: borrower_additional_info,
          bank_account: bank_account,
          cft_account: cft_account,
          loan: loan,
          offer: offer
        )

        expect(LoanProService::LoanManagementSystem::CreateAutopay).not_to have_received(:call)
      end
    end
  end
end
