# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanproOnboarding::PortfolioResolver do
  let(:unified_id) { Faker::Number.number(digits: 8).to_s }
  let!(:initial_status) { create(:above_lending_loan_app_status, name: 'APPROVED') }
  let!(:state_subportfolio) do
    create(
      :sub_portfolio_entity,
      parent: LoanPro::SubPortfolioEntity::PARENT_IDS[:state],
      title: 'CA'
    )
  end

  shared_examples 'includes common portfolios' do
    it 'includes originator and state portfolios' do
      expect(resolved[:portfolios]).to include(
        LoanPro::SubPortfolioEntity::PARENT_IDS[:originator],
        LoanPro::SubPortfolioEntity::PARENT_IDS[:state]
      )
    end

    it 'includes correct state subportfolio' do
      expect(resolved[:subportfolios]).to include(state_subportfolio.id)
    end
  end

  shared_examples 'ownership subportfolio' do |ownership|
    it "includes #{ownership.downcase} subportfolio" do
      expected_ownership = ownership.downcase.to_sym
      expect(resolved[:subportfolios]).to include(LoanPro::SubPortfolioEntity::SUB_PORTFOLIO_IDS[expected_ownership])
    end

    it 'excludes the other ownership subportfolio' do
      excluded_ownership = ownership == 'CRB' ? :direct_licenses : :crb
      expect(resolved[:subportfolios]).not_to include(LoanPro::SubPortfolioEntity::SUB_PORTFOLIO_IDS[excluded_ownership])
    end
  end

  shared_examples 'autopay subportfolio' do |enabled|
    it "includes #{enabled ? 'autopay_enabled' : 'manualpay_enabled'} subportfolio" do
      subportfolio_key = enabled ? :autopay_enabled : :manualpay_enabled
      expect(resolved[:subportfolios]).to include(LoanPro::SubPortfolioEntity::SUB_PORTFOLIO_IDS[subportfolio_key])
    end

    it "excludes #{enabled ? 'manualpay_enabled' : 'autopay_enabled'} subportfolio" do
      subportfolio_key = enabled ? :manualpay_enabled : :autopay_enabled
      expect(resolved[:subportfolios]).not_to include(LoanPro::SubPortfolioEntity::SUB_PORTFOLIO_IDS[subportfolio_key])
    end
  end

  describe 'DM product' do
    let(:loan) { create(:above_lending_loan, :loanpro, product_type: 'DM', unified_id: unified_id, loan_app_status_id: initial_status.id) }

    context 'with direct licenses and autopay enabled' do
      let(:resolved) do
        described_class.call(
          autopay_enabled: true,
          borrower_state: 'CA',
          loan: loan,
          origination_party: 'DIRECT_LICENSES'
        )
      end

      it_behaves_like 'includes common portfolios'

      it 'includes channel portfolio' do
        expect(resolved[:portfolios]).to include(LoanPro::SubPortfolioEntity::PARENT_IDS[:channel])
      end

      it 'includes dm subportfolio' do
        expect(resolved[:subportfolios]).to include(LoanPro::SubPortfolioEntity::SUB_PORTFOLIO_IDS[:dm])
      end

      it_behaves_like 'ownership subportfolio', 'DIRECT_LICENSES'
      it_behaves_like 'autopay subportfolio', true
    end

    context 'with CRB and autopay disabled' do
      let(:resolved) do
        described_class.call(
          autopay_enabled: false,
          borrower_state: 'CA',
          loan: loan,
          origination_party: 'CRB'
        )
      end

      it_behaves_like 'includes common portfolios'

      it 'includes channel portfolio' do
        expect(resolved[:portfolios]).to include(LoanPro::SubPortfolioEntity::PARENT_IDS[:channel])
      end

      it 'includes dm subportfolio' do
        expect(resolved[:subportfolios]).to include(LoanPro::SubPortfolioEntity::SUB_PORTFOLIO_IDS[:dm])
      end

      it_behaves_like 'ownership subportfolio', 'CRB'
      it_behaves_like 'autopay subportfolio', false
    end
  end

  describe 'IPL product' do
    let(:loan) { create(:above_lending_loan, :loanpro, product_type: 'IPL', unified_id: unified_id, loan_app_status_id: initial_status.id) }

    let(:resolved) do
      described_class.call(
        autopay_enabled: true,
        borrower_state: 'CA',
        loan: loan,
        origination_party: 'CRB'
      )
    end

    it_behaves_like 'includes common portfolios'

    it 'does not include channel portfolio' do
      expect(resolved[:portfolios]).not_to include(LoanPro::SubPortfolioEntity::PARENT_IDS[:channel])
    end

    it_behaves_like 'ownership subportfolio', 'CRB'
    it_behaves_like 'autopay subportfolio', true
  end

  describe 'UPL product' do
    let(:loan) { create(:above_lending_loan, :loanpro, product_type: 'UPL', unified_id: unified_id, loan_app_status_id: initial_status.id) }

    let(:resolved) do
      described_class.call(
        autopay_enabled: true,
        borrower_state: 'CA',
        loan: loan,
        origination_party: 'DIRECT_LICENSES'
      )
    end

    it_behaves_like 'includes common portfolios'

    it 'does not include channel portfolio' do
      expect(resolved[:portfolios]).not_to include(LoanPro::SubPortfolioEntity::PARENT_IDS[:channel])
    end

    it 'includes upl subportfolio' do
      expect(resolved[:subportfolios]).to include(LoanPro::SubPortfolioEntity::SUB_PORTFOLIO_IDS[:upl])
    end

    it_behaves_like 'ownership subportfolio', 'DIRECT_LICENSES'
    it_behaves_like 'autopay subportfolio', true
  end

  describe 'Unknown product type' do
    let(:loan) { create(:above_lending_loan, :loanpro, product_type: 'XYZ', unified_id: unified_id, loan_app_status_id: initial_status.id) }

    it 'logs an error and returns common portfolios only' do
      expect(Rails.logger).to receive(:error).with(/unknown product type/i)
      resolved = described_class.call(
        autopay_enabled: true,
        borrower_state: 'CA',
        loan: loan,
        origination_party: 'CRB'
      )

      expect(resolved[:portfolios]).to include(
        LoanPro::SubPortfolioEntity::PARENT_IDS[:originator],
        LoanPro::SubPortfolioEntity::PARENT_IDS[:state]
      )
    end
  end
end
