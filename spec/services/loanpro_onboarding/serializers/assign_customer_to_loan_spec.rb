# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanproOnboarding::Serializers::AssignCustomerToLoan do
  context '.call' do
    let(:loanpro_loan_id) { 'some-loanpro-loan-id' }
    let(:loanpro_customer_id) { 'some-loanpro-customer-id' }

    it 'serializes the payment profile payload' do
      payload = described_class.call(loanpro_loan_id: loanpro_loan_id, loanpro_customer_id: loanpro_customer_id)

      expect(payload).to eq(
        {
          id: loanpro_loan_id,
          Customers: {
            results: [
              {
                __id: loanpro_customer_id,
                __setLoanRole: 'loan.customerRole.primary'
              }
            ]
          },
          __update: true,
          __id: loanpro_loan_id
        }
      )
    end
  end
end
