# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanproOnboarding::Serializers::AssignPaymentProfilesToCustomer do
  context '.call' do
    let(:borrower_account_type) { 'checking' }
    let(:unified_id) { 'test-unified-id' }
    let(:personal_account_token) { 'test-personal-account-token' }
    let(:cft_account_token) { 'test-cft-account-token' }

    it 'serializes the payment profile assignment payload' do
      payload = described_class.call(
        borrower_account_type: borrower_account_type,
        unified_id: unified_id,
        personal_account_token: personal_account_token,
        cft_account_token: cft_account_token
      )

      expect(payload).to eq(
        {
          "PaymentAccounts": {
            "results": [{
              CheckingAccount: {
                accountType: "bankacct.type.#{borrower_account_type}",
                token: personal_account_token
              },
              active: 1,
              isPrimary: 1,
              isSecondary: 0,
              title: "Personal Account #{unified_id}",
              type: 'paymentAccount.type.checking'
            }, {
              CheckingAccount: {
                accountType: 'bankacct.type.checking',
                token: cft_account_token
              },
              active: 1,
              isPrimary: 0,
              isSecondary: 1, # CFT funding account is always the secondary account
              title: "CFT Account #{unified_id}",
              type: 'paymentAccount.type.checking'
            }]
          }
        }
      )
    end

    it 'does not include cft account when no token is provided' do
      payload = described_class.call(
        borrower_account_type: borrower_account_type,
        unified_id: unified_id,
        personal_account_token: personal_account_token,
        cft_account_token: nil
      )

      expect(payload).to eq(
        {
          "PaymentAccounts": {
            "results": [{
              CheckingAccount: {
                accountType: "bankacct.type.#{borrower_account_type}",
                token: personal_account_token
              },
              active: 1,
              isPrimary: 1,
              isSecondary: 0,
              title: "Personal Account #{unified_id}",
              type: 'paymentAccount.type.checking'
            }]
          }
        }
      )
    end

    it 'does not include personal account when no token is provided' do
      payload = described_class.call(
        borrower_account_type: borrower_account_type,
        unified_id: unified_id,
        personal_account_token: nil,
        cft_account_token: cft_account_token
      )

      expect(payload).to eq(
        {
          "PaymentAccounts": {
            "results": [{
              CheckingAccount: {
                accountType: 'bankacct.type.checking',
                token: cft_account_token
              },
              active: 1,
              isPrimary: 0,
              isSecondary: 1, # CFT funding account is always the secondary account
              title: "CFT Account #{unified_id}",
              type: 'paymentAccount.type.checking'
            }]
          }
        }
      )
    end
  end
end
