# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanproOnboarding::Serializers::Customer do
  context '.call' do
    let(:borrower) do
      create(
        :above_lending_borrower,
        date_of_birth: Date.new(2000, 10, 10),
        first_name: '<PERSON><PERSON>',
        last_name: '<PERSON><PERSON><PERSON>',
        ssn: '12345678',
        email: '<EMAIL>'
      )
    end

    let(:borrower_additional_info) do
      create(
        :borrower_additional_info,
        phone_number: '5554443333',
        address_street: '123 spooky street',
        zip_code: '12345',
        city: 'Nightvale',
        state: 'yy'
      )
    end

    it 'serializes the payment profile payload' do
      payload = described_class.call(borrower: borrower, borrower_additional_info: borrower_additional_info)

      expect(payload).to eq(
        {
          birthDate: '2000-10-10',
          firstName: 'Jorm',
          lastName: 'Nightengale',
          ssn: '12345678',
          email: '<EMAIL>',
          Phones: {
            results: [
              {
                __ignoreWarnings: true,
                phone: '5554443333',
                isPrimary: '1',
                isSecondary: '0',
                delete: false,
                _index: 0,
                type: 'customer.phoneType.cell',
                __isDirty: false,
                carrierVerified: 1,
                __lookupInProgress: true,
                carrierName: '',
                isLandLine: 0
              }
            ]
          },
          PrimaryAddress: {
            address1: '123 spooky street',
            zipcode: '12345',
            city: 'Nightvale',
            state: 'geo.state.YY'
          },
          MailAddress: {
            address1: '123 spooky street',
            zipcode: '12345',
            city: 'Nightvale',
            state: 'geo.state.YY'
          }
        }
      )
    end
  end
end
