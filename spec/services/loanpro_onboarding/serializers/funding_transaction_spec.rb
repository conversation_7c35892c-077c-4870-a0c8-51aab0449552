# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanproOnboarding::Serializers::FundingTransaction do
  context '.call' do
    let(:amount) { 1234.56 }
    let(:category_id) { 1 }
    let(:contract_date) { '2022-01-01' }
    let(:loanpro_customer_id) { 'some-customer-id' }
    let(:loanpro_loan_id) { 'some-loanpro-loan-id' }
    let(:payment_account_id) { 'some-payment-account-id' }

    it 'serializes the funding transaction payload' do
      payload = described_class.call(
        amount: amount,
        category_id: category_id,
        contract_date: contract_date,
        loanpro_customer_id: loanpro_customer_id,
        loanpro_loan_id: loanpro_loan_id,
        payment_account_id: payment_account_id
      )

      expect(payload).to eq(
        {
          loanId: loanpro_loan_id,
          customerId: loanpro_customer_id,
          sourceCompanyId: nil,
          merchantProcessorGroupId: 1,
          date: contract_date,
          amount: amount,
          categoryId: category_id,
          merchantTxProcessorId: '',
          method: 'loan.funding.method.deposit',
          authorizationType: 'loan.funding.auth.ppd',
          paymentAccountId: payment_account_id
        }
      )
    end
  end
end
