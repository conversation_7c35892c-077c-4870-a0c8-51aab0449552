# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanproOnboarding::Serializers::OnboardingAutopay do
  context '.call' do
    let(:first_payment_date) { Date.new(2022, 0o6, 25) }
    let(:is_bank_day_scheduling) { true }
    let(:number_of_payments) { 5 }
    let(:payment_amount) { '125.53' }
    let(:payment_method_account_type) { 'bankacct.type.checking' }
    let(:payment_profile_id) { 12_345 }
    let(:payment_type) { 1 }
    let(:process_date) { Date.new(2022, 0o6, 15) }
    let(:recurring_frequency) { 'autopay.recurringFrequency.monthly' }

    it 'serializes the autopay payload' do
      payload = described_class.call(
        first_payment_date: first_payment_date,
        is_bank_day_scheduling: is_bank_day_scheduling,
        number_of_payments: number_of_payments,
        payment_amount: payment_amount,
        payment_method_account_type: payment_method_account_type,
        payment_profile_id: payment_profile_id,
        payment_type: payment_type,
        process_date: process_date,
        recurring_frequency: recurring_frequency
      )

      expect(payload).to eq(
        {
          name: 'Scheduled Payment',
          type: 'autopay.type.recurring',
          paymentExtraTowards: 'payment.extra.tx.principal',
          amountType: 'autopay.amountType.static',
          amount: '125.53',
          paymentType: 1,
          chargeServiceFee: '0',
          processCurrent: 1,
          retryDays: 0,
          processTime: 19,
          postPaymentUpdate: 1,
          applyDate: '2022-06-25',
          processDate: '2022-06-15',
          methodType: 'autopay.methodType.echeck',
          recurringFrequency: 'autopay.recurringFrequency.monthly',
          recurringDateOption: 'autopay.recurringDate.applyDate',
          daysInPeriod: '',
          schedulingType: 'autopay.schedulingType.bankingDayPrior',
          processDateCondition: 'bankingDays',
          payoffAdjustment: 1,
          chargeOffRecovery: 0,
          paymentMethodAuthType: 'payment.echeckauth.PPD',
          paymentMethodAccountType: 'bankacct.type.checking',
          processZeroOrNegativeBalance: 0,
          lastDayOfMonthEnabled: false,
          PrimaryPaymentMethod: {
            __metadata: {
              uri: 'http:# loanpro.simnang.com/api/public/api/1/odata.svc/PaymentAccounts(id=12345)',
              type: 'Entity.PaymentAccount'
            },
            id: 12_345,
            type: 'paymentAccount.type.checking'
          },
          recurringPeriods: 5,
          baProcessor: '87',
          processDateTime: '2022-06-15 19:00:00',
          PaymentType: {
            __metadata: {
              type: 'Entity.CustomPaymentType',
              uri: '/api/1/odata.svc/CustomPaymentTypes(id=1)'
            }
          }
        }
      )
    end

    context 'without bank day scheduling' do
      it 'schedules for calendar days' do
        payload = described_class.call(
          first_payment_date: first_payment_date,
          is_bank_day_scheduling: false,
          number_of_payments: number_of_payments,
          payment_amount: payment_amount,
          payment_method_account_type: payment_method_account_type,
          payment_profile_id: payment_profile_id,
          payment_type: payment_type,
          process_date: process_date,
          recurring_frequency: recurring_frequency
        )

        expect(payload[:schedulingType]).to eq('autopay.schedulingType.calendarDay')
        expect(payload[:processDateCondition]).to eq('calendarDays')
      end
    end

    context 'for payment at the end of the month' do
      it 'enables last day of month when frequency is monthly' do
        payload = described_class.call(
          first_payment_date: Date.new(2022, 0o6, 30),
          is_bank_day_scheduling: is_bank_day_scheduling,
          number_of_payments: number_of_payments,
          payment_amount: payment_amount,
          payment_method_account_type: payment_method_account_type,
          payment_profile_id: payment_profile_id,
          payment_type: payment_type,
          process_date: process_date,
          recurring_frequency: recurring_frequency
        )

        expect(payload[:lastDayOfMonthEnabled]).to eq(true)
      end

      it 'does not enable last day of month when frequency is not monthly' do
        payload = described_class.call(
          first_payment_date: Date.new(2022, 0o6, 30),
          is_bank_day_scheduling: is_bank_day_scheduling,
          number_of_payments: number_of_payments,
          payment_amount: payment_amount,
          payment_method_account_type: payment_method_account_type,
          payment_profile_id: payment_profile_id,
          payment_type: payment_type,
          process_date: process_date,
          recurring_frequency: 'autopay.recurringFrequency.biWeekly'
        )

        expect(payload[:lastDayOfMonthEnabled]).to eq(false)
      end
    end
  end
end
