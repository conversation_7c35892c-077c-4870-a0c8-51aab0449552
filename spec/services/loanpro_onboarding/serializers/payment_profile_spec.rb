# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanproOnboarding::Serializers::PaymentProfile do
  context '.call' do
    let(:borrower_additional_info) do
      create(
        :borrower_additional_info,
        address_street: '123 spooky street',
        zip_code: '12345',
        city: 'Nightvale',
        state: 'yy'
      )
    end
    let!(:routing_number) { Faker::Bank.routing_number }
    let!(:account_number) { Faker::Bank.account_number }

    let(:bank_account) do
      create(
        :above_lending_bank_account,
        :loan,
        account_type: 'Checking',
        bank: 'free-money bank',
        holder_firstname: '<PERSON>rm',
        holder_lastname: 'Nightengale',
        routing_number: routing_number,
        account_number: account_number
      )
    end

    it 'serializes the payment profile payload' do
      payload = described_class.call(borrower_additional_info: borrower_additional_info, bank_account: bank_account)

      expect(payload).to eq(
        {
          'checking-account': {
            address: '123 spooky street',
            account_type: 'checking', # This has been downcased
            zipcode: '12345',
            city: 'Nightvale',
            state: 'YY',
            bank_name: 'free-money bank',
            accountholder_name: '<PERSON><PERSON> Nightengale',
            routing_number: routing_number,
            account_number: account_number,
            country: 'USA'
          }
        }
      )
    end

    it 'does not include blank space when given only one account holder name' do
      bank_account.update!(holder_firstname: nil)

      payload = described_class.call(borrower_additional_info: borrower_additional_info, bank_account: bank_account)

      expect(payload.dig(:'checking-account', :accountholder_name)).to eq('Nightengale')
    end
  end
end
