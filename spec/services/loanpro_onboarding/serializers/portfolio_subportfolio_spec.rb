# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanproOnboarding::Serializers::PortfolioSubportfolio do
  context '.call' do
    let(:portfolios) { [2, 4, 70] }
    let(:subportfolios) { [5, 122] }
    it 'serializes the portfolio/subportfolio payload' do
      payload = described_class.call(
        portfolios: portfolios,
        subportfolios: subportfolios
      )

      expect(payload).to eq(
        {
          'Portfolios' => {
            results: [{ '__id' => 2 }, { '__id' => 4 }, { '__id' => 70 }]
          },
          'SubPortfolios' => {
            results: [{ '__id' => 5 }, { '__id' => 122 }]
          }
        }
      )
    end

    it 'does not include portfolios in payload when none are given' do
      payload = described_class.call(
        portfolios: [],
        subportfolios: subportfolios
      )

      expect(payload).to eq(
        {
          'SubPortfolios' => {
            results: [{ '__id' => 5 }, { '__id' => 122 }]
          }
        }
      )
    end

    it 'does not include subportfolios in payload when none are given' do
      payload = described_class.call(
        portfolios: portfolios,
        subportfolios: []
      )

      expect(payload).to eq(
        {
          'Portfolios' => {
            results: [{ '__id' => 2 }, { '__id' => 4 }, { '__id' => 70 }]
          }
        }
      )
    end
  end
end
