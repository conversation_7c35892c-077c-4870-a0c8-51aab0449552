# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LookupS3ObjectSize do
  let(:key) { 'test.pdf' }
  let(:bucket) { 'test-bucket' }

  let(:head_object_response) { instance_double(Aws::S3::Types::HeadObjectOutput, content_length: 12_345) }
  let(:s3_client) { instance_double(Aws::S3::Client, head_object: head_object_response) }

  before do
    allow(Aws::S3::Client).to receive(:new).and_return(s3_client)
  end

  describe '.call' do
    subject { described_class }
    it 'permits the service to be called directly in a static manner' do
      expect(subject.call(bucket:, key:)).to eq(12_345)
      expect(s3_client).to have_received(:head_object).with(bucket:, key:)
    end
  end

  describe '#call' do
    subject { described_class.new(bucket:, key:) }

    it 'returns the size of the specified file in bytes' do
      expect(subject.call).to eq(12_345)
      expect(s3_client).to have_received(:head_object).with(bucket:, key:)
    end

    it 'returns nil and logs and error if the S3 client returns an unexpected payload' do
      allow(Rails.logger).to receive(:error)
      allow(s3_client).to receive(:head_object).and_return(instance_double(Aws::S3::Types::HeadObjectOutput, content_length: nil))

      expect(subject.call).to be(nil)

      expect(Rails.logger).to have_received(:error).with(/Invalid file details for S3 object #{key} in bucket #{bucket}/i)
    end

    it 'returns nil if the S3 client raises an error' do
      allow(Rails.logger).to receive(:error)
      allow(s3_client).to receive(:head_object).and_raise(Aws::S3::Errors::NoSuchKey.new(bucket, key))

      expect(subject.call).to be(nil)

      expect(Rails.logger).to have_received(:error).with(/Failed to retrieve file details for S3 object #{key} in bucket #{bucket}/i)
    end
  end
end
