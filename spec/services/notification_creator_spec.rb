# frozen_string_literal: true

require 'rails_helper'

RSpec.describe NotificationCreator do
  context 'loan allocation engine' do
    let!(:success_data) do
      { data: { errors_count: 1 } }
    end

    let!(:failure_data) do
      { data: { updated_count: 1, errors_count: 1, fail_reason: 'Boom' } }
    end

    let!(:engine_run) { create(:loan_allocation_engine_run) }

    let!(:run_id) { engine_run.id }

    it 'creates a successful loan allocation notification' do
      notification = NotificationCreator.create_loan_allocation_engine_success(success_data, run_id)
      expect(notification).to be_a(Notification)
      expect(Notification.count).to eq(1)
      expect(notification.title).to include('Engine Run')
      expect(notification.title).to include('Complete')
      expect(notification.title).to include(engine_run.id.to_s)
      expect(notification.title).to include(engine_run.created_at.in_time_zone('America/Chicago').strftime('%m/%-d/%Y, %I:%M %p'))
      expect(notification.message).to include('Candidate Loans')
      expect(notification.message).to include('Allocated Loans')
      expect(notification.message).to include('Unallocated Loans')
      expect(notification.message).to include('Errors Count')

      expect(notification.url).to include("/admin/loan_allocation_engine_runs/#{run_id}")
    end

    it 'creates a failure loan allocation notification' do
      notification = NotificationCreator.create_loan_allocation_engine_failure(failure_data, run_id)
      expect(notification).to be_a(Notification)
      expect(Notification.count).to eq(1)
      expect(notification.title).to include('Engine Run')
      expect(notification.title).to include('Failed')
      expect(notification.title).to include(engine_run.id.to_s)
      expect(notification.title).to include(engine_run.created_at.in_time_zone('America/Chicago').strftime('%m/%-d/%Y, %I:%M %p'))
      expect(notification.message).to include('Candidate Loans')
      expect(notification.message).to include('Allocated Loans')
      expect(notification.message).to include('Unallocated Loans')
      expect(notification.message).to include('Errors Count')
      expect(notification.url).to include("/admin/loan_allocation_engine_runs/#{run_id}")
    end
  end

  context 'ACH Submission Notifications' do
    let!(:payment_success_data) do
      { data: { successful_transactions: 1, failed_transactions: 1, title: ['AchPayment'] } }
    end

    let!(:funding_success_data) do
      { data: { successful_transactions: 1, failed_transactions: 1, title: %w[AchRefund AchAdvancement] } }
    end

    let!(:ach_submit_failure_data) do
      { data: { errors: 'Boom!' } }
    end

    it 'creates a payments success notification' do
      notification = NotificationCreator.create_ach_submission_success(payment_success_data)
      expect(notification).to be_a(Notification)
      expect(Notification.count).to eq(1)
      expect(notification.title).to include("AchPayment Submission #{Time.now.in_time_zone('America/Chicago').strftime('%m/%-d/%Y, %I:%M %p')} Complete")
      expect(notification.message).to include('Successful Transactions: 1')
      expect(notification.message).to include('Failed Transactions: 1')
      expect(notification.url).to eq(nil)
    end

    it 'creates a fundings/refunds/originations notification' do
      notification = NotificationCreator.create_ach_submission_success(funding_success_data)
      expect(notification).to be_a(Notification)
      expect(Notification.count).to eq(1)
      expect(notification.title).to include("AchFunding Submission #{Time.now.in_time_zone('America/Chicago').strftime('%m/%-d/%Y, %I:%M %p')} Complete")
      expect(notification.message).to include('Successful Transactions: 1')
      expect(notification.message).to include('Failed Transactions: 1')
      expect(notification.url).to eq(nil)
    end

    it 'creates an ach submit error notification' do
      notification = NotificationCreator.create_ach_submission_failure(ach_submit_failure_data)
      expect(notification).to be_a(Notification)
      expect(Notification.count).to eq(1)
      expect(notification.title).to include("ACH Submission #{Time.now.in_time_zone('America/Chicago').strftime('%m/%-d/%Y, %I:%M %p')} Failed!")
      expect(notification.message).to include('Errors: Boom!')
      expect(notification.url).to eq(nil)
    end
  end
end
