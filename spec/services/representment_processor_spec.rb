# frozen_string_literal: true

require 'rails_helper'

RSpec.describe RepresentmentProcessor, :vcr do
  include ActiveSupport::Testing::TimeHelpers
  include NotifierHelper

  let(:real_loan_id) { 514 }
  let(:loan_entity) do
    create(:loan_entity, deleted: false,
                         loan_setup_entity: build(:loan_setup_entity,
                                                  loan_term: 12,
                                                  payment_frequency: 'loan.frequency.monthly',
                                                  contract_date: Date.today))
  end
  let(:eligible_promise_states) { Representment::ELIGIBLE_PROMISE_STATES }
  let(:eligible_promise_type) { Representment::ELIGIBLE_PROMISE_TYPE }
  let(:loan_autopay_single) { Representment::LOAN_AUTOPAY_SINGLE }
  let(:loan_autopay_active) { Representment::LOAN_AUTOPAY_ACTIVE }
  let(:loan_status_open) { Representment::LOAN_STATUS_OPEN }
  let(:loan_sub_status_repaying) { Representment::LOAN_SUB_STATUS_REPAYING }
  let(:payment_profile_id) { ach_transaction.transaction_entity.payment_account_entity.id }

  let(:scheduled_transaction) { build(:ach_payment) }
  let(:transaction_scheduler_mock) { double }
  let(:skip_scheduler) { false }

  let!(:above_lending_loan) { create(:above_lending_loan, loan_pro_loan_entity: loan_entity) }
  let!(:loanpro_customer_id) { '98765' }
  let!(:loanpro_customer) { create(:above_lending_loanpro_customer, loanpro_customer_id:, borrower_id: above_lending_loan.borrower.id) }
  let(:ach_transaction) { create(:failed_ach_payment, above_lending_loan:, applied_at: '2024-02-15'.to_date) }

  let(:meta_attributes) do
    {
      transaction_id: Integer,
      representment_id: Integer,
      scheduled_transaction_id: Integer
    }
  end
  # We need to mock the ID of LoanPro::LoanEntity to match with a real staging record
  # This block does the mocking to get a 200 result from the loanpro.simnang.com instance
  before do
    allow_any_instance_of(Representment).to receive(:original_transaction).and_return(ach_transaction)

    allow(ach_transaction).to receive(:above_lending_loan).and_return(above_lending_loan)
    allow(above_lending_loan).to receive(:loan_pro_loan_entity).and_return(loan_entity)
    allow(loan_entity).to receive(:id).and_return(real_loan_id)
    allow(ActiveSupport::Notifications).to receive(:instrument).and_call_original
  end

  let(:expected_autopay_attributes) do
    {
      loan_id: above_lending_loan.loan_pro_loan_entity.id,
      attributes: {
        name: include('representment'),
        type: loan_autopay_single,
        amount: scheduled_transaction.transaction_amount,
        applyDate: scheduled_transaction.applied_at.to_date.iso8601,
        processDate: scheduled_transaction.processed_at.to_date.iso8601,
        processDateTime: "#{scheduled_transaction.processed_at.to_date.iso8601} 19:00:00",
        primaryPaymentMethodId: payment_profile_id,
        paymentMethodAuthType: LoanPro::LoanAutopayEntity.payment_method_auth_types[ach_transaction.sec_code]
      }
    }
  end

  describe '#call' do
    before do
      allow_any_instance_of(Slack::DashBot).to receive(:call).and_return(status: 200, body: '', headers: {})
    end

    context 'when the transaction is eligible' do
      let(:transaction_scheduler_args) do
        {
          representment: kind_of(Representment)
        }
      end
      before do
        travel_to('2024-02-23'.to_date)

        unless skip_scheduler
          expect(TransactionScheduler).to receive(:new).with(**transaction_scheduler_args).and_return(transaction_scheduler_mock)
          expect(transaction_scheduler_mock).to receive(:call).and_return(scheduled_transaction)
          expect(transaction_scheduler_mock).to receive(:processed_at).and_return(scheduled_transaction.processed_at)
        end
      end

      context 'and the transaction has no previous representment' do
        it 'creates a representment and schedules a payment in loanpro' do
          expect(ach_transaction.representment).to be_blank
          expect(LoanProService::LoanManagementSystem::CreateAutopay).to receive(:call).with(expected_autopay_attributes)

          expect do
            RepresentmentProcessor.call(ach_transaction:)
          end.to(change(AchTransaction, :count).by(1).and(change(Representment, :count).by(1)))

          ach_transaction.reload
          expect(ach_transaction.representment).to be_first_recycle_scheduled
          expect(ach_transaction.representment.ach_transactions.count).to eq(2)

          expect_to_notify('RepresentmentProcessor', success: false, fail_reason: String, extra: meta_attributes)
        end

        context 'when there is an issue with the TransactionScheduler' do
          let(:skip_scheduler) { true }
          before do
            expect(TransactionScheduler).to receive(:new).with(**transaction_scheduler_args).and_return(transaction_scheduler_mock)
            expect(transaction_scheduler_mock).to receive(:processed_at).and_return(2.days.from_now)
            expect(transaction_scheduler_mock).to receive(:call).and_return(nil)
          end

          it 'logs an error message and returns false' do
            expect(Rails.logger).to receive(:error).with(/TransactionScheduler did not create an AchPayment/)

            expect(RepresentmentProcessor.call(ach_transaction:)).to be_falsey
            expect_to_notify('RepresentmentProcessor', success: false, fail_reason: String, extra: meta_attributes.merge(scheduled_transaction_id: nil))
          end
        end
      end

      context 'and only one representment transaction exists' do
        let(:representment) do
          create(:representment, :first_recycle_completed, original_transaction: ach_transaction)
        end

        before do
          representment.ach_transactions.each { |transaction| transaction.update!(above_lending_loan:) }
        end

        it 'completes the current representment cycle and schedules another payment in loanpro' do
          expect(LoanProService::LoanManagementSystem::CreateAutopay).to receive(:call).with(expected_autopay_attributes)

          expect do
            RepresentmentProcessor.call(ach_transaction: representment.ach_transactions.last)
          end.to(change(AchTransaction, :count).by(1).and(change(Representment, :count).by(0)))

          representment.reload
          expect(representment).to be_second_recycle_scheduled
          expect(representment.ach_transactions.count).to eq(3)
          expect_to_notify('RepresentmentProcessor', success: false, fail_reason: String, extra: meta_attributes.merge(transaction_id: Integer))
        end

        context 'and the representment fails to schedule' do
          it 'creates a scheduled transaction, logs an error and does not create an autopay' do
            expect(LoanProService::LoanManagementSystem::CreateAutopay).not_to receive(:call).with(expected_autopay_attributes)
            allow(representment).to receive(:in_scheduled_state?).and_return(false)
            expect(Rails.logger).to receive(:error).with(/failed to transition to scheduled/)

            expect do
              RepresentmentProcessor.call(ach_transaction: representment.ach_transactions.last)
            end.to(change(AchTransaction, :count).by(1).and(change(Representment, :count).by(0)))

            expect_to_notify('RepresentmentProcessor', success: false, fail_reason: String, extra: meta_attributes.merge(scheduled_transaction_id: nil))
          end
        end

        context 'when the scheduled transaction is to be applied outside the eligibility window' do
          let(:skip_scheduler) { true }
          before do
            expect(TransactionScheduler).to receive(:new).with(**transaction_scheduler_args).and_return(transaction_scheduler_mock)
            expect(transaction_scheduler_mock).to receive(:processed_at).and_return(2.days.from_now)
          end

          it 'logs a message and cancels the representment' do
            allow(Rails.logger).to receive(:info)
            expect(Rails.logger).to receive(:info).with(/would land outside eligibility window/)
            representment.original_transaction.processed_at = 60.days.ago

            RepresentmentProcessor.call(ach_transaction: representment.ach_transactions.last)

            representment.reload
            expect(representment).to be_cancelled
            expect_to_notify('RepresentmentProcessor', success: true, extra: meta_attributes.merge(scheduled_transaction_id: nil))
          end
        end

        context 'when the id for the created autopay cannnot be found' do
          before do
            allow(LoanProService::LoanManagementSystem::GetLoanAutopays).to(
              receive(:call)
                .with(above_lending_loan.loan_pro_loan_entity.id)
                .and_return({ 'results' => {} })
            )
          end

          it 'logs an error message' do
            expect(LoanProService::LoanManagementSystem::CreateAutopay).to receive(:call).with(expected_autopay_attributes)
            expect(Rails.logger).to receive(:error).with(/Autopay ID for newly created payment missing/)
            RepresentmentProcessor.call(ach_transaction: representment.ach_transactions.last)

            expect_to_notify('RepresentmentProcessor', success: false, fail_reason: String, extra: meta_attributes)
          end
        end

        context 'when the autopays are not fetched properly' do
          before do
            allow(LoanProService::LoanManagementSystem::GetLoanAutopays).to(
              receive(:call)
                .with(above_lending_loan.loan_pro_loan_entity.id)
                .and_return({})
            )
          end

          it 'logs an error message' do
            expect(LoanProService::LoanManagementSystem::CreateAutopay).to receive(:call).with(expected_autopay_attributes)
            expect(Rails.logger).to receive(:error).with(/Failed to fetch loan autopays/)
            expect(Rails.logger).to receive(:error).with(/Autopay ID for newly created payment missing/)
            RepresentmentProcessor.call(ach_transaction: representment.ach_transactions.last)

            expect_to_notify('RepresentmentProcessor', success: false, fail_reason: String, extra: meta_attributes.merge(scheduled_transaction_id: nil))
          end
        end
      end

      context 'and two representment transactions exist' do
        let(:representment) do
          create(:representment, :second_recycle_completed, original_transaction: ach_transaction)
        end
        let(:skip_scheduler) { true }

        before do
          representment.ach_transactions.each { |transaction| transaction.update!(above_lending_loan:) }
        end

        it 'completes the current cycle and transitions the representment to exhausted' do
          expect(LoanProService::LoanManagementSystem::CreateAutopay).not_to receive(:call)

          expect do
            RepresentmentProcessor.call(ach_transaction: representment.ach_transactions.last)
          end.to(change(AchTransaction, :count).by(0).and(change(Representment, :count).by(0)))

          representment.reload
          expect(representment).to be_recycle_exhausted
          expect(representment.ach_transactions.count).to eq(3)
        end
      end

      context 'when LoanPro raises an error' do
        it 'logs an error message and re-raises the exception' do
          expect(LoanProService::LoanManagementSystem::CreateAutopay).to receive(:call)
            .and_raise(LoanProService::Exceptions::LoanManagementSystemError, 'Boom!')

          expect(Rails.logger).to receive(:error)
            .with("CreateAutopay - Failed to create a new autopay record for display id #{above_lending_loan.unified_id}")

          expect do
            RepresentmentProcessor.call(ach_transaction:)
          end.to raise_error(LoanProService::Exceptions::LoanManagementSystemError)
          expect_to_notify('RepresentmentProcessor', success: false, fail_reason: String, extra: meta_attributes)
        end
      end

      context 'when the transaction has already been processed' do
        let(:processed_ach_transaction) { representment.latest_transaction }
        let(:skip_scheduler) { true }

        before do
          representment.ach_transactions.each { |transaction| transaction.update!(above_lending_loan:) }
        end

        context 'when the transaction has been scheduled' do
          let(:representment) { create(:representment, :first_recycle_scheduled, original_transaction: ach_transaction) }

          it 'should log a message that the transaction has already been processed' do
            allow(Rails.logger).to receive(:info)
            expect(Rails.logger).to receive(:info).with(/has already been processed/)
            class_under_test = RepresentmentProcessor.new(ach_transaction: processed_ach_transaction)
            expect(class_under_test).not_to receive(:schedule_representment!)

            class_under_test.call
          end
        end

        context 'when the transaction has been submitted but not marked as complete' do
          let(:representment) { create(:representment, :first_recycle_submitted, original_transaction: ach_transaction) }

          it 'should log a message that the transaction has not been completed' do
            allow(Rails.logger).to receive(:info)
            expect(Rails.logger).to receive(:warn).with(/has not been completed/)
            class_under_test = RepresentmentProcessor.new(ach_transaction: processed_ach_transaction)
            expect(class_under_test).not_to receive(:schedule_representment!)

            class_under_test.call
          end
        end

        context 'when the transaction was moved to a terminal state' do
          let(:representment) { create(:representment, :honored_after_first_cycle, original_transaction: ach_transaction) }

          it 'should log a message that the transaction has already been processed' do
            allow(Rails.logger).to receive(:info)
            expect(Rails.logger).to receive(:info).with(/has already been processed/)
            class_under_test = RepresentmentProcessor.new(ach_transaction: processed_ach_transaction)
            expect(class_under_test).not_to receive(:schedule_representment!)

            class_under_test.call
          end
        end
      end
    end

    context 'when the transaction is ineligible' do
      context 'when the transaction is too old' do
        it 'creates a canceled representment and does not schedule a payment in loanpro' do
          ach_transaction.processed_at = 61.days.ago
          expect(LoanProService::LoanManagementSystem::CreateAutopay).not_to receive(:call)

          expect do
            RepresentmentProcessor.call(ach_transaction:)
          end.to(change(AchTransaction, :count).by(0).and(change(Representment, :count).by(1)))

          ach_transaction.reload
          expect(ach_transaction.representment).to be_cancelled
        end
      end

      context 'when the loan is not in a proper status' do
        let(:real_loan_id) { 525 }

        it 'creates a canceled representment and does not schedule a payment in loanpro' do
          ach_transaction.processed_at = 3.days.ago
          expect(LoanProService::LoanManagementSystem::CreateAutopay).not_to receive(:call)

          expect do
            RepresentmentProcessor.call(ach_transaction:)
          end.to(change(AchTransaction, :count).by(0).and(change(Representment, :count).by(1)))

          ach_transaction.reload
          expect(ach_transaction.representment).to be_cancelled
        end
      end

      context 'when the intended processing date is missing' do
        let(:transaction_scheduler_args) do
          {
            representment: kind_of(Representment)
          }
        end

        before do
          travel_to('2024-02-23'.to_date)

          expect(TransactionScheduler).to receive(:new).with(**transaction_scheduler_args).and_return(transaction_scheduler_mock)
          expect(transaction_scheduler_mock).to receive(:processed_at).and_return(nil)
        end

        it 'logs an error message and returns false' do
          ach_transaction
          expect(LoanProService::LoanManagementSystem::CreateAutopay).not_to receive(:call)
          expect(Rails.logger).to receive(:error).with(/Intended processing date/)

          expect do
            RepresentmentProcessor.call(ach_transaction:)
          end.to(change(AchTransaction, :count).by(0).and(change(Representment, :count).by(1)))
          expect_to_notify('RepresentmentProcessor', success: false, fail_reason: String, extra: meta_attributes.merge(scheduled_transaction_id: 'not set'))
        end
      end

      context 'when the payment profile is missing' do
        let(:ach_transaction) { create(:failed_ach_payment, above_lending_loan:, applied_at: '2022-02-15'.to_date, transaction_entity: nil) }
        let(:error_message) { "RepresentmentProcessor failed with error: ACH Payment ##{ach_transaction.id} does not have a payment account entity" }
        let(:payment_profile_id) { nil }

        it 'logs an error message and returns false' do
          ach_transaction
          expect(LoanProService::LoanManagementSystem::CreateAutopay).not_to receive(:call)
          expect(Rails.logger).to receive(:error).with(error_message)

          expect do
            RepresentmentProcessor.call(ach_transaction:)
          end.to change(AchTransaction, :count).by(0)
        end
      end
    end

    context 'when the transaction has an existing payment' do
      let(:real_loan_id) { 16_329 }
      before { travel_to('2022-02-23'.to_date) }

      it 'creates an honored representment and does not schedule a payment in loanpro when borrower has a future payment promised' do
        ach_transaction.processed_at = 3.days.ago
        expect(LoanProService::LoanManagementSystem::CreateAutopay).not_to receive(:call)

        expect do
          RepresentmentProcessor.call(ach_transaction:)
        end.to(change(AchTransaction, :count).by(0).and(change(Representment, :count).by(1)))

        ach_transaction.reload
        expect(ach_transaction.representment).to be_honored
      end

      it 'creates an honored representment and does not schedule a payment in loanpro when borrower has scheduled a one-time autopay' do
        ach_transaction.processed_at = 3.days.ago
        expect(LoanProService::LoanManagementSystem::CreateAutopay).not_to receive(:call)

        expect do
          RepresentmentProcessor.call(ach_transaction:)
        end.to(change(AchTransaction, :count).by(0).and(change(Representment, :count).by(1)))

        ach_transaction.reload
        expect(ach_transaction.representment).to be_honored
      end
    end
  end
end
