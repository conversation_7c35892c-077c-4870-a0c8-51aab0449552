# frozen_string_literal: true

require 'rails_helper'

RSpec.describe RequestTagging do
  let(:authorization) { Faker::Alphanumeric.alphanumeric }
  let(:default_headers) do
    {
      'Content-Type' => 'application/json',
      'X-Authorization' => authorization
    }
  end
  let!(:loan) { create(:above_lending_loan, :loanpro) }
  let!(:borrower_id) { loan.borrower.id }

  describe '#loan_pro_loan_id' do
    context 'when there is a borrower found' do
      before do
        allow(Auth::DecodeJwt).to receive(:call).and_return(Auth::DecodedToken.new(type: nil, borrower_id: borrower_id))
      end

      it 'returns the loan pro loan id' do
        expect(described_class.loan_pro_loan_id(authorization)).to eq(loan.loanpro_loan&.loanpro_loan_id)
      end
    end

    context 'when there is NO borrower found' do
      before do
        allow(Auth::DecodeJwt).to receive(:call).and_return(Auth::DecodedToken.new(type: nil, borrower_id: nil))
      end

      it 'returns nil' do
        expect(described_class.loan_pro_loan_id(authorization)).to be_nil
      end
    end

    context 'when there is an error raised' do
      before do
        allow(Auth::VerifyOauthToken).to receive(:call).and_raise(StandardError.new)
      end

      it 'returns nil' do
        expect(described_class.loan_pro_loan_id(authorization)).to be_nil
      end
    end
  end

  describe '#abovelending_loan_id' do
    context 'when there is a borrower found' do
      before do
        allow(Auth::DecodeJwt).to receive(:call).and_return(Auth::DecodedToken.new(type: nil, borrower_id: borrower_id))
      end

      it 'returns the loan id' do
        expect(described_class.abovelending_loan_id(authorization)).to eq(loan.id)
      end
    end

    context 'when there is NO borrower found' do
      before do
        allow(Auth::DecodeJwt).to receive(:call).and_return(Auth::DecodedToken.new(type: nil, borrower_id: nil))
      end

      it 'returns nil' do
        expect(described_class.abovelending_loan_id(authorization)).to be_nil
      end
    end

    context 'when there is an error raised' do
      before do
        allow(Auth::VerifyOauthToken).to receive(:call).and_raise(StandardError.new)
      end

      it 'returns nil' do
        expect(described_class.abovelending_loan_id(authorization)).to be_nil
      end
    end
  end

  describe '#loan_unified_id' do
    context 'when there is a borrower found' do
      before do
        allow(Auth::DecodeJwt).to receive(:call).and_return(Auth::DecodedToken.new(type: nil, borrower_id: borrower_id))
      end

      it 'returns the unified id' do
        expect(described_class.loan_unified_id(authorization)).to eq(loan&.unified_id)
      end
    end

    context 'when there is NO borrower found' do
      before do
        allow(Auth::DecodeJwt).to receive(:call).and_return(Auth::DecodedToken.new(type: nil, borrower_id: nil))
      end

      it 'returns nil' do
        expect(described_class.loan_unified_id(authorization)).to be_nil
      end
    end

    context 'when there is an error raised' do
      before do
        allow(Auth::VerifyOauthToken).to receive(:call).and_raise(StandardError.new)
      end

      it 'returns nil' do
        expect(described_class.loan_unified_id(authorization)).to be_nil
      end
    end
  end
end
