# frozen_string_literal: true

require 'rails_helper'

RSpec.describe SendFailedAchTransactions do
  let!(:payment_transaction) { create(:failed_ach_payment) }
  let!(:funding_transaction) { create(:failed_ach_refund) }

  subject { described_class.new([payment_transaction, funding_transaction]) }

  describe '#call' do
    it 'generates a csv and mails it' do
      expect { subject.call }.to change { ActionMailer::Base.deliveries.count }.by(1)

      attachment = ActionMailer::Base.deliveries.first.attachments.last
      lines = attachment.decoded.split("\n")
      expect(attachment.content_type).to start_with('text/csv')
      expect(lines.count).to eq(3)

      tx_data = lines.collect { |line| line.split(',') }
      credit_cols = tx_data.find { |tx| tx[0].to_i == funding_transaction.id }
      debit_cols = tx_data.find { |tx| tx[0].to_i == payment_transaction.id }

      expect(credit_cols[0].to_i).to eq(funding_transaction.id)
      expect(credit_cols[1].to_i).to eq(funding_transaction.transaction_entity.id)
      expect(credit_cols[2]).to eq('')
      expect(credit_cols[3]).to eq('credit')
      expect(credit_cols[4]).to eq('transaction_failed')
      expect(credit_cols[5]).to eq(funding_transaction.response_message)
      expect(credit_cols[6].to_d).to eq(funding_transaction.amount)
      expect(credit_cols[7].strip).to eq(funding_transaction.transaction_entity.mc_processor_extender.processor_name)

      expect(debit_cols[0].to_i).to eq(payment_transaction.id)
      expect(debit_cols[1].to_i).to eq(payment_transaction.transaction_entity.id)
      expect(debit_cols[2]).to eq('')
      expect(debit_cols[3]).to eq('debit')
      expect(debit_cols[4]).to eq('transaction_failed')
      expect(debit_cols[5]).to eq(payment_transaction.response_message)
      expect(debit_cols[6].to_d).to eq(payment_transaction.amount)
      expect(debit_cols[7].strip).to eq(payment_transaction.transaction_entity.mc_processor_extender.processor_name)
    end
  end
end
