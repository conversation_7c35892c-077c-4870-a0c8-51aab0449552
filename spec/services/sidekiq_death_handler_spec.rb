# frozen_string_literal: true

require 'rails_helper'

RSpec.describe SidekiqDeathHandler do
  include NotifierHelper

  describe '#call' do
    let(:job) { { 'class' => 'TestJob', 'jid' => '12345' } }
    let(:error) { StandardError.new('Boom!') }
    let(:meta) { "#{job['class']} #{job['jid']} just died with error #{error.message}." }

    before do
      set_notifier_stubs
    end

    it 'notifies with meta data and calls slackbot' do
      allow(Slack::DashBot).to receive(:call)

      described_class.call(meta)

      expect_to_notify('sidekiq_death_handler', meta:)
      expect(Slack::DashBot).to have_received(:call).with(message: meta, channel: Rails.application.config_for(:slack_channels).above_dash_alerts)
    end
  end
end
