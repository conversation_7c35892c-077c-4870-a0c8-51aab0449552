# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Slack::DashBot do
  let(:subject) { described_class.call(message: 'hello world') }

  describe '.call' do
    before do
      stub_request(:post, 'https://slack.com/api/chat.postMessage').to_return(status: 200, body: '', headers: {})
    end

    it 'calls slack api and logs event' do
      subject
      expect(WebMock).to have_requested(:post, 'https://slack.com/api/chat.postMessage')
        .with(body: a_string_including('channel=%23dash-test-bot&text=%22Environment%3A+Test%22%0Ahello+world%0A'))
    end

    it 'calls slack api with message blocks' do
      message_blocks = [{ type: 'section', text: { type: 'mrkdwn', text: 'Hello, world!' } }]
      expected = [
        { type: 'section', text: { type: 'mrkdwn', text: 'Environment: Test' } },
        { type: 'section', text: { type: 'mrkdwn', text: 'Hello, world!' } }
      ]

      described_class.call(message: message_blocks)
      expect(WebMock).to have_requested(:post, 'https://slack.com/api/chat.postMessage')
        .with(body: a_string_including(URI.encode_www_form_component(expected.to_json)))
    end

    it 'does not include PII in messages' do
      pii_message = <<-MSG
        SendReturnedPaymentEmailJob 2415439b2409ada2e5943df9 just died with error Missing data: bank_name
      MSG

      described_class.call(message: pii_message)
      expect(WebMock).to(have_requested(:post, 'https://slack.com/api/chat.postMessage').with do |req|
        expect(req.body).not_to include('Flour Brisket')
        expect(req.body).not_to include('<EMAIL>')
      end)
    end

    context '#channel in production environment' do
      before { allow(Rails).to receive(:env).and_return(ActiveSupport::StringInquirer.new('production')) }

      it 'returns the production channel' do
        dash_bot = described_class.new(message: 'Hello world')
        expect(dash_bot.channel).to eq('#dash-bot')
      end
    end

    context 'in non-production environment' do
      before { allow(Rails).to receive(:env).and_return(ActiveSupport::StringInquirer.new('development')) }

      it 'returns the non-production channel' do
        dash_bot = described_class.new(message: 'Hello world')
        expect(dash_bot.channel).to eq('#dash-test-bot')
      end
    end
  end

  describe '.upload' do
    let!(:request) do
      stub_request(:post, 'https://slack.com/api/files.upload')
        .with(
          body: {
            as_user: 'true',
            channels: '#dash-test-bot',
            content: 'the content',
            filename: 'file-like.csv',
            filetype: 'csv',
            initial_comment: 'Hello world',
            title: 'file-like.csv'
          }
        )
        .to_return(status: 200, body: '', headers: {})
    end

    it 'calls the dash file upload api' do
      described_class.new(message: 'Hello world').upload(content: 'the content', filename: 'file-like.csv')
      expect(request).to have_been_requested
    end
  end
end
