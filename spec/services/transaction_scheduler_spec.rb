# frozen_string_literal: true

require 'rails_helper'

RSpec.describe TransactionScheduler do
  include ActiveSupport::Testing::TimeHelpers

  let(:recurring_type) { 'autopay.type.recurring' }
  let(:loan_pro_autopay_data) do
    [
      { 'processDateTime' => "/Date(#{'2023-01-15'.to_datetime.to_i})/", 'type' => recurring_type, 'active' => 1 },
      { 'processDateTime' => "/Date(#{'2023-02-15'.to_datetime.to_i})/", 'type' => recurring_type, 'active' => 1 },
      { 'processDateTime' => "/Date(#{'2023-02-13'.to_datetime.to_i})/", 'type' => recurring_type, 'active' => 0 },
      { 'processDateTime' => "/Date(#{'2023-02-14'.to_datetime.to_i})/", 'type' => Representment::LOAN_AUTOPAY_SINGLE, 'active' => 1 },
      { 'processDateTime' => "/Date(#{'2023-03-15'.to_datetime.to_i})/", 'type' => recurring_type, 'active' => 1 }
    ]
  end

  let(:loan_pro_loan_data) do
    {
      'Autopays' => loan_pro_autopay_data
    }
  end

  before do
    allow(representment).to receive(:loan_pro_loan_data).and_return(loan_pro_loan_data)
    allow_any_instance_of(Slack::DashBot).to receive(:call).and_return(status: 200, body: '', headers: {})
  end

  describe '#initialize' do
    let(:representment) { create(:representment) }
    subject { described_class.new(representment:) }
    it { is_expected.to have_attributes(representment:, loan_pro_autopay_data:, original_transaction: representment.original_transaction) }
  end

  describe '#call' do
    let(:representment) { create(:representment) }

    RSpec.shared_examples 'a successful call' do
      it 'returns a created AchPayment record with appropriate properties' do
        scheduled_transaction = TransactionScheduler.call(representment:)
        expect(scheduled_transaction).to have_attributes(
          applied_at: be_a(ActiveSupport::TimeWithZone),
          loan_id: representment.original_transaction.loan_id,
          processed_at: be_a(ActiveSupport::TimeWithZone),
          transaction_amount: representment.original_transaction.transaction_amount,
          transaction_type: representment.original_transaction.transaction_type
        )
      end

      context 'when in the first representment cycle' do
        let(:representment) { create(:representment) }

        context 'when within seven days of original transaction' do
          it 'schedules payment for seven days from original transaction' do
            travel_to('2023-03-15'.to_date)
            representment.original_transaction.processed_at = '2023-03-10'.to_date

            scheduled_transaction = TransactionScheduler.call(representment:)
            expect(scheduled_transaction.processed_at).to eq('2023-03-17'.to_date)
            expect(scheduled_transaction.applied_at).to eq('2023-03-18'.to_date)
          end

          it 'does not schedule payment on a weekend' do
            travel_to('2023-03-15'.to_date)
            representment.original_transaction.processed_at = '2023-03-11'.to_date

            scheduled_transaction = TransactionScheduler.call(representment:)
            expect(scheduled_transaction.processed_at).to eq('2023-03-20'.to_date)
            expect(scheduled_transaction.applied_at).to eq('2023-03-21'.to_date)
          end

          it 'does not schedule payment on a holiday' do
            travel_to('2023-05-25'.to_date)
            representment.original_transaction.processed_at = '2023-05-22'.to_date

            scheduled_transaction = TransactionScheduler.call(representment:)
            expect(scheduled_transaction.processed_at).to eq('2023-05-30'.to_date)
            expect(scheduled_transaction.applied_at).to eq('2023-05-31'.to_date)
          end
        end

        context 'when more than seven days after the original transaction' do
          it 'schedules payment for the next working day' do
            travel_to('2023-02-01T08:00:00'.to_datetime)
            representment.original_transaction.processed_at = '2023-01-23'.to_date

            scheduled_transaction = TransactionScheduler.call(representment:)
            expect(scheduled_transaction.processed_at).to eq('2023-02-02'.to_date)
            expect(scheduled_transaction.applied_at).to eq('2023-02-03'.to_date)
          end

          it 'does not schedule payment on a weekend' do
            travel_to('2023-03-17T08:00:00'.to_datetime)
            representment.original_transaction.processed_at = '2023-03-08'.to_date

            scheduled_transaction = TransactionScheduler.call(representment:)
            expect(scheduled_transaction.processed_at).to eq('2023-03-20'.to_date)
            expect(scheduled_transaction.applied_at).to eq('2023-03-21'.to_date)
          end

          it 'does not schedule payment on a holiday' do
            travel_to('2023-05-26T08:00:00'.to_datetime)
            representment.original_transaction.processed_at = '2023-05-18'.to_date

            scheduled_transaction = TransactionScheduler.call(representment:)
            expect(scheduled_transaction.processed_at).to eq('2023-05-30'.to_date)
            expect(scheduled_transaction.applied_at).to eq('2023-05-31'.to_date)
          end
        end
      end

      context 'when in the second representment cycle' do
        let(:representment) do
          create(:representment, :first_recycle_completed)
        end

        it 'schedules payment for the next recurring payment autopayment date' do
          travel_to('2023-01-24'.to_date)
          representment.original_transaction.processed_at = '2023-01-23'.to_date

          scheduled_transaction = TransactionScheduler.call(representment:)
          expect(scheduled_transaction.processed_at).to eq('2023-02-15'.to_date)
          expect(scheduled_transaction.applied_at).to eq('2023-02-16'.to_date)
        end

        it 'schedules payment for a future autopayment date if current day is the next upcoming autopay date' do
          travel_to('2023-02-15'.to_date)
          representment.original_transaction.processed_at = '2023-01-23'.to_date

          scheduled_transaction = TransactionScheduler.call(representment:)
          expect(scheduled_transaction.processed_at).to eq('2023-03-15'.to_date)
          expect(scheduled_transaction.applied_at).to eq('2023-03-16'.to_date)
        end
      end
    end

    context 'when the autopay type is recurringMatch' do
      let(:recurring_type) { Representment::LOAN_AUTOPAY_RECURRING_MATCH }

      it_behaves_like 'a successful call'
    end

    context 'when the autopay type is recurring' do
      let(:recurring_type) { Representment::LOAN_AUTOPAY_RECURRING }

      it_behaves_like 'a successful call'
    end
  end

  describe '#processed_at' do
    let(:representment) { create(:representment, :first_recycle_completed) }
    subject { described_class.new(representment:) }

    context 'when there are no active recurring autopays for a representment in an advanced cycle' do
      let(:loan_pro_autopay_data) do
        [
          { 'processDateTime' => "/Date(#{'2023-01-15'.to_datetime.to_i})/", 'type' => Representment::LOAN_AUTOPAY_SINGLE, 'active' => 1 },
          { 'processDateTime' => "/Date(#{'2023-02-15'.to_datetime.to_i})/", 'type' => Representment::LOAN_AUTOPAY_SINGLE, 'active' => 1 },
          { 'processDateTime' => "/Date(#{'2023-02-13'.to_datetime.to_i})/", 'type' => Representment::LOAN_AUTOPAY_RECURRING, 'active' => 0 },
          { 'processDateTime' => "/Date(#{'2023-02-14'.to_datetime.to_i})/", 'type' => Representment::LOAN_AUTOPAY_SINGLE, 'active' => 1 },
          { 'processDateTime' => "/Date(#{'2023-03-15'.to_datetime.to_i})/", 'type' => Representment::LOAN_AUTOPAY_SINGLE, 'active' => 1 }
        ]
      end

      it 'logs an error message and returns nil' do
        expect(Rails.logger).to receive(:error).with(/no active recurring or recurringMatch autopays/)
        expect(subject.processed_at).to be_nil
      end
    end

    context 'when the process dates for the loan are not set in the future or cannot be parsed' do
      it 'logs an error message and returns nil' do
        travel_to('2023-04-15'.to_date)
        expect(Rails.logger).to receive(:error).with(/Unable to parse future payment dates/)
        expect(subject.processed_at).to be_nil
      end
    end
  end
end
