# frozen_string_literal: true

require 'rails_helper'

RSpec.describe UnallocatedLoanReport do
  include NotifierHelper

  let(:unallocated_loan) { create(:above_lending_loan, :unallocated) }

  describe '#call' do
    it 'sends report' do
      expect(unallocated_loan.til_history.first.til_data).to_not eq(nil)

      expect { subject.call }.to change { ActionMailer::Base.deliveries.count }.by(1)

      attachment = ActionMailer::Base.deliveries.first.attachments.last
      lines = attachment.decoded.split("\n")

      expect(attachment.content_type).to start_with('text/csv')
      expect(lines.count).to eq(2)
      expect(lines[1].split(',')[1]).to eq(unallocated_loan.id)

      expect(LoanReportUploader.new.fog_directory).to eq('unallocated-loan-reports')
    end

    context 'when an error occurs' do
      before do
        allow(ActiveSupport::Notifications).to receive(:instrument).and_call_original
        allow(AboveLending::Loan).to receive(:unallocated_loans).and_raise(StandardError, 'Boom!')
      end

      it 'notifies instrumentation and returns false' do
        expect(subject.call).to be_falsey
        expect_to_notify('send_unallocated_loan_report', success: false, fail_reason: 'Boom!')
      end
    end
  end
end
