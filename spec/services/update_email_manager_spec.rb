# frozen_string_literal: true

require 'rails_helper'

RSpec.describe UpdateEmailManager do
  let!(:loan) do
    create(:above_lending_loan,
           loan_app_status: create(:above_lending_loan_app_status, :pending))
  end

  let(:onboarded_status) { create(:above_lending_loan_app_status, :onboarded) }
  let!(:loanpro_customer) { create(:above_lending_loanpro_customer, borrower_id: loan.borrower_id) }
  let(:old_email) { loan.borrower.email }
  let(:new_email) { '<EMAIL>' }

  subject { described_class.call(loan:, old_email:, new_email:) }

  let(:loanpro_service_double) { instance_double('LoanProService::LoanManagementSystem::UpdateCustomer') }

  before do
    allow(AmsApi).to receive(:update_user_email)

    allow(GdsApi).to receive(:patch_loan_app)
    allow(LoanProService::LoanManagementSystem::UpdateCustomer).to receive(:call).and_return(loanpro_service_double)

    allow(loanpro_service_double).to receive(:call)
  end

  context 'when updating the user\'s email' do
    before do
      allow(AmsApi).to receive(:update_user_email)
    end

    it 'calls the AMS to update the User\'s email address' do
      subject
      expect(AmsApi).to have_received(:update_user_email).with(old_email, new_email)
    end
  end

  context 'when updating Case Center' do
    it 'updates case center' do
      subject

      expect(GdsApi).to have_received(:patch_loan_app).with(
        request_id: loan.request_id,
        product_type: loan.product_type,
        loan_app: {},
        borrower: { email: new_email }
      )
    end

    context 'when the loan is onboarded' do
      let!(:loan) do
        create(:above_lending_loan,
               loan_app_status: create(:above_lending_loan_app_status, :onboarded))
      end

      it 'does not update loanpro service' do
        subject

        expect(GdsApi).not_to have_received(:patch_loan_app)
      end
    end
  end

  context 'when updating LoanPro' do
    it 'updates loanpro service' do
      subject

      expect(LoanProService::LoanManagementSystem::UpdateCustomer).to have_received(:call).with(
        customer_id: loanpro_customer.loanpro_customer_id,
        attributes: { email: new_email, accessUserName: new_email, __ignoreWarnings: true }
      )
    end

    it 'does not update loanpro when loanpro customer' do
      loanpro_customer.delete

      subject

      expect(LoanProService::LoanManagementSystem::UpdateCustomer).not_to have_received(:call)
    end
  end

  describe 'rollback behavior' do
    let!(:loan) do
      create(:above_lending_loan,
             loan_app_status: create(:above_lending_loan_app_status, :pending))
    end

    context 'when AMS fails' do
      before { allow(AmsApi).to receive(:update_user_email).and_raise(AmsApi::ResponseError, 'Email already in use') }

      it 'stops further updates' do
        expect { subject }.to raise_error(UpdateEmailManager::UpdateFailed, /Email already in use/i)

        expect(LoanProService::LoanManagementSystem::UpdateCustomer).not_to have_received(:call)
        expect(GdsApi).not_to have_received(:patch_loan_app)
      end
    end
  end

  context 'when Case Center update fails' do
    before { allow(AmsApi).to receive(:update_user_email) }

    it 'does not attempt LoanPro update' do
      allow(GdsApi).to receive(:patch_loan_app).and_raise(StandardError.new('CaseCenter failure'))
      expect { subject }.to raise_error(UpdateEmailManager::UpdateFailed, /CaseCenter failure/i)

      expect(LoanProService::LoanManagementSystem::UpdateCustomer).not_to have_received(:call)
    end

    it 'rolls back AMS' do
      allow(GdsApi).to receive(:patch_loan_app).and_raise(StandardError.new('CaseCenter failure'))
      expect { subject }.to raise_error(UpdateEmailManager::UpdateFailed, /CaseCenter failure/i)

      expect(AmsApi).to have_received(:update_user_email).with(old_email, new_email)
      expect(AmsApi).to have_received(:update_user_email).with(new_email, old_email)
    end
  end

  context 'when LoanPro update fails' do
    before do
      allow(AmsApi).to receive(:update_user_email)
      allow(LoanProService::LoanManagementSystem::UpdateCustomer).to receive(:call).and_raise(StandardError.new('Loanpro failed'))
    end

    it 'rolls back AMS' do
      expect { subject }.to raise_error(UpdateEmailManager::UpdateFailed, /Loanpro failed/i)

      expect(AmsApi).to have_received(:update_user_email).with(old_email, new_email)
      expect(AmsApi).to have_received(:update_user_email).with(new_email, old_email)
    end

    context 'when the loan is onboarded' do
      let(:approved_status) { create(:above_lending_loan_app_status, :onboarded) }
      let!(:loan) { create(:above_lending_loan, :loanpro, loan_app_status_id: approved_status.id, todos: []) }

      it 'does not roll back Case Center' do
        expect { subject }.to raise_error(UpdateEmailManager::UpdateFailed, /Loanpro failed/i)

        expect(AmsApi).to have_received(:update_user_email).twice
        expect(GdsApi).not_to have_received(:patch_loan_app)
      end
    end

    context 'when a rollback error occurs' do
      before do
        allow(AmsApi).to receive(:update_user_email).with(old_email, new_email)
        allow(AmsApi).to receive(:update_user_email).with(new_email, old_email).and_raise(StandardError, 'Please fail me')

        allow(LoanProService::LoanManagementSystem::UpdateCustomer).to receive(:call).and_raise(StandardError.new('Loanpro failed'))
      end

      it 'records the rollback error' do
        expect { subject }.to raise_error(UpdateEmailManager::UpdateFailed, /Ams rollback failed/i)
      end
    end
  end
end
