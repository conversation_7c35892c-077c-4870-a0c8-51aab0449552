# frozen_string_literal: true

require 'rails_helper'

RSpec.describe UpdateLoanProData do
  let!(:loan) { create(:above_lending_loan, :loanpro) }
  let!(:loan_transfer_date) { Date.today }
  let!(:financed_date) { Date.today }
  let!(:loan_purchase_date) { Date.today }
  let!(:cspv_repurchase_date) { Date.today }
  let(:mock_update_loan_settings_reponse) { double }

  describe '#call' do
    before do
      allow(mock_update_loan_settings_reponse).to receive(:success?).and_return(true)
    end

    it 'updates a LoanPro loan with the passed data' do
      expect(LoanProService::LoanManagementSystem::UpdateLoanSettings).to receive(:call)
        .with(hash_including(
                custom_fields: hash_including(
                  LoanPro::CustomFieldEntity::FINANCED_DATE_FIELD_ID => financed_date,
                  LoanPro::CustomFieldEntity::LOAN_TRANSFER_DATE_FIELD_ID => loan_transfer_date,
                  LoanPro::CustomFieldEntity::PURCHASE_DATE_FIELD_ID => loan_purchase_date,
                  LoanPro::CustomFieldEntity::CSPV_REPURCHASE_DATE_FIELD_ID => cspv_repurchase_date
                )
              )).and_return(mock_update_loan_settings_reponse)

      UpdateLoanProData.call(loan:, financed_date:, loan_transfer_date:, loan_purchase_date:, cspv_repurchase_date:)
    end

    it 'does not update attributes that are not passed' do
      expect(LoanProService::LoanManagementSystem::UpdateLoanSettings).to receive(:call)
        .with(hash_including(
                custom_fields: hash_including(
                  LoanPro::CustomFieldEntity::FINANCED_DATE_FIELD_ID => financed_date
                )
              )).and_return(mock_update_loan_settings_reponse)

      UpdateLoanProData.call(loan:, financed_date:)
    end

    it 'returns early if there is nothing to update' do
      expect(LoanProService::LoanManagementSystem::UpdateLoanSettings).not_to receive(:call)

      UpdateLoanProData.call(loan:)
    end

    context 'when the LoanProService raises an error' do
      it 'catches the error, logs a messsage and the backtrace through Notifier' do
        expect(LoanProService::LoanManagementSystem::UpdateLoanSettings).to receive(:call)
          .and_raise(LoanProService::Exceptions::LoanManagementSystemError.new('Failed to update loan'))
        expect(Rails.logger).to receive(:error).with(
          'Edit Loanpro Loan - Failed to update data for Loanpro Loan',
          loan_id: loan.id,
          loanpro_loan_id: loan.loan_pro_loan_entity.id,
          message: 'Failed to update loan'
        )
        expect { UpdateLoanProData.call(loan:, financed_date:) }.to raise_error("Failed to update data for Loanpro loan #{loan.loan_pro_loan_entity.id}: Failed to update loan")
      end
    end
    context 'errors' do
      let!(:loan_cspv_repurchase_date) { Date.today }
      subject { described_class.call(loan: loan, cspv_repurchase_date: loan_cspv_repurchase_date) }
      context 'when loan is not present' do
        let(:loan) { nil }
        it 'should raise error for if loan does not exist' do
          expect { subject }.to raise_error(/Unified ID not found/)
        end
      end
      context 'when loanpro loan is not present' do
        let(:loan) { create(:above_lending_loan) }
        it 'should raise error for Loanpro Loan Entity' do
          expect { subject }.to raise_error(/Active Loanpro Loan Entity not found/)
        end
      end
    end
  end
end
