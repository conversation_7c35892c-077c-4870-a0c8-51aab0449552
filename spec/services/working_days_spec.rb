# frozen_string_literal: true

require 'rails_helper'

RSpec.describe WorkingDays do
  include ActiveSupport::Testing::TimeHelpers

  describe '.working_day?' do
    it 'returns false on all saturdays' do
      expect(WorkingDays.working_day?('2022-10-08'.to_date)).to eq(false) # Late in the year
      expect(WorkingDays.working_day?('2022-02-26'.to_date)).to eq(false) # Early in the year
      expect(WorkingDays.working_day?('1999-07-10'.to_date)).to eq(false) # In a past year
      expect(WorkingDays.working_day?('2040-05-19'.to_date)).to eq(false) # In a future year
    end

    it 'returns false on all sundays' do
      expect(WorkingDays.working_day?('2022-10-09'.to_date)).to eq(false) # Late in the year
      expect(WorkingDays.working_day?('2022-02-27'.to_date)).to eq(false) # Early in the year
      expect(WorkingDays.working_day?('1999-07-11'.to_date)).to eq(false) # In a past year
      expect(WorkingDays.working_day?('2040-05-20'.to_date)).to eq(false) # In a future year
    end

    it 'returns false on all US federal bank holidays in 2024' do
      expect(WorkingDays.working_day?('2024-01-01'.to_date)).to eq(false) # New Year's Day
      expect(WorkingDays.working_day?('2024-01-15'.to_date)).to eq(false) # MLK Jr Day
      expect(WorkingDays.working_day?('2024-02-19'.to_date)).to eq(false) # President's Day
      expect(WorkingDays.working_day?('2024-05-27'.to_date)).to eq(false) # Memorial Day
      expect(WorkingDays.working_day?('2024-06-19'.to_date)).to eq(false) # Juneteenth
      expect(WorkingDays.working_day?('2024-07-04'.to_date)).to eq(false) # Independence Day
      expect(WorkingDays.working_day?('2024-09-02'.to_date)).to eq(false) # Labor Day
      expect(WorkingDays.working_day?('2024-10-14'.to_date)).to eq(false) # Columbus Day
      expect(WorkingDays.working_day?('2024-11-11'.to_date)).to eq(false) # Veterans Day
      expect(WorkingDays.working_day?('2024-11-28'.to_date)).to eq(false) # Thanksgiving Day
      expect(WorkingDays.working_day?('2024-12-25'.to_date)).to eq(false) # Christmas Day
    end

    it 'returns false on all US federal bank holidays in 2025' do
      expect(WorkingDays.working_day?('2025-01-01'.to_date)).to eq(false) # New Year's Day
      expect(WorkingDays.working_day?('2025-01-20'.to_date)).to eq(false) # MLK Jr Day
      expect(WorkingDays.working_day?('2025-02-17'.to_date)).to eq(false) # President's Day
      expect(WorkingDays.working_day?('2025-05-26'.to_date)).to eq(false) # Memorial Day
      expect(WorkingDays.working_day?('2025-06-19'.to_date)).to eq(false) # Juneteenth
      expect(WorkingDays.working_day?('2025-07-04'.to_date)).to eq(false) # Independence Day
      expect(WorkingDays.working_day?('2025-09-01'.to_date)).to eq(false) # Labor Day
      expect(WorkingDays.working_day?('2025-10-13'.to_date)).to eq(false) # Columbus Day
      expect(WorkingDays.working_day?('2025-11-11'.to_date)).to eq(false) # Veterans Day
      expect(WorkingDays.working_day?('2025-11-27'.to_date)).to eq(false) # Thanksgiving Day
      expect(WorkingDays.working_day?('2025-12-25'.to_date)).to eq(false) # Christmas Day
    end

    it 'returns false on all US federal bank holidays in 2026' do
      expect(WorkingDays.working_day?('2026-01-01'.to_date)).to eq(false) # New Year's Day
      expect(WorkingDays.working_day?('2026-01-19'.to_date)).to eq(false) # MLK Jr Day
      expect(WorkingDays.working_day?('2026-02-16'.to_date)).to eq(false) # President's Day
      expect(WorkingDays.working_day?('2026-05-25'.to_date)).to eq(false) # Memorial Day
      expect(WorkingDays.working_day?('2026-06-19'.to_date)).to eq(false) # Juneteenth
      expect(WorkingDays.working_day?('2026-07-04'.to_date)).to eq(false) # Independence Day (Saturday)
      expect(WorkingDays.working_day?('2026-09-07'.to_date)).to eq(false) # Labor Day
      expect(WorkingDays.working_day?('2026-10-12'.to_date)).to eq(false) # Columbus Day
      expect(WorkingDays.working_day?('2026-11-11'.to_date)).to eq(false) # Veterans Day
      expect(WorkingDays.working_day?('2026-11-26'.to_date)).to eq(false) # Thanksgiving Day
      expect(WorkingDays.working_day?('2026-12-25'.to_date)).to eq(false) # Christmas Day
    end

    it 'returns true on holidays that treat the preceding Friday as open' do
      expect(WorkingDays.working_day?('2026-07-03'.to_date)).to be(true) # Friday before July 4th
    end
  end

  describe '.next_working_day' do
    it 'returns the following day if it is a working day and the inclusive value is unset' do
      travel_to('2022-10-06T11:00:00Z'.to_time) # 6am Central Time
      expect(WorkingDays.next_working_day).to eq('2022-10-07'.to_date)
    end

    it 'returns the following day if it is a working day and the inclusive value is set to false' do
      travel_to('2022-10-06T11:00:00Z'.to_time) # 6am Central Time
      expect(WorkingDays.next_working_day(inclusive: false)).to eq('2022-10-07'.to_date)
    end

    it 'returns the current day if it is a working day and the inclusive value is set to true' do
      travel_to('2022-10-06T11:00:00Z'.to_time) # 6am Central Time
      expect(WorkingDays.next_working_day(inclusive: true)).to eq('2022-10-06'.to_date)
    end

    it 'returns the following day if the it is a working day, the current day is a weekend/holiday, even if the inclusive value is set to true' do
      travel_to('2022-10-02T11:00:00Z'.to_time) # 6am Central Time
      expect(WorkingDays.next_working_day(inclusive: true)).to eq('2022-10-03'.to_date)
    end

    it 'returns the next working day if the current day is a friday, and the inclusive value is set to false' do
      travel_to('2022-10-07T11:00:00Z'.to_time) # 6am Central Time
      expect(WorkingDays.next_working_day(inclusive: false)).to eq('2022-10-11'.to_date)
    end
  end

  describe '.previous_working_day' do
    it 'returns the previous day if it is a working day and the inclusive value is unset' do
      travel_to('2022-10-06T11:00:00Z'.to_time) # 6am Central Time
      expect(WorkingDays.previous_working_day).to eq('2022-10-05'.to_date)
    end

    it 'returns the previous day if it is a working day and the inclusive value is set to false' do
      travel_to('2022-10-06T11:00:00Z'.to_time) # 6am Central Time
      expect(WorkingDays.previous_working_day(inclusive: false)).to eq('2022-10-05'.to_date)
    end

    it 'returns the previous day if it is a working day and the inclusive value is set to true' do
      travel_to('2022-10-06T11:00:00Z'.to_time) # 6am Central Time
      expect(WorkingDays.previous_working_day(inclusive: true)).to eq('2022-10-06'.to_date)
    end

    it 'returns the previous day if the it is a working day, the current day is a weekend/holiday, even if the inclusive value is set to true' do
      travel_to('2022-10-02T11:00:00Z'.to_time) # 6am Central Time
      expect(WorkingDays.previous_working_day(inclusive: true)).to eq('2022-09-30'.to_date)
    end

    it 'returns the previous working day if the current day is a monday, and the inclusive value is set to false' do
      travel_to('2022-10-10T11:00:00Z'.to_time) # 6am Central Time
      expect(WorkingDays.previous_working_day(inclusive: false)).to eq('2022-10-07'.to_date)
    end
  end
end
