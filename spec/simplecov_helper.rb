# frozen_string_literal: true

require 'active_support/inflector'
require 'simplecov'

module <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
  def self.report_coverage
    SimpleCov.start 'rails' do
      add_filter '/spec'

      # NOTE: This may be removed if these models start getting their own helper methods, validators, etc
      add_filter 'app/models/above_lending'

      # rails-generated superclass files
      add_filter '/app/jobs/application_job.rb'
      add_filter '/app/mailers/application_mailer.rb'
      add_filter '/app/models/application_record.rb'
      add_filter '/app/models/above_lending_record.rb'

      # Ignore ActiveAdmin default files
      add_filter '/app/admin/dashboard.rb'
      add_filter '/app/admin/admin_users.rb'

      # ActiveAdmin Concerns are not automatically covered
      add_filter '/app/admin/concerns'

      # Ignore check commerce env vars
      add_filter 'app/services/check_commerce_service/configuration.rb'

      add_filter '/lib'

      # Ignore funding_packages
      add_filter '/app/services/funding_packages'

      Dir['app/*'].each do |dir|
        add_group File.basename(dir).humanize, dir
      end

      minimum_coverage(100.00)
    end
    SimpleCov.collate(Dir['./coverage_results/**/.resultset*.json'])
  end
end
