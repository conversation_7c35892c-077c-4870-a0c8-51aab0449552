# frozen_string_literal: true

require 'webmock/rspec'
require 'capybara'
require 'sidekiq/testing'
require 'support/helpers/stub_feature_flags'
require 'site_prism'
require 'site_prism/all_there'
require_relative 'support/helpers/remote_selenium'
require_relative 'support/helpers/common_methods'
require_relative 'support/capybara_extensions'

Capybara.register_driver :chrome_headless do |app|
  options = Selenium::WebDriver::Chrome::Options.new(args: %w[headless disable-gpu disable-dev-shm-usage no-sandbox])
  options.add_preference(:download, prompt_for_download: false, default_directory: './')
  options.add_preference(:browser, set_download_behavior: { behavior: 'allow' })
  options.add_emulation(device_metrics: { width: 2880, height: 1800, touch: false })

  driver = Capybara::Selenium::Driver.new(app, browser: :chrome, options: options)
  bridge = driver.browser.send(:bridge)

  path = "/session/#{bridge.session_id}/chromium/send_command"
  bridge.http.call(:post, path, cmd: 'Page.setDownloadBehavior',
                                params: {
                                  behavior: 'allow',
                                  downloadPath: './'
                                })
  driver
end

Capybara.javascript_driver = :chrome
Capybara.server = :puma, { Silent: true }
Capybara.default_max_wait_time = 5

RSpec.configure do |config|
  config.include StubFeatureFlags

  config.expect_with :rspec do |expectations|
    expectations.include_chain_clauses_in_custom_matcher_descriptions = true
  end

  config.mock_with :rspec do |mocks|
    mocks.verify_partial_doubles = true
  end

  config.shared_context_metadata_behavior = :apply_to_host_groups
  config.filter_run_when_matching :focus
  config.example_status_persistence_file_path = 'spec/examples.txt'
  config.disable_monkey_patching!
  config.warnings = false
  config.default_formatter = 'doc' if config.files_to_run.one?
  config.profile_examples = 10
  config.order = :random
  config.filter_run_excluding :internal     if ENV['HOST']
  config.filter_run_excluding :stagingfixme if ENV['HOST'] == 'staging'
  config.include CapybaraExtensions, type: :feature

  Kernel.srand config.seed

  # System test configs
  config.before(:each, type: :system) do
    driven_by :rack_test
  end

  config.before(:each, type: :feature) do
    Capybara.default_host = 'http://www.example.com'

    case ENV['BROWSER']
    when 'remote_mobile'
      Capybara.current_driver = :browserstack_mobile
      ENV['mobile'] = 'true'
    when 'mobile'
      Capybara.current_driver = :mobile_headless
      ENV['mobile'] = 'true'
    when 'desktop'
      Capybara.current_driver = :selenium
    else
      Capybara.current_driver = :chrome_headless
    end

    if !ENV['HOST']
      ENV['HOST'] = 'dev'
    elsif ENV['HOST'] != 'dev'
      ENV['USE_API'] = 'true'
      Capybara.run_server = false
      Capybara.app_host = "https://#{ENV['HOST']}.abovelending.com"
    end

    if run_on_remote_selenium?
      Capybara.register_driver :remote_selenium do |app|
        options = []
        options << 'headless' if ENV['HEADLESS']
        Capybara::Selenium::Driver.new(
          app,
          browser: :remote,
          url: "http://#{remote_selenium_host}:4444/wd/hub",
          capabilities: [Selenium::WebDriver::Chrome::Options.new(args: options)]
        )
      end
      Capybara.current_driver = :remote_selenium
      Capybara.javascript_driver = :remote_selenium
      Capybara.run_server = true
      Capybara.server_host = 'runner'
      Capybara.server_port = 3001
      Capybara.app_host = 'http://runner:3001'
    end

    Capybara.ignore_hidden_elements = false
  end

  config.before(:each, type: :feature) do
    WebMock.allow_net_connect!
  end

  config.before(:each) do
    # Disable all feature flags by default
    # Use StubFeatureFlags methods to stub feature flags
    stub_all_feature_flags(false)

    MailerConfiguration.all_mailer_actions.each do |action_name|
      MailerConfiguration.find_or_create_by(name: action_name, active: true).update(emails: Faker::Internet.email)
    end
  end

  config.around :example, :tz do |example|
    Time.use_zone(example.metadata[:tz]) { example.run }
  end

  config.after(:each, type: :feature) do
    Capybara.ignore_hidden_elements = true
    WebMock.disable_net_connect!
  end

  config.after(:suite) do
    # to allow selenium to delete session
    WebMock.allow_net_connect!
  end
end
