# frozen_string_literal: true

require 'rails_helper'

RSpec.describe EventSubscriber do
  it 'delegates to DatadogSpanTagger' do
    event = instance_double(
      'ActiveSupport::Notifications::Event',
      payload: { _event_name: 'test_event', _payload: { success: true, message: 'hoorah' } }
    )

    expect(DatadogSpanTagger).to receive(:add_event_tags_to_span).with('test_event', { success: true, message: 'hoorah' })
    described_class.new.notify(event)
  end

  it 'filters pii' do
    payload_with_pii = {
      success: true,
      email: '<EMAIL>',
      recipient: '<EMAIL>',
      nested: { ssn: 12_345, etc: [1, { last_name: 'hi', phone_number: '+****************' }] }
    }
    event = instance_double(
      'ActiveSupport::Notifications::Event',
      payload: { _event_name: 'test_event', _payload: payload_with_pii }
    )

    expected_payload = {
      success: true,
      email: '[FILTERED]',
      recipient: '[FILTERED]',
      nested: { ssn: '[FILTERED]', etc: [1, { last_name: '[FILTERED]', phone_number: '[FILTERED]' }] }
    }

    expect(DatadogSpanTagger).to receive(:add_event_tags_to_span).with('test_event', expected_payload)
    described_class.new.notify(event)
  end

  it 'does not filter perceived pii if the field is designated as admin' do
    payload_with_pii = {
      success: true,
      admin_email: '<EMAIL>',
      recipient: '<EMAIL>',
      nested: { ssn: 12_345, etc: [1, { last_name: 'hi', phone_number: '+****************' }] }
    }
    event = instance_double(
      'ActiveSupport::Notifications::Event',
      payload: { _event_name: 'test_event', _payload: payload_with_pii }
    )

    expected_payload = {
      success: true,
      admin_email: '<EMAIL>',
      recipient: '[FILTERED]',
      nested: { ssn: '[FILTERED]', etc: [1, { last_name: '[FILTERED]', phone_number: '[FILTERED]' }] }
    }

    expect(DatadogSpanTagger).to receive(:add_event_tags_to_span).with('test_event', expected_payload)
    described_class.new.notify(event)
  end
end
