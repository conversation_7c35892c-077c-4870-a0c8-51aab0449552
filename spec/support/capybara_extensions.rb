# frozen_string_literal: true

module CapybaraExtensions
  def click_on_and_wait(*args, **kwargs)
    click_on(*args, **kwargs)
    sleep 0.5
  end
end

module Capybara
  module SessionExtensions
    def wait_until_stable(locator)
      element = find(locator)
      Capybara.using_wait_time(Capybara.default_max_wait_time) do
        initial_rect = element.rect
        sleep 0.5
        final_rect = element.rect
        final_rect == initial_rect
      end
    end

    def ensure_sidebar_visible
      window_width = driver.execute_script('return window.screen.width;')
      sidebar_location = find('#sidebar').native.location.x
      return if sidebar_location < window_width

      # Move the sidebar to ensure it is visible within the screen area.
      driver.execute_script("return $('#sidebar')[0].style.left = '#{window_width - 50}px'")
    end
  end
end

Capybara::Session.include Capybara::SessionExtensions
