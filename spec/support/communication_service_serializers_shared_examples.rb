# frozen_string_literal: true

RSpec.shared_examples 'a communications service serializer' do |required_loan_pro_fields|
  describe 'attribution' do
    it 'may produce borrower attribution' do
      expect(described_class.attribute_borrower('borrower-id')).to eq(id: 'borrower-id', type: 'BOR<PERSON>WER')
    end

    it 'may produce loan attribution' do
      expect(described_class.attribute_loan('loan-id')).to eq(id: 'loan-id', type: 'LOAN')
    end
  end

  describe 'loan pro data validation' do
    required_loan_pro_fields.each do |required_field|
      it "raises an exception when #{required_field} is missing" do
        expect(valid_loan_pro_data).to receive(required_field).at_least(:once).and_return(nil)

        if defined?(additional_data)
          expect do
            described_class.call(loan_pro_data: valid_loan_pro_data, **additional_data)
          end.to raise_error(CommunicationsService::Serializers::SerializationError)
        else
          expect do
            described_class.call(loan_pro_data: valid_loan_pro_data)
          end.to raise_error(CommunicationsService::Serializers::SerializationError)
        end
      end
    end
  end
end
