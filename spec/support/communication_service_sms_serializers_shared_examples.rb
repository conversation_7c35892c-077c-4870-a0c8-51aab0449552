# frozen_string_literal: true

RSpec.shared_examples 'a communications service sms serializer' do
  describe 'phone validation' do
    let(:subject) do
      if defined?(additional_data)
        described_class.call(loan_pro_data: valid_loan_pro_data, **additional_data)
      else
        described_class.call(loan_pro_data: valid_loan_pro_data)
      end
    end

    it 'accepts phone numbers with a us country code' do
      expect(loan_pro_data).to receive(:phone_number).at_least(:once).and_return('+15553334444')
      expect(JSON.parse(subject).fetch('recipient')).to eq('15553334444')
    end

    it 'accepts US phone numbers without a country code' do
      expect(loan_pro_data).to receive(:phone_number).at_least(:once).and_return('(*************')
      expect(JSON.parse(subject).fetch('recipient')).to eq('15553334444')
    end

    it 'raises a serialization error when phone number is malformed' do
      expect(loan_pro_data).to receive(:phone_number).at_least(:once).and_return('333-4444')

      expect do
        subject
      end.to raise_error(CommunicationsService::Serializers::SerializationError, /Unable to coerce phone to valid format/)
    end

    it 'raises a serialization error when phone number is non-US-based' do
      expect(loan_pro_data).to receive(:phone_number).at_least(:once).and_return('+61-************')

      expect do
        subject
      end.to raise_error(CommunicationsService::Serializers::SerializationError, /Unable to coerce phone to valid format/)
    end
  end
end
