# frozen_string_literal: true

RSpec.shared_examples 'a decision engine report' do
  let(:type) { described_class::TYPE }
  let(:matching_document) { build(:funding_validation_funding_document, type:) }
  let(:subject) { described_class.new(funding_document: matching_document) }
  let(:raw_report) { "<?xml version='1.0'?><report>SAMPLE REPORT BODY 1</report>" }

  describe '.initialize_from' do
    it 'initializes an instance of the class' do
      funding_documents = [
        build(:funding_validation_funding_document),
        build(:funding_validation_funding_document),
        matching_document
      ]

      instance = described_class.initialize_from(funding_documents)
      expect(instance).to be_an_instance_of(described_class)
      expect(instance.funding_document).to eq(matching_document)
    end

    it 'raises an error if there are 0 funding documents for a type' do
      funding_documents = [
        build(:funding_validation_funding_document),
        build(:funding_validation_funding_document)
      ]

      expect do
        described_class.initialize_from(funding_documents)
      end.to raise_error(described_class::InitializationError, /Missing document of type/i)
    end

    it 'raises an error if there are multiple funding documents for a type' do
      funding_documents = [
        build(:funding_validation_funding_document),
        build(:funding_validation_funding_document),
        build(:funding_validation_funding_document, type:),
        matching_document
      ]

      expect do
        described_class.initialize_from(funding_documents)
      end.to raise_error(described_class::InitializationError, /Multiple documents of type/i)
    end
  end

  describe '#type' do
    it 'returns the document type' do
      expect(subject.type).to eq(type)
    end

    it 'is a supported document type' do
      expect(FundingValidation::FundingDocument::TYPES.values).to include(subject.type)
    end
  end

  describe '#report' do
    let(:s3_client) { instance_double(Aws::S3::Client) }
    let(:get_object_response) { instance_double(Aws::S3::Types::GetObjectOutput, body: StringIO.new(raw_report)) }

    it 'presents the underlying xml report' do
      get_object_args = {
        bucket: matching_document.storage_bucket,
        key: matching_document.storage_key
      }
      expect(s3_client).to receive(:get_object).with(**get_object_args).and_return(get_object_response)
      expect(Aws::S3::Client).to receive(:new).and_return(s3_client)

      expect(subject.report).to be_an_instance_of(REXML::Document)
      expect(subject.report.to_s).to eq(raw_report)
    end
  end
end
