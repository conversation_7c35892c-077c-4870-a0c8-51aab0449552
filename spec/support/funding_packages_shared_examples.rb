# frozen_string_literal: true

RSpec.shared_examples 'a funding package' do
  describe '.ready_to_package?' do
    it 'is ready when loan has all required data' do
      funding_validation = create(:funding_validation)
      funding = create(:funding, funding_validation:)
      loan = create(:loan, funding:)
      subject = described_class.new(loan:)

      expect(subject).to be_ready_to_package
    end

    it 'is not ready when no loan is present' do
      subject = described_class.new(loan: nil)

      expect(subject).not_to be_ready_to_package
    end

    it 'is not ready when loan has no borrower' do
      loan = create(:loan, borrower: nil)
      subject = described_class.new(loan:)

      expect(subject).not_to be_ready_to_package
    end

    it 'is not ready when loan has no funding' do
      loan = create(:loan, funding: nil)
      subject = described_class.new(loan:)

      expect(subject).not_to be_ready_to_package
    end

    it 'is not ready when loan funding has no funding validation' do
      funding = create(:funding, funding_validation: nil)
      loan = create(:loan, funding:)
      subject = described_class.new(loan:)

      expect(subject).not_to be_ready_to_package
    end
  end
end
