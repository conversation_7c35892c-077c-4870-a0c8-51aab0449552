# frozen_string_literal: true

def pages_folder
  File.expand_path('../../pages', __dir__)
end

def instantiate_pages
  # This will create the Page Objects for each of the files in the pages folder
  Dir["#{pages_folder}/*.rb"].each do |path|
    page = path.split('/')[-1].sub('.rb', '')
    define_method page do
      Object.const_get(page.to_s.split('_').map(&:capitalize).join).new
    end
  end
end

def require_pages_and_sections
  Dir["#{pages_folder}/**_page.rb"].each { |f| require_relative f }
end
