# frozen_string_literal: true

module Helpers
  module DecisionEngineInput # rubocop:disable Metrics/ModuleLength
    INFORMATIVE_RESEARCH = {
      bankruptcy_count: '0',
      charge_offs_within_36_months_count: '0',
      charge_offs_within_6_months_count: '0',
      derogatory_trade_count: '0',
      past_due_60_days_count: '0',
      petitioned_bankruptcy_count: '0',
      recent_auto_delinquency_3_months_count: '0',
      recent_auto_delinquency_6_months_count: '0',
      recent_bankruptcy_within_7_years_count: '0',
      recent_inquiries_within_3_months_count: '0',
      recent_inquiries_within_6_months_count: '0',
      recent_mortgage_delinquency_3_months_count: '0',
      recent_mortgage_delinquency_6_months_count: '0',
      revolving_credit_limit: '0',
      revolving_unpaid_balance: '0'
    }.freeze

    def decision_engine_input_parser(informative_research: nil)
      report_args = {}.tap do |h|
        h[:informative_research] = informative_research if informative_research.present?
      end

      parser = CrbOnboarding::DecisionEngineDocuments::DecisionEngineInput.new(
        funding_document: FundingValidation::FundingDocument.new
      )
      parser.raw_report = decision_engine_input_report(**report_args)
      parser
    end

    # rubocop:disable Metrics/MethodLength
    def decision_engine_input_report(informative_research: INFORMATIVE_RESEARCH)
      values = INFORMATIVE_RESEARCH.merge(informative_research)

      <<-XML
        <?xml version="1.0" encoding="UTF-8"?>
        <!DOCTYPE decisiondocument [<!ELEMENT v (id)><!ATTLIST v id ID #REQUIRED>]>
        <decisiondocument xmlns:xsi="http://www.w3.org/2001/XMLSchemainstance">
          <block name="system">
            <subdictionary name="main">
              <v id="ReturnSystemBlock">
                <item>main</item>
              </v>
              <v id="ReturnInputBlock">
                <item>main</item>
                <item>Applicant</item>
                <item>TU</item>
                <item>LN Risk View</item>
                <item>Clarity Clear Bank Behavior</item>
                <item>Application</item>
                <item>Errors Block</item>
              </v>
              <v id="ReturnInternalBlock">
                <item>main</item>
                <item>Champion Challenger</item>
              </v>
              <v id="ReturnDecisionBlock">
                <item>main</item>
                <item>Adverse Action</item>
                <item>Offers</item>
                <item>Data Source Call Flags</item>
                <item>Reuse Thresholds</item>
                <item>Error Block</item>
                <item>Versions</item>
                <item>Alternative Option</item>
                <item>Verification Tasks</item>
                <item>Informative</item>
              </v>
            </subdictionary>
          </block>
          <block name="input">
            <subdictionary name="Applicant">
              <v id="address_street">409 GLENWOOD</v>
              <v id="amount_financed">20619.4</v>
              <v id="annual_income">185000</v>
              <v id="beyond_payment_amount">620.26</v>
              <v id="beyond_payment_frequency">bi_weekly</v>
              <v id="city">West Edchester</v>
              <v id="date_of_birth">1962-06-29</v>
              <v id="education_level">bachelors</v>
              <v id="email"><EMAIL></v>
              <v id="employment_industry">financial_services</v>
              <v id="employment_start_date">01/01/2019</v>
              <v id="employment_status">employed_full_time</v>
              <v id="first_name">Elden40892</v>
              <v id="housing_status">own_with_mortgage</v>
              <v id="last_name">Daniel</v>
              <v id="monthly_housing_payment">1000</v>
              <v id="months_since_enrollment">19</v>
              <v id="ssn">666012955</v>
              <v id="state">IN</v>
              <v id="time_at_residence">more_than_3_years</v>
              <v id="zip_code">94025</v>
            </subdictionary>
            <subdictionary name="Application">
              <v id="credit_score_range">excellent</v>
            </subdictionary>
            <subdictionary name="Credible"/>
            <subdictionary name="Errors Block">
              <v id="Informative_connection_error">N</v>
              <v id="Informative_report_error">N</v>
              <v id="InformativeEXP_report_error">N</v>
            </subdictionary>
            <subdictionary name="ExistingOffers"/>
            <subdictionary name="gIdentify"/>
            <subdictionary name="gVerify"/>
            <subdictionary name="Informative Research">
              <v id="IR_Consumer_Deceased_Ind">N</v>
              <v id="IR_CreditFile_SSNVariation">666012955</v>
              <v id="IR_CreditHistory_LessThan2Years_Ind_DM12">N</v>
              <v id="IR_CreditHistory_LessThan3Years_Ind">N</v>
              <v id="IR_CreditHistory_LessThan5Years_Ind_APL12">N</v>
              <v id="IR_CreditHistory_LessThan7Years_Ind_DM12">N</v>
              <v id="IR_CreditHistoryLessThan5Years_Ind">N</v>
              <v id="IR_Defferment_Plan_Mortgage_or_Auto_Ind">N</v>
              <v id="IR_Fico8ReasonDescription1">Serious delinquency</v>
              <v id="IR_Fico8ReasonDescription2">Proportion of balances to credit limits is too high on bank revolving or other revolving accounts</v>
              <v id="IR_Fico8ReasonDescription3">Number of accounts with delinquency</v>
              <v id="IR_Fico8ReasonDescription4">Length of time accounts have been established</v>
              <v id="IR_FICO_Beacon8">617</v>
              <v id="IR_FrozenFile_Ind">N</v>
              <v id="IR_NoHit_Ind">N</v>
              <v id="IR_Num_60DaysPastdue_DM14">#{values[:past_due_60_days_count]}</v>
              <v id="IR_Num_ActiveTradelines_180Days_NonDelinquent">5</v>
              <v id="IR_Num_Bankruptcy_DM11">0</v>
              <v id="IR_Num_Bankruptcy_UPL11">#{values[:bankruptcy_count]}</v>
              <v id="IR_Num_ChargeOffers_Within36Months_UPL17">#{values[:charge_offs_within_36_months_count]}</v>
              <v id="IR_Num_ChargeOffers_Within6Months_DM17">#{values[:charge_offs_within_6_months_count]}</v>
              <v id="IR_Num_DerogatoryTrades_DM13">#{values[:derogatory_trade_count]}</v>
              <v id="IR_Num_Installment_or_Revolving_3Months">0</v>
              <v id="IR_Num_PetitionedBankruptcy">#{values[:petitioned_bankruptcy_count]}</v>
              <v id="IR_Num_RecentAutoDelinquency_3Months">#{values[:recent_auto_delinquency_3_months_count]}</v>
              <v id="IR_Num_RecentAutoDeliquency_6Months">#{values[:recent_auto_delinquency_6_months_count]}</v>
              <v id="IR_Num_RecentAutoLoans_Within180Days">0</v>
              <v id="IR_Num_RecentBankruptcy_Within7Years">#{values[:recent_bankruptcy_within_7_years_count]}</v>
              <v id="IR_Num_RecentDelinquency_UPL14">0</v>
              <v id="IR_Num_RecentInquiries_180Days">0</v>
              <v id="IR_Num_RecentInquiries_LessThan3Months_DM16">#{values[:recent_inquiries_within_3_months_count]}</v>
              <v id="IR_Num_RecentInquiries_LessThan6Months_UPL16">#{values[:recent_inquiries_within_6_months_count]}</v>
              <v id="IR_Num_RecentInstallmentTrades_Within180Days">0</v>
              <v id="IR_Num_RecentMortgageDelinquency_3Months">#{values[:recent_mortgage_delinquency_3_months_count]}</v>
              <v id="IR_Num_RecentMortgageDeliquency_6Months">#{values[:recent_mortgage_delinquency_6_months_count]}</v>
              <v id="IR_Num_RecentRetailTrades_Within180Days">0</v>
              <v id="IR_Num_RecentRevolving_or_InstallmentInquiries_3Months">0</v>
              <v id="IR_Num_RecentRevolvingTradelines_Within180Days">0</v>
              <v id="IR_Num_RecentStudentLoans_Within180Days">0</v>
              <v id="IR_Num_Reposession_or_Forclosure_WithinLast7Years">0</v>
              <v id="IR_Num_Reposession_or_Foreclosure_Last12Months">0</v>
              <v id="IR_Num_TradesOpenedWithin180Days">0</v>
              <v id="IR_Sum_Installment_Tradelines_MonthlyPayments_DM19">26</v>
              <v id="IR_Sum_Mortgage_Tradelines_MonthlyPayments_DM19">1004</v>
              <v id="IR_Sum_Of_Installment_Tradelines">26</v>
              <v id="IR_Sum_Of_Mortgage_Monthly_Payments">1004</v>
              <v id="IR_Sum_Of_Revolving_Tradelines">0</v>
              <v id="IR_Sum_RevolvingCreditLimitAmount_DM15">#{values[:revolving_credit_limit]}</v>
              <v id="IR_Sum_RevolvingTradelines_MonthlyPayments_DM19">0</v>
              <v id="IR_Sum_RevolvingUpaidBalanceAmount_DM15">#{values[:revolving_unpaid_balance]}</v>
            </subdictionary>
            <subdictionary name="main">
              <v id="addBankSteps">Step 1</v>
              <v id="borrowerReportsSteps">Step 1</v>
              <v id="callType">decisionRun</v>
              <v id="credibleCallbackPrequalifiedOffersSteps">Step 1</v>
              <v id="decisionRunSteps">Step 3</v>
              <v id="existing_plaid_access_token_ind">N</v>
              <v id="fetchCodeSteps">Step 1</v>
              <v id="fetchLeadsSteps">Step 1</v>
              <v id="fetchTokenSteps">Step 1</v>
              <v id="finalDecisionSteps">Step 1</v>
              <v id="GDS_Environment">UAT</v>
              <v id="getInfoSteps">Step 1</v>
              <v id="getIRTradelinesSteps">Step 1</v>
              <v id="getOffersSteps">Step 1</v>
              <v id="getOffersSteps_DM">Step 1</v>
              <v id="getOffersSteps_IPL">Step 1</v>
              <v id="getOfferSteps_BD">Step 1</v>
              <v id="getOfferSteps_Organic">Step 1</v>
              <v id="getOfferSteps_UPL">Step 1</v>
              <v id="getPlaidAssetsSteps">Step 1</v>
              <v id="getTasksSteps">Step 1</v>
              <v id="getTilCopySteps">Step 1</v>
              <v id="hardCreditPullSteps">Step 1</v>
              <v id="newLoanAppSteps">Step 1</v>
              <v id="patchLoanAppSteps">Step 1</v>
              <v id="postOfferCredibleSteps">Step 1</v>
              <v id="saveSelectionSteps">Step 1</v>
              <v id="sendOffersSteps">Step 1</v>
              <v id="sendVerificationCodeEmailSteps">Step 1</v>
              <v id="submitDocumentSteps">Step 1</v>
              <v id="syncBorrowerInfoSteps">Step 1</v>
              <v id="syncStatusSteps">Step 1</v>
              <v id="syncTasksSteps">Step 1</v>
              <v id="triggerOnboardingEmailSteps">Step 1</v>
              <v id="updateBankSteps">Step 1</v>
              <v id="verificationRequirementsSteps">Step 1</v>
            </subdictionary>
            <subdictionary name="Offers">
              <v id="amount_financed">20619.4</v>
            </subdictionary>
            <subdictionary name="Plaid"/>
            <subdictionary name="Socure ID"/>
          </block>
        </decisiondocument>
      XML
    end
    # rubocop:enable Metrics/MethodLength

    def stub_decision_engine_input!(funding_documents:, parser: decision_engine_input_parser, enforce: true)
      if enforce
        expect(CrbOnboarding::DecisionEngineDocuments::DecisionEngineInput).to(
          receive(:initialize_from).with(funding_documents).and_return(parser)
        )
      else
        allow(CrbOnboarding::DecisionEngineDocuments::DecisionEngineInput).to(
          receive(:initialize_from).with(funding_documents).and_return(parser)
        )
      end
    end
  end
end
