# frozen_string_literal: true

module Helpers
  module DecisionEngineOutput # rubocop:disable Metrics/ModuleLength
    VERSION_POLICY = '1.4.3'
    DEBT_TO_INCOME_RATIO = '0.1234'

    def decision_engine_output_parser_with_loan(loan,
                                                version_policy: nil)
      report_args = {}.tap do |h|
        h[:version_policy] = version_policy if version_policy.present?
      end
      parser = CrbOnboarding::DecisionEngineDocuments::DecisionEngineOutput.new(loan:,
                                                                                funding_document: FundingValidation::FundingDocument.new)

      parser.raw_report = decision_engine_output_report(**report_args)
      parser
    end

    def decision_engine_output_parser(
      version_policy: nil
    )
      report_args = {}.tap do |h|
        h[:version_policy] = version_policy if version_policy.present?
      end

      parser = CrbOnboarding::DecisionEngineDocuments::DecisionEngineOutput.new(
        funding_document: FundingValidation::FundingDocument.new
      )
      parser.raw_report = decision_engine_output_report(**report_args)
      parser
    end

    def decision_engine_output_report(version_policy: VERSION_POLICY, debt_to_income_ratio: DEBT_TO_INCOME_RATIO)
      <<-XML
        <?xml version="1.0" encoding="UTF-8"?>
        <!DOCTYPE decisiondocument [  <!ELEMENT v (id)><!ATTLIST v id ID #REQUIRED>]>
        <decisiondocument xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
          <block name="system">
            <subdictionary name="main">
              <v id="ClientTCPIP">127.0.0.1</v>
              <v id="SystemDate">2023-02-23</v>
              <v id="SystemId">D4-33-11-6A-F0-1D-16-CE</v>
              <v id="Error2">0</v>
              <v id="Error3">0</v>
              <v id="Error1">0</v>
              <v id="WriteDecisionHistory">Y</v>
              <v id="Error4">0</v>
              <v id="StepByStep">N</v>
              <v id="SystemTime">20:47:15</v>
            </subdictionary>
            <subdictionary name="Champion Challenger">
              <v id="ch-ch-01">59.7445</v>
              <v id="ch-ch-03">43.6784</v>
              <v id="ch-ch-02">90.0476</v>
            </subdictionary>
          </block>
          <block name="input">
            <subdictionary name="TU"/>
            <subdictionary name="Errors Block">
              <v id="Informative_report_error">N</v>
              <v id="InformativeEXP_report_error">N</v>
              <v id="Informative_connection_error">N</v>
            </subdictionary>
            <subdictionary name="main">
              <v id="getIRTradelinesSteps">Step 1</v>
              <v id="hardCreditPullSteps">Step 1</v>
              <v id="newLoanAppSteps">Step 1</v>
              <v id="callType">decisionRun</v>
              <v id="getInfoSteps">Step 1</v>
              <v id="patchLoanAppSteps">Step 1</v>
              <v id="getOffersSteps_IPL">Step 1</v>
              <v id="fetchCodeSteps">Step 1</v>
              <v id="fetchLeadsSteps">Step 1</v>
              <v id="postOfferCredibleSteps">Step 1</v>
              <v id="getTilCopySteps">Step 1</v>
              <v id="fetchTokenSteps">Step 1</v>
              <v id="saveSelectionSteps">Step 1</v>
              <v id="sendOffersSteps">Step 1</v>
              <v id="finalDecisionSteps">Step 1</v>
              <v id="submitDocumentSteps">Step 1</v>
              <v id="triggerOnboardingEmailSteps">Step 1</v>
              <v id="getPlaidAssetsSteps">Step 1</v>
              <v id="InProgramLoanSteps">Step 1</v>
              <v id="existing_plaid_access_token_ind">N</v>
              <v id="syncBorrowerInfoSteps">Step 1</v>
              <v id="decisionRunSteps">Step 3</v>
              <v id="getOfferSteps_UPL">Step 1</v>
              <v id="updateBankSteps">Step 1</v>
              <v id="appByPhoneIndicator">N</v>
              <v id="syncTasksSteps">Step 1</v>
              <v id="addBankSteps">Step 1</v>
              <v id="getTasksSteps">Step 1</v>
              <v id="getOffersSteps">Step 1</v>
              <v id="existingOffersChecksSteps">Step 1</v>
              <v id="getOfferSteps_BD">Step 1</v>
              <v id="getOffersSteps_DM">Step 1</v>
              <v id="sendVerificationCodeEmailSteps">Step 1</v>
              <v id="GDS_Environment">UAT</v>
              <v id="syncStatusSteps">Step 1</v>
              <v id="credibleCallbackPrequalifiedOffersSteps">Step 1</v>
              <v id="getOfferSteps_Organic">Step 1</v>
              <v id="borrowerReportsSteps">Step 1</v>
              <v id="verificationRequirementsSteps">Step 1</v>
            </subdictionary>
            <subdictionary name="Application">
              <v id="credit_score_range">excellent</v>
              <v id="loan_creation_date">1800-01-01</v>
              <v id="loan_purpose">debt_consolidation</v>
            </subdictionary>
            <subdictionary name="Applicant">
              <v id="city">West Edchester</v>
              <v id="date_of_birth">1962-06-29</v>
              <v id="time_at_residence">more_than_3_years</v>
              <v id="zip_code">94025</v>
              <v id="beyond_payment_amount">620.2600</v>
              <v id="ssn">666012955</v>
              <v id="beyond_payment_frequency">bi_weekly</v>
              <v id="monthly_housing_payment">1000</v>
              <v id="employment_status">employed_full_time</v>
              <v id="state">IN</v>
              <v id="employment_industry">financial_services</v>
              <v id="first_name">Elden40892</v>
              <v id="email"><EMAIL></v>
              <v id="annual_income">185000</v>
              <v id="housing_status">own_with_mortgage</v>
              <v id="employment_start_date">1800-01-01</v>
              <v id="last_name">Daniel</v>
              <v id="address_street">409 GLENWOOD</v>
              <v id="education_level">bachelors</v>
              <v id="months_since_enrollment">19</v>
            </subdictionary>
          </block>
          <block name="internal">
            <subdictionary name="main">
              <v id="i_aa_Description"/>
              <v id="i_end_de_Ind">Y</v>
              <v id="i_aa_ID"/>
            </subdictionary>
            <subdictionary name="Champion Challenger"/>
          </block>
          <block name="decision">
            <subdictionary name="Versions">
              <v id="de_version_policy">#{version_policy}</v>
              <v id="de_version_system">D4-33-11-6A-F0-1D-16-CE</v>
            </subdictionary>
            <subdictionary name="Adverse Action">
              <v id="de_aa_Decision">Approved</v>
            </subdictionary>
            <subdictionary name="Verification Tasks"/>
            <subdictionary name="Reuse Thresholds"/>
            <subdictionary name="main">
              <v id="de_patchLoanAppSteps">Step 1</v>
              <v id="de_addBankSteps">Step 1</v>
              <v id="de_net_monthly_income">11563</v>
              <v id="de_originating_party">CRB</v>
              <v id="de_product_type">Other</v>
              <v id="de_getTasksSteps">Step 1</v>
              <v id="de_sendVerificationCodeEmailSteps">Step 1</v>
              <v id="de_updateBankSteps">Step 1</v>
              <v id="de_sendOffersSteps">Step 1</v>
              <v id="de_ApplicationStatus">offered</v>
              <v id="de_finalDecisionSteps">Step 1</v>
              <v id="de_borrowerReportsSteps">Step 1</v>
              <v id="de_credibleCallbackPrequalifiedOffersSteps">Step 1</v>
              <v id="de_syncStatusSteps">Step 1</v>
              <v id="de_monthly_mortage">1004</v>
              <v id="de_syncBorrowerInfoSteps">Step 1</v>
              <v id="de_newLoanAppSteps">Step 1</v>
              <v id="de_fetchCodeSteps">Step 1</v>
              <v id="de_getOfferSteps_UPL">Step 1</v>
              <v id="de_callType">decisionRun</v>
              <v id="de_monthly_income">15417</v>
              <v id="de_hardCreditPullSteps">Step 1</v>
              <v id="de_getInfoSteps">Step 1</v>
              <v id="de_getOfferSteps_BD">Step 1</v>
              <v id="de_getOffers_Steps_IPL">Step 1</v>
              <v id="de_getIRTradelinesSteps">Step 1</v>
              <v id="de_getOffers_Steps_PPC">Step 1</v>
              <v id="de_syncTasksSteps">Step 1</v>
              <v id="de_monthly_debt_calculation">2374</v>
              <v id="de_triggerOnboardingEmailSteps">Step 1</v>
              <v id="de_fetchLeadsSteps">Step 1</v>
              <v id="de_getOfferSteps_Organic">Step 1</v>
              <v id="de_submitDocumentsSteps">Step 1</v>
              <v id="de_decisionRunSteps">Step 5</v>
              <v id="de_fetchTokenSteps">Step 1</v>
              <v id="de_offer_dti">#{debt_to_income_ratio}</v>
              <v id="de_postOfferCredibleSteps">Step 1</v>
              <v id="de_decision">offered</v>
              <v id="de_getTilCopySteps">Step 1</v>
              <v id="de_saveSelectionSteps">Step 1</v>
              <v id="de_endTransaction_Ind">N</v>
              <v id="de_verificationRequirementsSteps">Step 1</v>
              <v id="de_getPlaidAssetsSteps">Step 1</v>
              <v id="de_getOffers_Steps_DM">Step 1</v>
            </subdictionary>
            <subdictionary name="Data Source Call Flags">
              <v id="de_call_Credible_CreateLeadApp">N</v>
              <v id="de_call_Credible_GetLeadResults">N</v>
              <v id="de_call_DBLookup">N</v>
              <v id="de_call_Informative">N</v>
              <v id="de_call_Credible_CreateToken">N</v>
              <v id="de_call_Credible_SelectProduct">N</v>
              <v id="de_call_DBSave">Y</v>
              <v id="de_call_Credible_PrefillPrequal">N</v>
            </subdictionary>
            <subdictionary name="Offers">
              <v id="de_offer_principal_loan_amount">#22757.2632#22757.2632#21704.6316#21704.6316</v>
              <v id="de_offer_initial_term_payment">#552.5076#315.3284#526.9515#300.7429</v>
              <v id="de_offer_amount_financed">#21619.4000#21619.4000#20619.4000#20619.4000</v>
              <v id="de_offer_origination_fee_amount">#1137.8632#1137.8632#1085.2316#1085.2316</v>
              <v id="de_offer_originating_party">#CRB#CRB#CRB#CRB</v>
              <v id="de_offer_payment_frequency">#bi_weekly#bi_weekly#bi_weekly#bi_weekly</v>
              <v id="de_offer_description">#Best Matched Payment to Debt Resolution Program Deposit+#Lowest Payment (Longer Term)#Best Matched Payment to Debt Resolution Program Deposit+#Lowest Payment (Longer Term)</v>
              <v id="de_offer_interest_rate">#23.9000#23.9000#23.9000#23.9000</v>
              <v id="de_offer_final_term_payment">#552.5076#315.3284#526.9515#300.7429</v>
              <v id="de_offer_origination_fee_percent">#0.0500#0.0500#0.0500#0.0500</v>
              <v id="de_offer_hero_offer">#false#false#true#false</v>
              <v id="de_offer_term">#52#119#52#119</v>
              <v id="de_offer_cash_out_amount">#1000#1000#0#0</v>
              <v id="de_offer_id">#20:47:1598705.9194#20:47:1514209.2075#20:47:1596200.6243#20:47:1585423.7177</v>
              <v id="de_offer_settlement_amount">#20619.4000#20619.4000#20619.4000#20619.4000</v>
            </subdictionary>
            <subdictionary name="Informative">
              <v id="IR_LoginAccountIdentifier">abovelending</v>
              <v id="IR_LoginAccountPassword">altest</v>
              <v id="IR_InternalAccountIdentifier">2100022</v>
            </subdictionary>
            <subdictionary name="Alternative Option"/>
            <subdictionary name="Error Block">
              <v id="de_error_Type"/>
              <v id="de_error_Description"/>
              <v id="de_error_Name"/>
              <v id="de_error_Location"/>
            </subdictionary>
          </block>
        </decisiondocument>
      XML
    end

    def stub_decision_engine_output!(loan:, parser: decision_engine_output_parser, dti: 0.5765, enforce: true)
      if enforce
        expect(CrbOnboarding::DecisionEngineDocuments::DecisionEngineOutput).to(
          receive(:initialize_from_loan).with(loan).and_return(parser)
        )
      else
        allow(CrbOnboarding::DecisionEngineDocuments::DecisionEngineOutput).to(
          receive(:initialize_from_loan).with(loan).and_return(parser)
        )
      end

      allow(parser).to receive(:debt_to_income_ratio).and_return(dti)
    end
  end
end
