# frozen_string_literal: true

module Helpers
  module Giact
    OFAC = <<-XML
      <OfacListPotentialMatches>
        <OfacListData>
          <EntityId>407289</EntityId>
          <Summary>
            <PotentialMatchLevel>Conditional</PotentialMatchLevel>
            <PotentialMatchDetails>
              <string>FirstN<PERSON> matched and LastN<PERSON> was within 2 characters of being an exact match.</string>
            </PotentialMatchDetails>
            <PotentialMatchAka>false</PotentialMatchAka>
          </Summary>
          <Name>
            <ListItemName>La Croix Bandit</ListItemName>
            <FirstName>La Croix</FirstName>
            <LastName>Bandit</LastName>
          </Name>
          <DataSource>Politically Exposed Persons</DataSource>
          <ProgramNames>
            <string>PEP List: United States Of America</string>
          </ProgramNames>
          <Associations/>
        </OfacListData>
      </OfacListPotentialMatches>
    XML

    GIACT_DATA = {
      customer_response_code: 'N123'
    }.freeze

    def giact_parser(
      giact_data: nil,
      ofac: nil
    )
      report_args = {}.tap do |h|
        h[:giact_data] = giact_data if giact_data.present?
        h[:ofac] = ofac if ofac.present?
      end

      parser = CrbOnboarding::DecisionEngineDocuments::Giact.new(funding_document: FundingValidation::FundingDocument.new)
      parser.raw_report = giact_report(**report_args)

      parser
    end

    def giact_report(
      giact_data: GIACT_DATA,
      ofac: OFAC
    )
      values = GIACT_DATA.merge(giact_data)
      <<-XML
         <Identify>
           <PostInquiryResult>
             <ItemReferenceId>**********</ItemReferenceId>
             <CreatedDate>2023-02-23T14:47:56.9543341-06:00</CreatedDate>
             <VerificationResponse>NoData</VerificationResponse>
             <AccountResponseCode>ND00</AccountResponseCode>
             <BankName>UNLISTED TEST BANK</BankName>
             <AccountAddedDate/>
             <AccountLastUpdatedDate/>
             <AccountClosedDate/>
             <FundsConfirmationResult/>
             <CustomerResponseCode>#{values[:customer_response_code]}</CustomerResponseCode>
             #{ofac}
           </PostInquiryResult>
         </Identify>
      XML
    end

    def stub_giact!(funding_documents:, parser: giact_parser, enforce: true)
      if enforce
        expect(CrbOnboarding::DecisionEngineDocuments::Giact).to(
          receive(:initialize_from).with(funding_documents).and_return(parser)
        )
      else
        allow(CrbOnboarding::DecisionEngineDocuments::Giact).to(
          receive(:initialize_from).with(funding_documents).and_return(parser)
        )
      end
    end
  end
end
