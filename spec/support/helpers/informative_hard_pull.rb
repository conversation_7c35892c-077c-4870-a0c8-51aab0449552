# frozen_string_literal: true

module Helpers
  module Informative<PERSON>ard<PERSON>ull
    def informative_hard_pull_parser
      parser = CrbOnboarding::DecisionEngineDocuments::InformativeHardPull.new(funding_document: FundingValidation::FundingDocument.new)
      parser.raw_report = informative_hard_pull_report

      parser
    end

    def informative_hard_pull_report
      <<-XML
         <InformativeHardPullStub />
      XML
    end

    def stub_informative_hard_pull!(funding_documents:, parser: informative_hard_pull_parser, enforce: true)
      if enforce
        expect(CrbOnboarding::DecisionEngineDocuments::InformativeHardPull).to(
          receive(:initialize_from).with(funding_documents).and_return(parser)
        )
      else
        allow(CrbOnboarding::DecisionEngineDocuments::InformativeHardPull).to(
          receive(:initialize_from).with(funding_documents).and_return(parser)
        )
      end
    end
  end
end
