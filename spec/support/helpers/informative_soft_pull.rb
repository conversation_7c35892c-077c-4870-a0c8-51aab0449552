# frozen_string_literal: true

module Helpers
  module InformativeSoftPull
    CREDIT_DATA = {
      credit_score: 617,
      credit_retrieval_date: '2020-10-28',
      date_of_birth: '2000-01-01',
      first_name: '<PERSON> of',
      last_name: 'The Century',
      ssn: '001020003',
      street_address: '123 State St',
      zipcode: '78909'
    }.freeze

    EMBEDDED_DOC = Base64.encode64(
      <<-PDF
        %PDF-1.4
        %????
        1 0 obj
        <<
        /Title (Informative Research)
        /Author (Informative Research)
      PDF
    )

    FACTORS = [
      'Serious delinquency',
      'Proportion of balances to credit limits is too high on bank revolving or other revolving accounts',
      'Number of accounts with delinquency',
      'Length of time accounts have been established'
    ].freeze

    def informative_soft_pull_parser(
      credit_data: nil,
      embedded_doc: nil,
      factors: nil
    )
      report_args = {}.tap do |h|
        h[:credit_data] = credit_data if credit_data.present?
        h[:embedded_doc] = embedded_doc if embedded_doc.present?
        h[:factors] = factors if factors.present?
      end

      parser = CrbOnboarding::DecisionEngineDocuments::InformativeSoftPull.new(
        funding_document: FundingValidation::FundingDocument.new
      )
      parser.raw_report = informative_soft_pull_report(**report_args)
      parser
    end

    def informative_soft_pull_report(
      credit_data: CREDIT_DATA,
      embedded_doc: EMBEDDED_DOC,
      factors: FACTORS,
      bankruptcies: []
    )
      values = CREDIT_DATA.merge(credit_data)

      report_template = Rails.root.join('spec/fixtures/files/decision_engine_documents/informative_credit_report.xml.erb').read
      erb_template = ERB.new(report_template)
      b = binding
      b.local_variable_set(:credit_data, credit_data)
      b.local_variable_set(:embedded_doc, embedded_doc)
      b.local_variable_set(:factors, factors)
      b.local_variable_set(:values, values)
      b.local_variable_set(:bankruptcies, bankruptcies)
      erb_template.result(b)
    end

    def stub_informative_soft_pull!(funding_documents:, parser: informative_soft_pull_parser, enforce: true)
      if enforce
        expect(CrbOnboarding::DecisionEngineDocuments::InformativeSoftPull).to(
          receive(:initialize_from).with(funding_documents).and_return(parser)
        )
      else
        allow(CrbOnboarding::DecisionEngineDocuments::InformativeSoftPull).to(
          receive(:initialize_from).with(funding_documents).and_return(parser)
        )
      end
    end
  end
end
