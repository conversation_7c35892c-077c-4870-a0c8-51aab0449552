# frozen_string_literal: true

module Helpers
  module Login
    def create_user(trait)
      FactoryBot.create :admin_user, trait, email: '<EMAIL>', password: 'password'
    end

    def log_in(username = '<EMAIL>', password = 'password', validate_login: true)
      visit '/admin'
      fill_in 'admin_user_email', with: username
      fill_in 'admin_user_password', with: password
      first('#admin_user_submit_action > input').click

      return unless validate_login

      expect(page).to have_current_path('/admin')
      expect(page).to have_content('Signed in successfully.')
    end

    def create_and_login_admin
      user = create_user(:admin)
      log_in(user.email, user.password)
      user
    end
  end
end
