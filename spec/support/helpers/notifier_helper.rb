# frozen_string_literal: true

module Not<PERSON><PERSON><PERSON><PERSON>
  def expect_to_notify(name, success: nil, fail_reason: nil, meta: nil, message: nil, extra: {})
    expected_message = {
      _event_name: name,
      _payload: hash_including({ success:, fail_reason:, meta:, message:, **extra }.compact)
    }

    expect(ActiveSupport::Notifications)
      .to have_received(:instrument)
      .with('notify.event', expected_message)
      .at_least(:once)
  end

  def expect_not_to_notify(name)
    expect(ActiveSupport::Notifications)
      .not_to have_received(:instrument)
      .with('notify.event', hash_including(_event_name: name))
  end

  def set_notifier_stubs
    allow(ActiveSupport::Notifications).to receive(:instrument).and_call_original
  end
end
