# frozen_string_literal: true

module Helpers
  module Socure
    GLOBAL_WATCHLIST = <<-XML
      <reasonCodes>test-reason-code</reasonCodes>
      <matches/>
    XML

    KYC = <<-XML
      <reasonCodes>test-reason-code</reasonCodes>
      <fieldValidation>
        <firstName>0.99</firstName>
        <surName>0.99</surName>
        <streetAddress>0.99</streetAddress>
        <city>0.99</city>
        <state>0.99</state>
        <zip>0.99</zip>
        <dob>0.99</dob>
        <ssn>0.99</ssn>
      </fieldValidation>
    XML

    NAME_ADDRESS_CORRELATION = <<-XML
      <reasonCodes>test-reason-code</reasonCodes>
      <reasonCodes>test-reason-code2</reasonCodes>
      <score>0.99</score>
    XML

    def socure_parser(
      global_watchlist: nil,
      kyc: nil,
      name_address_correlation: nil
    )
      report_args = {}.tap do |h|
        h[:global_watchlist] = global_watchlist if global_watchlist.present?
        h[:kyc] = kyc if kyc.present?
        h[:name_address_correlation] = name_address_correlation if name_address_correlation.present?
      end

      parser = CrbOnboarding::DecisionEngineDocuments::Socure.new(funding_document: FundingValidation::FundingDocument.new)
      parser.raw_report = socure_report(**report_args)

      parser
    end

    def socure_report(
      global_watchlist: GLOBAL_WATCHLIST,
      kyc: KYC,
      name_address_correlation: NAME_ADDRESS_CORRELATION
    )
      <<-XML
        <Response>
          <referenceId>fd678718-c836-45fb-b78d-36eb044eee2e</referenceId>
          #{name_address_correlation.present? && "<nameAddressCorrelation>#{name_address_correlation}</nameAddressCorrelation>"}
          <nameEmailCorrelation>
            <reasonCodes>I558</reasonCodes>
            <reasonCodes>I557</reasonCodes>
            <reasonCodes>I556</reasonCodes>
            <score>0.99</score>
          </nameEmailCorrelation>
          <namePhoneCorrelation>
            <reasonCodes>I618</reasonCodes>
            <reasonCodes>I622</reasonCodes>
            <reasonCodes>I621</reasonCodes>
            <score>0.99</score>
          </namePhoneCorrelation>
          <fraud>
            <reasonCodes>I553</reasonCodes>
            <reasonCodes>I121</reasonCodes>
            <reasonCodes>I127</reasonCodes>
            <scores>
              <name>sigma</name>
              <version>2.0</version>
              <score>0.21</score>
            </scores>
          </fraud>
          #{kyc.present? && "<kyc>#{kyc}</kyc>"}
          <addressRisk>
            <reasonCodes>I704</reasonCodes>
            <reasonCodes>I720</reasonCodes>
            <reasonCodes>I707</reasonCodes>
            <score>0.01</score></addressRisk>
          <emailRisk>
            <reasonCodes>I520</reasonCodes>
            <reasonCodes>I556</reasonCodes>
            <score>0.01</score>
          </emailRisk>
          <phoneRisk>
            <reasonCodes>I611</reasonCodes>
            <reasonCodes>I609</reasonCodes>
            <reasonCodes>I616</reasonCodes>
            <score>0.01</score>
          </phoneRisk>
          #{global_watchlist.present? && "<globalWatchlist>#{global_watchlist}</globalWatchlist>"}
          <alertList/>
        </Response>
      XML
    end

    def stub_socure!(funding_documents:, parser: socure_parser, enforce: true)
      if enforce
        expect(CrbOnboarding::DecisionEngineDocuments::Socure).to(
          receive(:initialize_from).with(funding_documents).and_return(parser)
        )
      else
        allow(CrbOnboarding::DecisionEngineDocuments::Socure).to(
          receive(:initialize_from).with(funding_documents).and_return(parser)
        )
      end
    end
  end
end
