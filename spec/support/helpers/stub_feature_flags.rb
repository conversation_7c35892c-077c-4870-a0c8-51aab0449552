# frozen_string_literal: true

module StubFeatureFlags
  # Enable/disable Flipper globally
  # Disabled by default in spec/spec_helper.rb
  def stub_all_feature_flags(value)
    allow(Flipper).to receive(:enabled?).and_return(value)
  end

  # Unstub any global stubs
  # aka undo `stub_all_feature_flags`
  # More details at the end of the post
  def unstub_all_feature_flags
    RSpec::Mocks.space.proxy_for(Flipper).reset
  end

  # Enable/disable Flipper for a specific feature for any actors
  # e.g.: stub_feature_flag(:allow_cat_to_have_snacks, true)
  def stub_feature_flag(feature_name, value)
    allow(Flipper).to receive(:enabled?).with(feature_name, anything).and_return(value)
    allow(Flipper).to receive(:enabled?).with(feature_name).and_return(value)
  end

  # Enable/disable Flipper for a specific feature and actor
  # e.g.: stub_feature_flag_for_actor(:allow_cat_to_have_snacks, cat, true)
  def stub_feature_flag_for_actor(feature_name, actor, value)
    allow(Flipper).to receive(:enabled?).with(feature_name, actor).and_return(value)
  end
end
