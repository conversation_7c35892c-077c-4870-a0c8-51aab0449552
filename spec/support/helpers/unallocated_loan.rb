# frozen_string_literal: true

def create_loan_details(details = {})
  Array.wrap(details).map { |detail| create_unallocated_loan_record(detail) }
end

# rubocop:disable Metrics/MethodLength
def create_unallocated_loan_record(loan = {})
  LoanAllocation::UnallocatedLoanRecord.new({
    unified_id: '1', # must match csv field string
    loan_id: Faker::Number.number(digits: 5),
    loan_amount: 15_000,
    borrower_id: 123_321,
    contract_date: Date.yesterday.to_s,
    state: 'al',
    product: 'IPL',
    interest_rate: 5,
    originator: 'CRB',
    additional_funds_flag: 1,
    payment_adherence_ratio_3_months: 1,
    payment_adherence_ratio_4_months: 1,
    payment_adherence_ratio_6_months: 1,
    payment_increase_percentage: 30,
    nsfs_4_months: 1,
    nsfs_6_months: 1,
    nsfs_9_months: 0,
    nsfs_12_months: 0,
    nsfs_18_months: 0,
    nsfs_24_months: 0,
    nsfs_lifetime: 1,
    borrower_age: 19,
    apr: 29,
    origination_fee: 150,
    origination_fee_percent: 5,
    term: 60,
    months_at_beyond: 5,
    days_past_due: 30,
    external_dti: 25,
    credit_score: 670,
    cashout_amount: 0,
    consecutive_payments_count: 10,
    payment_frequency: 'loan.frequency.monthly',
    bankruptcy_filed_date: 1.year.ago.to_date.to_s,
    unpaid_principal_balance: 1_000,
    payment_to_income: 35,
    credit_inquiries_last_6_months: 1,
    residual_income: 1000,
    hard_inquiries_60_days: 2,
    hard_inquiries_90_days: 3,
    credit_model_cohort: 'champion',
    credit_model_level: 'n/a',
    champion_decision: 'OFFERED',
    challenger_decision: 'FRONT_END_DECLINED'
  }.merge(loan))
end
# rubocop:enable Metrics/MethodLength
