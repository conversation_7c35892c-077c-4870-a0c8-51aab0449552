# frozen_string_literal: true

RSpec.shared_context 'loanpro_data_common' do
  let(:loan_id) { rand(10_000..100_000) }
  let(:loanpro_id) { rand(10_000..100_000) }
  let(:loanpro_data) do
    {
      '__metadata': {
        'uri': "http://loanpro.simnang.com/api/public/api/1/odata.svc/Loans(id=#{loan_id})",
        'type': 'Entity.Loan'
      },
      'Insurance': {
        '__deferred': {
          'uri': "Loans(id=#{loan_id})/Insurance"
        }
      },
      'CustomFieldValues': {
        '__deferred': {
          'uri': "Loans(id=#{loan_id})/CustomFieldValues"
        }
      },
      'ChecklistItemValues': {
        '__deferred': {
          'uri': "Loans(id=#{loan_id})/ChecklistItemValues"
        }
      },
      'Documents': {
        '__deferred': {
          'uri': "Loans(id=#{loan_id})/Documents"
        }
      },
      'Collateral': {
        '__deferred': {
          'uri': "Loans(id=#{loan_id})/Collateral"
        }
      },
      'LoanSettings': {
        '__deferred': {
          'uri': "Loans(id=#{loan_id})/LoanSettings"
        }
      },
      'LoanSetup': {
        '__metadata': {
          'uri': "http://loanpro.simnang.com/api/public/api/1/odata.svc/LoanSetup(id=#{loanpro_id})",
          'type': 'Entity.LoanSetup'
        },
        'CustomFieldValues': {
          '__deferred': {
            'uri': "LoanSetup(id=#{loanpro_id})/CustomFieldValues"
          }
        },
        'id': loanpro_id,
        'loanId': loan_id,
        'modId': 0,
        'active': 0,
        'apr': '25.5164',
        'aprForceSingle': 0,
        'payment': '144.69',
        'origFinalPaymentDate': '/Date(1809907200)/',
        'origFinalPaymentAmount': '145.59',
        'tilFinanceCharge': '7215.78',
        'tilTotalOfPayments': '17325.21',
        'tilLoanAmount': '10109.43',
        'tilSalesPrice': '17325.21',
        'tilPaymentSchedule': [{ 'count': 119, 'payment': 145.59, 'startDate': '10/31/2022' }].to_json,
        'regzCustomEnabled': 0,
        'regzApr': '0.0000',
        'regzFinanceCharge': '0.00',
        'regzAmountFinanced': '0.00',
        'regzTotalOfPayments': '0.00',
        'loanAmount': '10109.43',
        'discount': '0.00',
        'underwriting': '532.08',
        'loanRate': '22.9000',
        'loanRateType': 'loan.rateType.annually',
        'loanTerm': '119.0000',
        'moneyFactor': '0',
        'residual': '0.00',
        'contractDate': '/Date(**********)/',
        'firstPaymentDate': '/Date(1667174400)/',
        'scheduleRound': '-0.15',
        'amountDown': '0.00',
        'reserve': '0.00',
        'salesPrice': '0.00',
        'gap': '0.00',
        'warranty': '0.00',
        'dealerProfit': '0.00',
        'taxes': '0.00',
        'creditLimit': '0.00',
        'reportingCreditLimit': '0.00',
        'loanClass': 'loan.class.consumer',
        'loanType': 'loan.type.installment',
        'discountSplit': 1,
        'paymentFrequency': 'loan.frequency.biWeekly',
        'calcType': 'loan.calcType.simpleInterest',
        'daysInYear': 'loan.daysInYear.actual',
        'interestApplication': 'loan.interestApplication.betweenTransactions',
        'begEnd': 'loan.begend.end',
        'firstPeriodDays': 'loan.firstPeriodDays.actual',
        'firstDayInterest': 1,
        'discountCalc': 'loan.discountCalc.straightLine',
        'diyAlt': 0,
        'dueDateOnLastDOM': 0,
        'dueDatesOnBusinessDays': 'loan.businessduedates.disabled',
        'daysInPeriod': 'loan.daysinperiod.30',
        'roundDecimals': 5,
        'lastAsFinal': 0,
        'nddCalc': 'loan.nddCalc.standard',
        'endInterest': 'loan.endInterest.no',
        'scheduleTemplate': 4,
        'curtailmentTemplate': 0,
        'feesPaidBy': 'loan.feesPaidBy.date',
        'useInterestTiers': 0,
        'calcHistoryEnabled': 0,
        'calcDatesEnabled': 0,
        'graceDays': 5,
        'lateFeeType': 'loan.lateFee.2',
        'lateFeeAmount': '0.00',
        'lateFeePercent': '10.00',
        'lateFeeCalc': 'loan.lateFeeCalc.standard',
        'lateFeePercentBase': 'loan.latefeepercentbase.regular',
        'rollLastPayment': 0,
        'paymentDateApp': 'loan.pmtdateapp.actual',
        'suspendForecastTo': nil,
        'isSetupValid': true,
        'usuryAlert': nil,
        'maxInterestAmount': nil,
        'financeChargeAsMIA': nil
      },
      'Notes': {
        '__deferred': {
          'uri': "Loans(id=#{loan_id})/Notes"
        }
      },
      'Promises': {
        '__deferred': {
          'uri': "Loans(id=#{loan_id})/Promises"
        }
      },
      'Bankruptcies': {
        '__deferred': {
          'uri': "Loans(id=#{loan_id})/Bankruptcies"
        }
      },
      'Charges': {
        '__deferred': {
          'uri': "Loans(id=#{loan_id})/Charges"
        }
      },
      'Payments': {
        '__deferred': {
          'uri': "Loans(id=#{loan_id})/Payments"
        }
      },
      'LoanFunding': {
        '__deferred': {
          'uri': "Loans(id=#{loan_id})/LoanFunding"
        }
      },
      'Advancements': {
        '__deferred': {
          'uri': "Loans(id=#{loan_id})/Advancements"
        }
      },
      'Credits': {
        '__deferred': {
          'uri': "Loans(id=#{loan_id})/Credits"
        }
      },
      'DueDateChanges': {
        '__deferred': {
          'uri': "Loans(id=#{loan_id})/DueDateChanges"
        }
      },
      'CurtailmentDates': {
        '__deferred': {
          'uri': "Loans(id=#{loan_id})/CurtailmentDates"
        }
      },
      'StatusArchive': {
        '__deferred': {
          'uri': "Loans(id=#{loan_id})/StatusArchive"
        }
      },
      'Transactions': {
        '__deferred': {
          'uri': "Loans(id=#{loan_id})/Transactions"
        }
      },
      'EscrowCalculatedTx': {
        '__deferred': {
          'uri': "Loans(id=#{loan_id})/EscrowCalculatedTx"
        }
      },
      'ScheduleRolls': {
        '__deferred': {
          'uri': "Loans(id=#{loan_id})/ScheduleRolls"
        }
      },
      'StopInterestDates': {
        '__deferred': {
          'uri': "Loans(id=#{loan_id})/StopInterestDates"
        }
      },
      'APDAdjustments': {
        '__deferred': {
          'uri': "Loans(id=#{loan_id})/APDAdjustments"
        }
      },
      'DPDAdjustments': {
        '__deferred': {
          'uri': "Loans(id=#{loan_id})/DPDAdjustments"
        }
      },
      'InterestAdjustments': {
        '__deferred': {
          'uri': "Loans(id=#{loan_id})/InterestAdjustments"
        }
      },
      'LoanModifications': {
        '__deferred': {
          'uri': "Loans(id=#{loan_id})/LoanModifications"
        }
      },
      'EscrowAdjustments': {
        '__deferred': {
          'uri': "Loans(id=#{loan_id})/EscrowAdjustments"
        }
      },
      'EscrowTransactions': {
        '__deferred': {
          'uri': "Loans(id=#{loan_id})/EscrowTransactions"
        }
      },
      'EscrowSubsetOptions': {
        '__deferred': {
          'uri': "Loans(id=#{loan_id})/EscrowSubsetOptions"
        }
      },
      'EscrowCalculators': {
        '__deferred': {
          'uri': "Loans(id=#{loan_id})/EscrowCalculators"
        }
      },
      'EstimatedDisbursements': {
        '__deferred': {
          'uri': "Loans(id=#{loan_id})/EstimatedDisbursements"
        }
      },
      'Loans': {
        '__deferred': {
          'uri': "Loans(id=#{loan_id})/Loans"
        }
      },
      'LinkedLoanValues': {
        '__deferred': {
          'uri': "Loans(id=#{loan_id})/LinkedLoanValues"
        }
      },
      'LoanChilds': {
        '__deferred': {
          'uri': "Loans(id=#{loan_id})/LoanChilds"
        }
      },
      'RecurrentCharges': {
        '__deferred': {
          'uri': "Loans(id=#{loan_id})/RecurrentCharges"
        }
      },
      'PayNearMeOrders': {
        '__deferred': {
          'uri': "Loans(id=#{loan_id})/PayNearMeOrders"
        }
      },
      'Customers': {
        '__deferred': {
          'uri': "Loans(id=#{loan_id})/Customers"
        }
      },
      'Portfolios': {
        '__deferred': {
          'uri': "Loans(id=#{loan_id})/Portfolios"
        }
      },
      'SubPortfolios': {
        '__deferred': {
          'uri': "Loans(id=#{loan_id})/SubPortfolios"
        }
      },
      'EscrowSubsets': {
        '__deferred': {
          'uri': "Loans(id=#{loan_id})/EscrowSubsets"
        }
      },
      'RuleApplied': {
        '__deferred': {
          'uri': "Loans(id=#{loan_id})/RuleApplied"
        }
      },
      'RuleAppliedLoanSettings': {
        '__deferred': {
          'uri': "Loans(id=#{loan_id})/RuleAppliedLoanSettings"
        }
      },
      'RuleAppliedChargeOff': {
        '__deferred': {
          'uri': "Loans(id=#{loan_id})/RuleAppliedChargeOff"
        }
      },
      'RuleAppliedAPDReset': {
        '__deferred': {
          'uri': "Loans(id=#{loan_id})/RuleAppliedAPDReset"
        }
      },
      'RuleAppliedChecklists': {
        '__deferred': {
          'uri': "Loans(id=#{loan_id})/RuleAppliedChecklists"
        }
      },
      'RuleAppliedChangeDueDates': {
        '__deferred': {
          'uri': "Loans(id=#{loan_id})/RuleAppliedChangeDueDates"
        }
      },
      'RuleAppliedStopInterest': {
        '__deferred': {
          'uri': "Loans(id=#{loan_id})/RuleAppliedStopInterest"
        }
      },
      'RuleAppliedAccountTools': {
        '__deferred': {
          'uri': "Loans(id=#{loan_id})/RuleAppliedAccountTools"
        }
      },
      'RuleAppliedCustomerTools': {
        '__deferred': {
          'uri': "Loans(id=#{loan_id})/RuleAppliedCustomerTools"
        }
      },
      'RuleAppliedAutopay': {
        '__deferred': {
          'uri': "Loans(id=#{loan_id})/RuleAppliedAutopay"
        }
      },
      'RuleAppliedLoanSetup': {
        '__deferred': {
          'uri': "Loans(id=#{loan_id})/RuleAppliedLoanSetup"
        }
      },
      'RuleAppliedBankruptcy': {
        '__deferred': {
          'uri': "Loans(id=#{loan_id})/RuleAppliedBankruptcy"
        }
      },
      'Autopays': {
        '__deferred': {
          'uri': "Loans(id=#{loan_id})/Autopays"
        }
      },
      'ActionResultNotes': {
        '__deferred': {
          'uri': "Loans(id=#{loan_id})/ActionResultNotes"
        }
      },
      'LoanFundingTransactions': {
        '__deferred': {
          'uri': "Loans(id=#{loan_id})/LoanFundingTransactions"
        }
      },
      'id': loan_id,
      'displayId': '********',
      'title': '********',
      'settingsId': rand(10_000..100_000),
      'setupId': loanpro_id,
      'insurancePolicyId': nil,
      'collateralId': 0,
      'linkedLoan': nil,
      'modId': nil,
      'modTotal': 0,
      'humanActivityDate': '/Date(**********)/',
      'created': '/Date(**********)/',
      'lastMaintRun': '/Date(0)/',
      'createdBy': 6546,
      'active': 1,
      'archived': 0,
      'loanAlert': nil,
      'temporaryAccount': 1,
      'deleted': 0,
      'deletedAt': nil,
      '_relatedMetadata': nil,
      '_dynamicProperties': nil
    }
  end
end
