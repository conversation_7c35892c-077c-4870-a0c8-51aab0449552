# frozen_string_literal: true

RSpec.shared_context 'onboard_loan_common' do
  let(:status) { AboveLending::LoanAppStatus::CRM_MAPPINGS.keys.sample }
  let!(:application_status) { create(:above_lending_loan_app_status, name: AboveLending::LoanAppStatus::CRM_MAPPINGS[status]) }
  let(:process_loan) { true }
  let(:installment_loan_agreement_document_params) do
    {
      bucket_name: 'some_bucket_name',
      document_type: 'INSTALLMENT_LOAN_AGREEMENT',
      signed_storage_key: "contract_documents/test/#{SecureRandom.uuid}/signed-1234567890.pdf",
      signed_at: Time.current.iso8601,
      file_size_bytes: rand(1_000..20_000),
      inputs: {
        address: Faker::Address.full_address,
        apr: rand(1.0..29.99).round(2).to_d,
        cash_back_amount: rand(1_000.0..2_000.0).round(2).to_d,
        contract_date: Date.tomorrow.iso8601,
        first_name: Faker::Name.first_name,
        interest_rate: rand(1.0..29.99).round(2).to_d,
        last_name: Faker::Name.last_name,
        loan_number: 'CRB0100001111',
        origination_fee_amount: rand(1_000.0..2_000.0).round(2).to_d,
        payment_due_on: Faker::Date.forward(days: 1).to_date.iso8601,
        zip_code: Faker::Address.zip_code.first(5)
      }
    }
  end
  let(:onboard_params) do
    {
      process_loan:,
      application: {
        account_number: Faker::Bank.account_number,
        account_type: 'checking',
        annual_income: Faker::Number.decimal,
        autopay_enabled: true,
        bank: Faker::Company.name,
        created_at: Faker::Time.backward(days: 5).iso8601,
        credit_score_number: 632,
        credit_score_range: Decision::Credit.new.score_range_values.values.sample,
        credit_score_retrieval_date: Faker::Date.backward(days: 1).iso8601,
        debt_to_income_ratio: 0.45,
        docusign_envelope_id: SecureRandom.uuid,
        employment_status: Application::Employment.new.status_values.values.sample,
        employment_industry: LoanManagement::ParamsSchema::Onboard::EMPLOYMENT_INDUSTRY.sample,
        employment_pay_frequency: Application::Employment.new.pay_frequency_values.values.sample,
        hard_credit_pull_requested_at: Faker::Date.backward(days: 2).iso8601,
        holder_firstname: Faker::Name.first_name,
        holder_lastname: Faker::Name.last_name,
        housing_payment_monthly: rand(500..1_500).to_d,
        noaa_successfully_sent_date: [Time.current.iso8601, nil].sample,
        routing_number: Faker::Bank.routing_number,
        ip_address: Faker::Internet.ip_v4_address,
        time_at_residence: Application::Housing.new.time_at_residence_values.values.sample,
        education_level: Application::Applicant.new.education_level_values.values.sample,
        status:,
        decline_reason_messages: Array.new(rand(1..5)) { Faker::Lorem.sentence }
      },

      borrower: {
        address_street: Faker::Address.full_address,
        beyond_enrollment_date: rand(1..5).years.ago.to_date.iso8601,
        beyond_payment_amount: rand(500..1_500).to_d,
        program_duration_in_tmonths: rand(1..12),
        city: Faker::Address.city,
        date_of_birth: Faker::Date.birthday.iso8601,
        email: Faker::Internet.email,
        first_name: Faker::Name.first_name,
        identity_id: SecureRandom.uuid,
        last_name: Faker::Name.last_name,
        phone_number: Faker::PhoneNumber.phone_number.first(10),
        program_id: SecureRandom.hex.first(12),
        service_entity_name: Application::DebtSettlementDetail.new.debt_resolution_program_name_values.values.sample,
        ssn: Faker::IdNumber.valid,
        state: Faker::Address.state_abbr,
        total_amount_enrolled_debt: rand(1_000...10_000),
        zip_code: Faker::Address.zip_code.first(5),
        married: true,
        consecutive_payments_count: rand(0..20),
        payment_adherence_ratio_3_months: Faker::Number.decimal,
        payment_adherence_ratio_6_months: Faker::Number.decimal
      },
      cft_account: {
        cft_account_balance: rand(5000..10_000).to_d,
        cft_account_holder_name: "#{Faker::Name.first_name} #{Faker::Name.last_name}",
        cft_account_number: Faker::Bank.account_number,
        cft_bank_name: Faker::Company.name,
        cft_routing_number: Faker::Bank.routing_number
      },
      offers: [{
        cashout_amount: rand(500..1_000).to_d,
        description: Faker::Lorem.sentence,
        final_term_payment: rand(50.0..100.0).round(2).to_d,
        initial_term_payment: rand(50.0..100.0).round(2).to_d,
        interest_rate: rand(5.0..25.0).round(2).to_d,
        is_hero: true,
        originating_party: Faker::Company.name,
        origination_fee: rand(100..1_000).to_d,
        origination_fee_percent: rand(1.0..5.0).round(2).to_d,
        policy_version: '1.2',
        selected: true,
        settlement_amount: rand(2500..10_000).to_d,
        term: rand(12..24).to_s
      }, {
        cashout_amount: rand(500..1_000).to_d,
        description: Faker::Lorem.sentence,
        final_term_payment: rand(50.0..100.0).round(2).to_d,
        initial_term_payment: rand(50.0..100.0).round(2).to_d,
        interest_rate: rand(5.0..25.0).round(2).to_d,
        is_hero: true,
        originating_party: Faker::Company.name,
        origination_fee: rand(100..1_000).to_d,
        origination_fee_percent: rand(1.0..5.0).round(2).to_d,
        policy_version: '1.2',
        selected: false,
        settlement_amount: rand(2500..10_000).to_d,
        term: rand(12..24).to_s
      }],
      verifications: [
        {
          document_type: 'ID',
          verification_reasons: 'identity,1903,FS01,Address was not provided at input',
          status: 'accepted'
        },
        {
          document_type: 'Bank Account',
          verification_reasons: 'bank,R907,FS06,SSN has been reported as deceased',
          status: 'pending'
        },
        {
          document_type: 'Income',
          verification_reasons: 'income,,IV01,Triggered based on certain rules regarding debt-to-income etc.',
          status: 'accepted'
        }
      ],
      verification_documents: [
        {
          id: '1',
          name: 'Bank Statement',
          mime_type: 'application/pdf',
          s3_bucket: 'abovelending',
          s3_key: '1-bank-statement.pdf',
          tags: ['Bank Account']
        },
        {
          id: '2',
          name: 'Payslip',
          mime_type: 'image/jpeg',
          s3_bucket: 'abovelending',
          s3_key: '2-payslip.pdf',
          tags: ['Income']
        },
        {
          id: '3',
          name: 'Drivers License',
          mime_type: 'image/jpeg',
          s3_bucket: 'abovelending',
          s3_key: '3-drivers-license.pdf',
          tags: ['ID']
        },
        {
          id: '4',
          name: 'Rental Agreement',
          mime_type: 'image/jpeg',
          s3_bucket: 'abovelending',
          s3_key: '4-rental-agreement.pdf',
          tags: ['Residence']
        }
      ],
      til_history: {
        docusign_envelope_id: SecureRandom.uuid,
        til_data: {
          loan: {
            contract_date: Time.zone.now.strftime('%Y-%m-%d'),
            agreement_date: Time.zone.now.strftime('%Y-%m-%d'),
            apr: "#{rand(1.0..99.99).round(2)}%"
          },
          payment_schedule: build_payment_schedule
        }
      },
      contract_documents: [
        installment_loan_agreement_document_params
      ],
      consent_documents: [
        {
          document_type: 'PRIVACY_POLICY',
          s3_key: 'consent_documents/test/privacy-policy.pdf',
          s3_bucket: 'some_bucket_name',
          signed_at: Time.now.iso8601,
          ip_address: Faker::Internet.ip_v4_address,
          inputs: {
            device: 'telepathy'
          }
        },
        {
          document_type: 'TERMS_OF_USE',
          s3_key: 'consent_documents/test/terms-of-use.pdf',
          s3_bucket: 'some_bucket_name',
          signed_at: Time.now.iso8601,
          ip_address: Faker::Internet.ip_v4_address,
          inputs: {
            device: 'telepathy'
          }
        },
        {
          document_type: 'ESIGN_ACT_CONSENT',
          s3_key: 'consent_documents/test/esign-act-consent.pdf',
          s3_bucket: 'some_bucket_name',
          signed_at: Time.now.iso8601,
          ip_address: Faker::Internet.ip_v4_address,
          inputs: {
            device: 'telepathy'
          }
        },
        {
          document_type: 'CREDIT_PROFILE_AUTHORIZATION',
          s3_key: 'consent_documents/test/credit-profile-authorization.pdf',
          s3_bucket: 'some_bucket_name',
          signed_at: Time.now.iso8601,
          ip_address: Faker::Internet.ip_v4_address,
          inputs: {
            device: 'telepathy'
          }
        }
      ],
      decision_engine_documents: [
        {
          created_at: Time.now.iso8601,
          file_size_bytes: rand(1_000..20_000),
          mime_type: 'application/xml',
          name: 'informative_soft_pull',
          storage_bucket: 'borrower_report_bucket',
          storage_key: "borrower_reports/test/#{SecureRandom.uuid}/informative_soft_pull-123456.xml"
        },
        {
          created_at: Time.now.iso8601,
          file_size_bytes: rand(1_000..20_000),
          mime_type: 'application/xml',
          name: 'socure',
          storage_bucket: 'borrower_report_bucket',
          storage_key: "borrower_reports/test/#{SecureRandom.uuid}/socure-123456.xml"
        },
        {
          created_at: Time.now.iso8601,
          file_size_bytes: rand(1_000..20_000),
          mime_type: 'application/xml',
          name: 'giact',
          storage_bucket: 'borrower_report_bucket',
          storage_key: "borrower_reports/test/#{SecureRandom.uuid}/giact-123456.xml"
        }
      ],
      funding: {
        processing_system: 'Dash'
      }
    }
  end

  def build_payment_schedule
    list = []

    start_date = Time.zone.now
    number_of_payments = rand(2..20)
    amount = rand(100.0..999.99).round(2).to_f

    list << {
      amount: "$#{amount}",
      number_of_payments: number_of_payments,
      start_date: "Monthly payments starting on #{start_date.strftime('%m/%d/%Y')}",
      raw_start_date: start_date.strftime('%m/%d/%Y')
    }

    start_date = Time.zone.now + number_of_payments.months
    number_of_payments = rand(1..5)
    amount = rand(100.0..999.99).round(2).to_f

    list << {
      amount: "$#{amount}",
      number_of_payments: number_of_payments,
      start_date: start_date.strftime('%m/%d/%Y'),
      raw_start_date: start_date.strftime('%m/%d/%Y')
    }

    list
  end

  def create_templates(multiple_versions: false)
    templates = []
    (onboard_params[:consent_documents] + onboard_params[:contract_documents]).each do |doc|
      if multiple_versions
        create(:template, detail_info: {
                 type: doc[:document_type],
                 version: 1,
                 name: "#{doc[:document_type]}_one"
               })
      end
      templates.push create(:template, detail_info: {
                              type: doc[:document_type],
                              version: 2,
                              name: "#{doc[:document_type]}_two"
                            })
    end
    templates
  end
end
