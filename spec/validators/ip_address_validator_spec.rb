# frozen_string_literal: true

require 'rails_helper'

RSpec.describe IpAddressValidator, type: :validator do
  include Shoulda::Matchers
  before do
    stub_const('Validatable', Class.new).class_eval do
      include ActiveModel::Attributes
      include ActiveModel::Validations
      attribute :ip_address, :string
      validates :ip_address, ip_address: true
    end
  end

  subject { Validatable.new }

  it { should allow_value('127.0.0.1').for(:ip_address) }
  it { should_not allow_value('cheerios').for(:ip_address) }
  it { should_not allow_value(nil).for(:ip_address) }

  describe 'with custom message' do
    before do
      stub_const('Validatable', Class.new).class_eval do
        include ActiveModel::Attributes
        include ActiveModel::Validations
        attribute :ip_address, :string
        validates :ip_address, ip_address: { message: 'not frozen yoghurt' }
      end
    end

    it 'validates attribute is ip address' do
      subject.ip_address = 'candyfloss'
      expect(subject.valid?).to eq(false)
      expect(subject.errors[:ip_address]).to eq(['not frozen yoghurt'])
    end
  end
end
