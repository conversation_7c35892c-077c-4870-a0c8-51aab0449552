# frozen_string_literal: true

require 'rails_helper'

RSpec.describe UuidValidator, type: :validator do
  subject { DummyModel.new }
  let!(:klass) do
    klass = Class.new
    klass.class_eval do
      include ActiveModel::Model
      attr_accessor :uuid_attr

      validates :uuid_attr, uuid: true
    end
    stub_const('DummyModel', klass)
    klass
  end

  it 'validates attribute is uuid' do
    subject.uuid_attr = SecureRandom.uuid
    expect(subject.valid?).to eq(true)
  end

  it 'provides corresponding error in case attribute is not valid' do
    subject.uuid_attr = 'whatever'
    expect(subject.valid?).to eq(false)
    expect(subject.errors.to_hash).to eq(uuid_attr: ['is not a valid UUID'])
  end
end
