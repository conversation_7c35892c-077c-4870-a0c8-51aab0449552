---
openapi: 3.0.1
info:
  title: API V1
  version: v1
components:
  securitySchemes:
    bearer:
      description: An internal access token. Available at .env as DASH_ACCESS_TOKEN
      type: http
      scheme: bearer
      bearerFormat: plain
paths:
  "/api/loans":
    post:
      summary: Create a Loan
      description: 'Create the initial loan record for later processing.

        '
      tags:
      - Loans
      operationId: create
      security:
      - bearer: []
      parameters: []
      responses:
        '200':
          description: Loan is successfully created. The response contains the loan
            terms for the created loan
        '422':
          description: Loan is already processed. 422 error is returned
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                process_loan:
                  type: boolean
                  example: true
                loan:
                  type: object
                  properties:
                    unified_id:
                      type: string
                      example: '12345678'
                    request_id:
                      type: string
                      example: 424e9570-8382-425b-8661-e20215ce0a1f
                    amount_financed:
                      type: number
                      example: 1000.99
                    cashout_amount:
                      type: number
                      example: 1000
                    contract_date:
                      type: string
                      example: '2025-03-24'
                    first_payment_date:
                      type: string
                      example: '2025-04-24'
                    interest_rate:
                      type: number
                      example: 13.0
                    number_of_payments:
                      type: integer
                      example: 52
                    origination_fee:
                      type: number
                      example: 150.1
                    payment_frequency:
                      type: string
                      enum:
                      - monthly
                      - bi_weekly
                      - semi_monthly
                      - weekly
                      example: monthly
                    product_type:
                      type: string
                      enum:
                      - UPL
                      - IPL
                      - DM
                      - PPC
                      example: UPL
                    purpose:
                      type: string
                      example: debt_consolidation
                    settlement_amount:
                      type: number
                      example: 5000.5
                    source_type:
                      type: string
                      enum:
                      - BEYOND
                      - CRMM
                      example: BEYOND
                    loanpro_data:
                      type: object
                      example:
                        id: 824b7612-c43f-4426-b34c-13b86bbb8ce4
                        LoanSetup:
                          origFinalPaymentDate: "/Date(1809907200)/"
                          apr: '25.5164'
                          tilFinanceCharge: '7215.78'
                          tilPaymentSchedule:
                          - count: 119
                            payment: 145.59
                            startDate: 10/31/2022
  "/api/loans/{unified_id}":
    patch:
      summary: Update a Loan
      description: ''
      tags:
      - Loans
      operationId: update
      security:
      - bearer: []
      parameters:
      - name: unified_id
        in: path
        description: The unified_id of the loan to update.
        required: true
        schema:
          type: string
      responses:
        '200':
          description: Loan is successfully updated. The response contains the loan
            terms for the created loan
        '422':
          description: Loan is already processed. 422 error is returned
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                process_loan:
                  type: boolean
                  example: true
                loan:
                  type: object
                  properties:
                    unified_id:
                      type: string
                      example: '12345678'
                    request_id:
                      type: string
                      example: 424e9570-8382-425b-8661-e20215ce0a1f
                    amount_financed:
                      type: number
                      example: 1000.99
                    cashout_amount:
                      type: number
                      example: 1000
                    contract_date:
                      type: string
                      example: '2025-03-24'
                    first_payment_date:
                      type: string
                      example: '2025-04-24'
                    interest_rate:
                      type: number
                      example: 13.0
                    number_of_payments:
                      type: integer
                      example: 52
                    origination_fee:
                      type: number
                      example: 150.1
                    payment_frequency:
                      type: string
                      enum:
                      - monthly
                      - bi_weekly
                      - semi_monthly
                      - weekly
                      example: monthly
                    product_type:
                      type: string
                      enum:
                      - UPL
                      - IPL
                      - DM
                      - PPC
                      example: UPL
                    purpose:
                      type: string
                      example: debt_consolidation
                    settlement_amount:
                      type: number
                      example: 5000.5
                    source_type:
                      type: string
                      enum:
                      - BEYOND
                      - CRMM
                      example: BEYOND
                    loanpro_data:
                      type: object
                      example:
                        id: 824b7612-c43f-4426-b34c-13b86bbb8ce4
                        LoanSetup:
                          origFinalPaymentDate: "/Date(1809907200)/"
                          apr: '25.5164'
                          tilFinanceCharge: '7215.78'
                          tilPaymentSchedule:
                          - count: 119
                            payment: 145.59
                            startDate: 10/31/2022
                    loan_id:
                      type: string
                      example: f54e300e-4205-4d5c-93da-1dc6cf1392f9
  "/api/loans/{unified_id}/onboard":
    post:
      summary: Onboard a Loan
      description: |
        This method onboards a loan. When onboarding, both legacy (abovelending) and new models (dash) are created.

        Explanation of parameters:

          - `verifications`: Verifications for the verification_documents uploaded by the user.
              Each `verification.verification_reasons` has a comma separated string of reasons that is used by `verification_documents.tags` to link
              the verification with the specific document.
              The different document types can be found [here](https://github.com/Above-Lending/dash/blob/23ef0869b4a6b13d61da5fd781ff66179d22e6be/app/services/loan_management/onboard_loans/prepare_document_info.rb#L6).

              NB: When onboarding, the new models (dash) assume that all verifications have been accepted.
          - `verification_documents`: The verification documents uploaded by the user. They are linked to the verification records by the content of the `tags` property.
          - `contract_documents`: The signed contracts, signed in docusign by the user. Following a successful signing, this endpoint is called to onboard the loan.
          - `consent_documents`: Consent documents are the legal documents that the user implicitly agrees to by using the platform. They include the ip_address of the user.
          - `offers`: All offers presented to the user.
          - `offer`: The selected offer (deprecated parameter, `offers` have a property `selected` for this functionality).
          - `application`: All information related to the loan application.
          - `borrower`: All information related to the borrower.
          - `til_history`: Truth in Lending history.

        The documents in `verification_documents`, `contract_documents` and `consent_documents` are all stored in S3.

        To successfully onboard a loan, it's payload should include the following document types in  `contracts_documents`:

        - TRUTH_IN_LENDING
        - INSTALLMENT_LOAN_AGREEMENT

        and the following document types in `consent_documents`:

        - ESIGN_ACT_CONSENT
        - PRIVACY_POLICY
        - CREDIT_PROFILE_AUTHORIZATION
        - ELECTRONIC_FUND_TRANSFER_AUTH
        - TERMS_OF_USE

        specific for the state of Maryland:

        - MARYLAND_CREDIT_SERVICES_CONTRACT
        - MARYLAND_NOTICE_OF_CANCELLATION

        Depending on the loan type, this list may vary.
      tags:
      - Loans
      operationId: update
      security:
      - bearer: []
      parameters:
      - name: unified_id
        in: path
        description: The unified id of the legacy loan record. Corresponds to `loan_number`
          in the new loan model.
        required: true
        schema:
          type: string
      responses:
        '201':
          description: Successfully onboarded the loan. The response only contains
            the id of the newly created legacy loan record
        '400':
          description: In case of a failure, response contains details about it
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                loan_id:
                  type: string
                  example: 1ea7a05a-7af2-4782-aa28-1f80faf742d6
                offers:
                  type: array
                  items:
                    type: object
                    properties:
                      cashout_amount:
                        type: number
                        example: 1000
                      description:
                        type: string
                        example: Description of a particular offer
                      final_term_payment:
                        type: number
                        example: 75.0
                      finance_amount:
                        type: number
                        example: 1200.45
                      initial_term_payment:
                        type: number
                        example: 75.0
                      interest_rate:
                        type: number
                        example: 7.0
                      is_hero:
                        type: boolean
                        example: true
                      originating_party:
                        type: string
                        example: Some Company Name
                      origination_fee:
                        type: number
                        example: 400.0
                      origination_fee_percent:
                        type: number
                        example: 2.5
                      policy_version:
                        type: string
                        example: '1.2'
                      selected:
                        type: boolean
                        example: true
                      settlement_amount:
                        type: number
                        example: 4000
                      term:
                        type: integer
                        example: 18
                application:
                  type: object
                  properties:
                    account_number:
                      type: string
                      example: '**********'
                    account_type:
                      type: string
                      example: checking
                    annual_income:
                      type: integer
                      example: 100000
                    autopay_enabled:
                      type: boolean
                      example: true
                    bank:
                      type: string
                      example: Bank Name Inc
                    created_at:
                      type: string
                      example: '2025-03-24T19:10:33Z'
                    credit_score_number:
                      type: integer
                      example: 632
                    credit_score_range:
                      type: string
                      enum:
                      - excellent
                      - good
                      - fair
                      - limited
                      - poor
                      example: excellent
                    credit_score_retrieval_date:
                      type: string
                      example: '2025-03-23'
                    debt_to_income_ratio:
                      type: number
                      example: 0.45
                    docusign_envelope_id:
                      type: string
                      example: 824b7612-c43f-4426-b34c-13b86bbb8ce4
                    employment_status:
                      type: string
                      enum:
                      - employed_full_time
                      - employed_part_time
                      - employed
                      - self_employed
                      - military
                      - retired
                      - not_employed
                      - other
                      example: employed_full_time
                    employment_industry:
                      enum:
                      - agriculture_or_farming
                      - construction
                      - education
                      - energy
                      - financial_services
                      - government_or_public_services
                      - health_services
                      - information_technology
                      - manufacturing
                      - professional_or_business_services
                      - transportation_or_utilities
                      - wholesale_or_retail_trade
                      - entertainment_or_events
                      - travel_leisure_or_hospitality
                      - restaurant
                      - retail
                      type: string
                      example: agriculture_or_farming
                    employment_pay_frequency:
                      type: string
                      enum:
                      - weekly
                      - biweekly
                      - semi_monthly
                      - monthly
                      example: weekly
                    education_level:
                      type: string
                      enum:
                      - masters
                      - high_school
                      - associates
                      - bachelors
                      - other_graduate
                      - postgraduate
                      - other
                      example: masters
                    hard_credit_pull_requested_at:
                      type: string
                      example: '2025-03-22'
                    holder_firstname:
                      type: string
                      example: Jane
                    holder_lastname:
                      type: string
                      example: Doe
                    housing_payment_monthly:
                      type: number
                      example: 1000
                    time_at_residence:
                      type: string
                      enum:
                      - more_than_3_years
                      - 1_to_3_years
                      - less_than_1_year
                      - 1_to_2_years
                      example: more_than_3_years
                    routing_number:
                      type: string
                      example: '*********'
                    ip_address:
                      type: string
                      example: **************
                    status:
                      type: string
                      enum:
                      - bank_details_complete
                      - offer_selected
                      - declined
                      - offered
                      - complete
                      - incomplete
                      example: bank_details_complete
                    noaa_successfully_sent_date:
                      type: string
                      example: '2025-03-24T19:10:33Z'
                    decline_reason_messages:
                      type: array
                      items: string
                      example:
                      - Some reason
                borrower:
                  type: object
                  properties:
                    address_street:
                      type: string
                      example: Suite 869 2717 Torp Parks
                    beyond_enrollment_date:
                      type: string
                      example: '2023-03-24'
                    program_duration_in_tmonths:
                      type: integer
                      example: 11
                    beyond_payment_amount:
                      type: number
                      example: 1000
                    city:
                      type: string
                      example: Michigan
                    date_of_birth:
                      type: string
                      example: '2000-03-24'
                    email:
                      type: string
                      example: <EMAIL>
                    first_name:
                      type: string
                      example: jane
                    last_name:
                      type: string
                      example: Doe
                    payment_adherence_ratio_3_months:
                      type: number
                      example: 123.45
                    payment_adherence_ratio_6_months:
                      type: number
                      example: 123.45
                    phone_number:
                      type: string
                      example: '1777456091'
                    program_id:
                      type: string
                      example: 7aa57500fcc4
                    service_entity_name:
                      type: string
                      enum:
                      - Beyond Finance
                      - Five Lakes Law Group
                      example: Beyond Finance
                    ssn:
                      type: string
                      example: ***********
                    state:
                      type: string
                      example: IL
                    total_amount_enrolled_debt:
                      type: number
                      example: 5000
                    zip_code:
                      type: string
                      example: '53594'
                    identity_id:
                      type: string
                      example: 824b7612-c43f-4426-b34c-13b86bbb8ce4
                    consecutive_payments_count:
                      type: integer
                      example: 12
                cft_account:
                  type: object
                  properties:
                    cft_account_balance:
                      type: number
                      example: 7500
                    cft_account_holder_name:
                      type: string
                      example: Jane Doe
                    cft_account_number:
                      type: string
                      example: '**********'
                    cft_bank_name:
                      type: string
                      example: Another Bank Inc
                    cft_routing_number:
                      type: string
                      example: '*********'
                verifications:
                  type: array
                  items:
                    type: object
                    properties:
                      document_type:
                        type: string
                        example: ID
                      verification_reasons:
                        type: string
                        example: identity,1903,FS01,Address was not provided at input
                      status:
                        type: string
                        example: accepted
                verification_documents:
                  type: array
                  items:
                    type: object
                    properties:
                      id:
                        type: string
                        example: '1'
                      name:
                        type: string
                        example: Social Security Card
                      mime_type:
                        type: string
                        example: application/pdf
                      s3_bucket:
                        type: string
                        example: abovelending
                      s3_key:
                        type: string
                        example: 1-social-security-card.pdf
                      tags:
                        type: array
                        items: string
                        example:
                        - Social Security Doc
                til_history:
                  type: object
                  properties:
                    docusign_envelope_id:
                      type: string
                      example: 8f5941ad-4424-4d06-9f0b-fa2baa1eb06b
                    til_data:
                      type: object
                      properties:
                        loan:
                          type: object
                          properties:
                            contract_date:
                              type: string
                              example: '2025-03-24'
                            agreement_date:
                              type: string
                              example: '2025-03-24'
                            apr:
                              type: number
                              example: 11.22
                        payment_schedule:
                          type: array
                          items:
                            type: object
                            properties:
                              amount:
                                type: string
                                example: "$100"
                              number_of_payments:
                                type: integer
                                example: 10
                              start_date:
                                type: string
                                example: 03/24/2025
                              raw_start_date:
                                type: string
                                example: 03/24/2025
                contract_documents:
                  type: array
                  items:
                    type: object
                    properties:
                      signed_storage_key:
                        type: string
                        example: contract_documents/test/8f5941ad-4424-4d06-9f0b-fa2baa1eb06b/signed-1234567890.pdf
                      document_type:
                        type: string
                        example: TRUTH_IN_LENDING
                      bucket_name:
                        type: string
                        example: some_bucket_name
                      file_size_bytes:
                        type: number
                        example: 10345
                      signed_at:
                        type: string
                        example: '2025-03-24T19:10:33Z'
                      ip_address:
                        type: string
                        example: **************
                      inputs:
                        type: object
                        example:
                          some_field: some_data
                consent_documents:
                  type: array
                  items:
                    type: object
                    properties:
                      document_type:
                        type: string
                        example: PRIVACY_POLICY
                      s3_key:
                        type: string
                        example: consent_documents/test/privacy-policy.pdf
                      s3_bucket:
                        type: string
                        example: some_bucket_name
                      signed_at:
                        type: string
                        example: '2025-03-24T12:10:33-07:00'
                      file_size_bytes:
                        type: number
                        example: 10345
                      ip_address:
                        type: string
                        example: **************
                      inputs:
                        type: object
                        example:
                          some_field: some_data
                decision_engine_documents:
                  type: array
                  items:
                    type: object
                    properties:
                      created_at:
                        type: string
                        example: '2025-03-24T12:10:33-07:00'
                      file_size_bytes:
                        type: number
                        example: 10345
                      mime_type:
                        type: string
                        example: application/xml
                      name:
                        type: string
                        example: informative_soft_pull
                      storage_bucket:
                        type: string
                        example: borrower_report_bucket
                      storage_key:
                        type: string
                        example: borrower_reports/test/21066dd7-55b4-4779-9197-a0a7e8711e61/informative_soft_pull-123456.xml
                funding:
                  type: object
                  properties:
                    processing_system:
                      type: string
                      example: Dash
  "/api/arix_webhooks":
    post:
      summary: Processes Arix webhook payload
      tags:
      - Loans
      parameters: []
      responses:
        '200':
          description: Successful
        '400':
          description: Bad Request
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                content:
                  type: object
                  properties:
                    Id:
                      type: integer
                    LoanId:
                      type: string
                    Status:
                      type: integer
                    TimeStamp:
                      type: string
                    DateInserted:
                      type: string
                  required:
                  - LoanId
              required:
              - content
  "/api/loan_pro_webhooks":
    post:
      summary: Processes LoanPro webhook payload
      tags:
      - LoanPro Webhooks
      parameters: []
      responses:
        '202':
          description: Accepted
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                ids:
                  type: object
                  properties:
                    loan_id:
                      type: integer
                    primary_customer_id:
                      type: integer
                  required:
                  - loan_id
                  - primary_customer_id
                event_type:
                  type: string
                context:
                  type: object
                  properties:
                    days_past_due:
                      type: integer
              required:
              - ids
              - event_type
            examples:
              Payment Past Due SMS:
                summary: LoanPro webhook event used to signal a payment past due sms
                value:
                  context:
                    days_past_due: 7
                  event_type: payment_past_due
                  ids:
                    loan_id: 12345
                    primary_customer_id: 98765
              Charge Off Email:
                summary: LoanPro webhook event used to signal a charge_off email
                value:
                  context: {}
                  event_type: charge_off
                  ids:
                    loan_id: 12345
                    primary_customer_id: 98765
              Debt Validation Email:
                summary: LoanPro webhook event used to signal a debt_validation email
                value:
                  context: {}
                  event_type: debt_validation
                  ids:
                    loan_id: 12345
                    primary_customer_id: 98765
              Due Date Change Email:
                summary: LoanPro webhook event used to signal a due_date_change email
                value:
                  context: {}
                  event_type: due_date_change
                  ids:
                    loan_id: 12345
                    primary_customer_id: 98765
              Missed Payment Email:
                summary: LoanPro webhook event used to signal a missed_payment email
                value:
                  context: {}
                  event_type: missed_payment
                  ids:
                    loan_id: 12345
                    primary_customer_id: 98765
              Notice Of Default Ks Email:
                summary: LoanPro webhook event used to signal a notice_of_default_ks
                  email
                value:
                  context: {}
                  event_type: notice_of_default_ks
                  ids:
                    loan_id: 12345
                    primary_customer_id: 98765
              Notice Of Default Mo Email:
                summary: LoanPro webhook event used to signal a notice_of_default_mo
                  email
                value:
                  context: {}
                  event_type: notice_of_default_mo
                  ids:
                    loan_id: 12345
                    primary_customer_id: 98765
              Notice Of Default Wi Email:
                summary: LoanPro webhook event used to signal a notice_of_default_wi
                  email
                value:
                  context: {}
                  event_type: notice_of_default_wi
                  ids:
                    loan_id: 12345
                    primary_customer_id: 98765
              Payment Posted Email:
                summary: LoanPro webhook event used to signal a payment_posted email
                value:
                  context: {}
                  event_type: payment_posted
                  ids:
                    loan_id: 12345
                    primary_customer_id: 98765
              Payment Reminder Ach Email:
                summary: LoanPro webhook event used to signal a payment_reminder_ach
                  email
                value:
                  context: {}
                  event_type: payment_reminder_ach
                  ids:
                    loan_id: 12345
                    primary_customer_id: 98765
              Payment Reminder Manual Email:
                summary: LoanPro webhook event used to signal a payment_reminder_manual
                  email
                value:
                  context: {}
                  event_type: payment_reminder_manual
                  ids:
                    loan_id: 12345
                    primary_customer_id: 98765
              Payoff Email:
                summary: LoanPro webhook event used to signal a payoff email
                value:
                  context: {}
                  event_type: payoff
                  ids:
                    loan_id: 12345
                    primary_customer_id: 98765
              Post Funding Survey Email:
                summary: LoanPro webhook event used to signal a post_funding_survey
                  email
                value:
                  context: {}
                  event_type: post_funding_survey
                  ids:
                    loan_id: 12345
                    primary_customer_id: 98765
              Statement Of Rights Dc Email:
                summary: LoanPro webhook event used to signal a statement_of_rights_dc
                  email
                value:
                  context: {}
                  event_type: statement_of_rights_dc
                  ids:
                    loan_id: 12345
                    primary_customer_id: 98765
              Welcome Email:
                summary: LoanPro webhook event used to signal a welcome email
                value:
                  context: {}
                  event_type: welcome
                  ids:
                    loan_id: 12345
                    primary_customer_id: 98765
              Ach Authorization Email:
                summary: LoanPro webhook event used to signal a ach_authorization
                  email
                value:
                  context: {}
                  event_type: ach_authorization
                  ids:
                    loan_id: 12345
                    primary_customer_id: 98765
              Attorney Retained Enabled opt out communication:
                summary: LoanPro webhook event used to signal a attorney_retained_enabled
                value:
                  context: {}
                  event_type: attorney_retained_enabled
                  ids:
                    loan_id: 12345
              Bankruptcy Enabled opt out communication:
                summary: LoanPro webhook event used to signal a bankruptcy_enabled
                value:
                  context: {}
                  event_type: bankruptcy_enabled
                  ids:
                    loan_id: 12345
              Cease And Desist Enabled opt out communication:
                summary: LoanPro webhook event used to signal a cease_and_desist_enabled
                value:
                  context: {}
                  event_type: cease_and_desist_enabled
                  ids:
                    loan_id: 12345
              Debt Settlement Company Enabled opt out communication:
                summary: LoanPro webhook event used to signal a debt_settlement_company_enabled
                value:
                  context: {}
                  event_type: debt_settlement_company_enabled
                  ids:
                    loan_id: 12345
              Do Not Call Enabled opt out communication:
                summary: LoanPro webhook event used to signal a do_not_call_enabled
                value:
                  context: {}
                  event_type: do_not_call_enabled
                  ids:
                    loan_id: 12345
              Attorney Retained Disabled opt in communication:
                summary: LoanPro webhook event used to signal a attorney_retained_disabled
                value:
                  context: {}
                  event_type: attorney_retained_disabled
                  ids:
                    loan_id: 12345
              Bankruptcy Disabled opt in communication:
                summary: LoanPro webhook event used to signal a bankruptcy_disabled
                value:
                  context: {}
                  event_type: bankruptcy_disabled
                  ids:
                    loan_id: 12345
              Cease And Desist Disabled opt in communication:
                summary: LoanPro webhook event used to signal a cease_and_desist_disabled
                value:
                  context: {}
                  event_type: cease_and_desist_disabled
                  ids:
                    loan_id: 12345
              Debt Settlement Company Disabled opt in communication:
                summary: LoanPro webhook event used to signal a debt_settlement_company_disabled
                value:
                  context: {}
                  event_type: debt_settlement_company_disabled
                  ids:
                    loan_id: 12345
              Do Not Call Disabled opt in communication:
                summary: LoanPro webhook event used to signal a do_not_call_disabled
                value:
                  context: {}
                  event_type: do_not_call_disabled
                  ids:
                    loan_id: 12345
              Past Due 7 To 28 Days Past Due Email:
                summary: LoanPro webhook event used to signal a past_due_7_to_28_days
                  past due email
                value:
                  context: {}
                  event_type: past_due_7_to_28_days
                  ids:
                    loan_id: 12345
                    primary_customer_id: 98765
              Past Due 35 To 56 Days Past Due Email:
                summary: LoanPro webhook event used to signal a past_due_35_to_56_days
                  past due email
                value:
                  context: {}
                  event_type: past_due_35_to_56_days
                  ids:
                    loan_id: 12345
                    primary_customer_id: 98765
              Past Due 63 To 84 Days Past Due Email:
                summary: LoanPro webhook event used to signal a past_due_63_to_84_days
                  past due email
                value:
                  context: {}
                  event_type: past_due_63_to_84_days
                  ids:
                    loan_id: 12345
                    primary_customer_id: 98765
              Past Due 90 To 115 Days Past Due Email:
                summary: LoanPro webhook event used to signal a past_due_90_to_115_days
                  past due email
                value:
                  context: {}
                  event_type: past_due_90_to_115_days
                  ids:
                    loan_id: 12345
                    primary_customer_id: 98765
              Past Due 1 Day New Past Due Email:
                summary: LoanPro webhook event used to signal a past_due_1_day_new
                  past due email
                value:
                  context: {}
                  event_type: past_due_1_day_new
                  ids:
                    loan_id: 12345
                    primary_customer_id: 98765
              Past Due 7 Days New Past Due Email:
                summary: LoanPro webhook event used to signal a past_due_7_days_new
                  past due email
                value:
                  context: {}
                  event_type: past_due_7_days_new
                  ids:
                    loan_id: 12345
                    primary_customer_id: 98765
              Past Due 14 To 28 Days New Past Due Email:
                summary: LoanPro webhook event used to signal a past_due_14_to_28_days_new
                  past due email
                value:
                  context: {}
                  event_type: past_due_14_to_28_days_new
                  ids:
                    loan_id: 12345
                    primary_customer_id: 98765
              Past Due 35 To 56 Days New Past Due Email:
                summary: LoanPro webhook event used to signal a past_due_35_to_56_days_new
                  past due email
                value:
                  context: {}
                  event_type: past_due_35_to_56_days_new
                  ids:
                    loan_id: 12345
                    primary_customer_id: 98765
              Past Due 63 To 84 Days New Past Due Email:
                summary: LoanPro webhook event used to signal a past_due_63_to_84_days_new
                  past due email
                value:
                  context: {}
                  event_type: past_due_63_to_84_days_new
                  ids:
                    loan_id: 12345
                    primary_customer_id: 98765
              Past Due 91 To 115 Days New Past Due Email:
                summary: LoanPro webhook event used to signal a past_due_91_to_115_days_new
                  past due email
                value:
                  context: {}
                  event_type: past_due_91_to_115_days_new
                  ids:
                    loan_id: 12345
                    primary_customer_id: 98765
  "/api/servicing/auth/resume":
    get:
      summary: Retrieves information needed to resume onboarding
      tags:
      - Auth
      parameters:
      - name: authorization
        in: header
        schema:
          type: string
      responses:
        '200':
          description: Successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: string
                  borrower:
                    type: object
                    properties:
                      first_name:
                        type: string
                      last_name:
                        type: string
                      id:
                        type: string
                      email:
                        type: string
                  loan:
                    type: object
                    properties:
                      id:
                        type: string
                      unified_id:
                        type: string
                      status:
                        type: string
                      product_type:
                        type: string
                  additional_information:
                    type: object
                    properties:
                      loanpro_loan_id:
                        type: string
                        required: false
                        description: Present when loan's truth in lending has been
                          signed.
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                type: object
                properties:
                  statusCode:
                    type: integer
                    example: 401
                  error:
                    type: string
                    example: Unauthorized
                  message:
                    type: string
                    example: Invalid authorization
                required:
                - statusCode
                - error
                - message
  "/api/servicing/borrowers/me":
    get:
      summary: Retrieves information about the borrower
      tags:
      - Loan Customers
      parameters:
      - name: authorization
        in: header
        schema:
          type: string
      responses:
        '200':
          description: Successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: string
                  first_name:
                    type:
                    - 'null'
                    - string
                  last_name:
                    type:
                    - 'null'
                    - string
                  email:
                    type: string
                  ssn:
                    type:
                    - 'null'
                    - string
                  date_of_birth:
                    type:
                    - 'null'
                    - string
                  aditional_id:
                    type: string
                  address_street:
                    type:
                    - 'null'
                    - string
                  address_apt:
                    type:
                    - 'null'
                    - string
                  city:
                    type:
                    - 'null'
                    - string
                  state:
                    type:
                    - 'null'
                    - string
                  zip_code:
                    type:
                    - 'null'
                    - string
                  phone_number:
                    type:
                    - 'null'
                    - string
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                type: object
                properties:
                  statusCode:
                    type: integer
                    example: 401
                  error:
                    type: string
                    example: Unauthorized
                  message:
                    type: string
                    example: Invalid authorization
                required:
                - statusCode
                - error
                - message
  "/api/servicing/customers":
    get:
      summary: Retrieves all customers details for a loan
      tags:
      - Loan Customers
      responses:
        '200':
          description: Successful
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    id:
                      type: integer
                    firstName:
                      type: string
                    lastName:
                      type: string
                    email:
                      type: string
                    customerRole:
                      type: string
                    mail_address:
                      type: hash
                    address1:
                      type: string
                    city:
                      type: string
                    state:
                      type: string
                    zipcode:
                      type: string
                    country:
                      type: string
                    phones:
                      type: hash
                    phone:
                      type: string
                    phone_id:
                      type: string
                    isPrimary:
                      type: string
                    isSecondary:
                      type: string
                  required: []
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                type: object
                properties:
                  statusCode:
                    type: integer
                    example: 401
                  error:
                    type: string
                    example: Unauthorized
                  message:
                    type: string
                    example: Invalid authorization
                required:
                - statusCode
                - error
                - message
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                type: object
                properties:
                  statusCode:
                    type: integer
                    example: 500
                  error:
                    type: string
                    example: Internal Server Error
                  message:
                    type: string
                    example: Internal Server Error
                required:
                - statusCode
                - error
                - message
  "/api/servicing/documents/my-documents":
    get:
      summary: Retrieves documents
      tags:
      - Documents
      parameters:
      - name: loan_id
        in: query
        required: true
        schema:
          type: integer
      responses:
        '200':
          description: Successful
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    uri:
                      type: string
                    name:
                      type: string
                    type:
                      type: string
                    created:
                      type: string
                      format: date-time
                  required:
                  - uri
                  - name
                  - type
                  - created
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                type: object
                properties:
                  statusCode:
                    type: integer
                    example: 401
                  error:
                    type: string
                    example: Unauthorized
                  message:
                    type: string
                    example: Invalid authorization
                required:
                - statusCode
                - error
                - message
  "/api/servicing/loan/dashboard-details":
    get:
      summary: Retrieves loan details
      tags:
      - Loan
      responses:
        '200':
          description: Successful
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    apr:
                      type: string
                    currentPaymentDue:
                      type: string
                    currentDueDate:
                      type: string
                    remainingBalance:
                      type: string
                    nextPaymentAmount:
                      type: string
                    nextPaymentDate:
                      type: string
                    loanAmount:
                      type: string
                    numberOfTerms:
                      type: string
                    loanPayment:
                      type: string
                    numberOfRemainingTerms:
                      type: string
                    overdueAmount:
                      type: string
                    remainingPrincipalBalance:
                      type: string
                    loanStatusText:
                      type: string
                    daysPastDue:
                      type: integer
                    subStatus:
                      type: string
                    subStatusId:
                      type: integer
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                type: object
                properties:
                  statusCode:
                    type: integer
                    example: 401
                  error:
                    type: string
                    example: Unauthorized
                  message:
                    type: string
                    example: Invalid authorization
                required:
                - statusCode
                - error
                - message
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                type: object
                properties:
                  statusCode:
                    type: integer
                    example: 500
                  error:
                    type: string
                    example: Internal Server Error
                  message:
                    type: string
                    example: Internal Server Error
                required:
                - statusCode
                - error
                - message
  "/api/servicing/loan/loan-payoff":
    get:
      summary: Retrieves payoff information for the loan
      tags:
      - Loan Payoff
      parameters:
      - name: authorization
        in: header
        schema:
          type: string
      responses:
        '200':
          description: Successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  '2023-06-06':
                    type: object
                    properties:
                      date:
                        type: string
                      payoff:
                        type: number
                      change:
                        tupe: number
                      dailyInterest:
                        type: number
                      details:
                        type: object
                        properties:
                          principal:
                            type: number
                          fees:
                            type: number
                          interest:
                            type: number
                          escrow:
                            type: number
                          escrowAlt:
                            type: number
                          totalEscrow:
                            type: number
                          escrowSubsets:
                            type: object
                            properties:
                              '2':
                                type: object
                                properties:
                                  escrow:
                                    type: number
                                  escrowAlt:
                                    type: number
                                  totalEscrow:
                                    type: number
                          finalInterest:
                            type: number
                          paymentsPending:
                            type: number
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                type: object
                properties:
                  statusCode:
                    type: integer
                    example: 401
                  error:
                    type: string
                    example: Unauthorized
                  message:
                    type: string
                    example: Invalid authorization
                required:
                - statusCode
                - error
                - message
  "/api/servicing/loan/link-payment-profile":
    post:
      summary: Links a Debit Card Payment Profile to a Customer in LoanPro
      tags:
      - Debit Card Payments
      parameters: []
      responses:
        '200':
          description: Successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                required:
                - success
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                type: object
                properties:
                  statusCode:
                    type: integer
                    example: 500
                  error:
                    type: string
                    example: Internal Server Error
                  message:
                    type: string
                    example: Internal Server Error
                required:
                - statusCode
                - error
                - message
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                type: object
                properties:
                  statusCode:
                    type: integer
                    example: 401
                  error:
                    type: string
                    example: Unauthorized
                  message:
                    type: string
                    example: Invalid authorization
                required:
                - statusCode
                - error
                - message
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                loan_id:
                  type: integer
                token:
                  type: string
                amount:
                  type: number
                date:
                  type: string
                  format: date
                firstName:
                  type: string
                accountNumberLast4Digits:
                  type: string
              required:
              - token
              - date
  "/api/servicing/loan/debit-card-payment":
    post:
      summary: Processes a debit card payment
      tags:
      - Debit Card Payments
      parameters: []
      responses:
        '200':
          description: Successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                required:
                - success
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                type: object
                properties:
                  statusCode:
                    type: integer
                    example: 500
                  error:
                    type: string
                    example: Internal Server Error
                  message:
                    type: string
                    example: Internal Server Error
                required:
                - statusCode
                - error
                - message
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                type: object
                properties:
                  statusCode:
                    type: integer
                    example: 401
                  error:
                    type: string
                    example: Unauthorized
                  message:
                    type: string
                    example: Invalid authorization
                required:
                - statusCode
                - error
                - message
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                loan_id:
                  type: integer
                amount:
                  type: number
                date:
                  type: string
                  format: date
                firstName:
                  type: string
                accountNumberLast4Digits:
                  type: string
              required:
              - amount
              - date
  "/api/servicing/loan/payments":
    post:
      summary: Creates a payment
      tags:
      - Payments
      parameters: []
      responses:
        '200':
          description: Successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  achPaymentNotificationEmailSent:
                    type: string
                required:
                - success
                - achPaymentNotificationEmailSent
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                type: object
                properties:
                  statusCode:
                    type: integer
                    example: 401
                  error:
                    type: string
                    example: Unauthorized
                  message:
                    type: string
                    example: Invalid authorization
                required:
                - statusCode
                - error
                - message
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                type: object
                properties:
                  statusCode:
                    type: integer
                    example: 400
                  error:
                    type: string
                    example: Bad Request
                  message:
                    type: string
                    examples:
                      Example 1:
                        value: 'Validation failed: First name can''t be blank'
                      Example 2:
                        value: 'Validation failed: Account number last4 should be
                          4 digits numeric'
                      Example 3:
                        value: 'Validation failed: Account number last4 can''t be
                          blank, Account number last4 should be 4 digits numeric'
                      Example 4:
                        value: 'Validation failed: Apply date Invalid payment date.
                          Expected YYYY-MM-DD but received: '''''
                      Example 5:
                        value: 'Validation failed: Payment profile can''t be blank'
                      Example 6:
                        value: 'Validation failed: Amount Invalid 0.0'
                required:
                - statusCode
                - error
                - message
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                type: object
                properties:
                  statusCode:
                    type: integer
                    example: 500
                  error:
                    type: string
                    example: Internal Server Error
                  message:
                    type: string
                    example: Internal Server Error
                required:
                - statusCode
                - error
                - message
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                amount:
                  type: number
                date:
                  type: string
                  format: date
                datetime:
                  type: string
                  format: date
                timezone:
                  type: string
                paymentProfileId:
                  type: integer
                accountNumberLast4Digits:
                  type: string
                firstName:
                  type: string
              required:
              - amount
              - date
              - datetime
              - timezone
              - paymentProfileId
  "/api/servicing/loan/payment-history":
    get:
      summary: Retrieves payment history
      tags:
      - Payment History
      parameters:
      - name: borrower_id
        in: query
        schema:
          type: integer
      responses:
        '200':
          description: Successful
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    payments:
                      type: array
                      items:
                        type: object
                        properties:
                          id:
                            type: integer
                            example: '12345'
                          amount:
                            type: string
                          date:
                            type: string
                            format: date-time
                          type:
                            type: string
                          status:
                            type: string
                          isCustomerInitiated:
                            type: boolean
                          interest:
                            type: string
                          principal:
                            type: string
                          beforeBalance:
                            type: string
                          afterBalance:
                            type: string
                        required:
                        - id
                        - amount
                        - date
                        - type
                        - status
                        - isCustomerInitiated
                        - interest
                        - principal
                        - afterBalance
                    count:
                      type: integer
                      example: 1
                    loanpro_loan_id:
                      type: integer
                      example: 54321
                  required:
                  - payments
                  - count
                  - loanpro_loan_id
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                type: object
                properties:
                  statusCode:
                    type: integer
                    example: 401
                  error:
                    type: string
                    example: Unauthorized
                  message:
                    type: string
                    example: Invalid authorization
                required:
                - statusCode
                - error
                - message
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                type: object
                properties:
                  statusCode:
                    type: integer
                    example: 500
                  error:
                    type: string
                    example: Internal Server Error
                  message:
                    type: string
                    example: Internal Server Error
                required:
                - statusCode
                - error
                - message
  "/api/servicing/loan/recent-payment-history":
    get:
      summary: Retrieves recent payment history
      tags:
      - Recent Payment History
      parameters:
      - name: borrower_id
        in: query
        schema:
          type: integer
      responses:
        '200':
          description: Successful
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    payments:
                      type: array
                      items:
                        type: object
                        properties:
                          id:
                            type: integer
                            example: '12345'
                          amount:
                            type: string
                          date:
                            type: string
                            format: date-time
                          type:
                            type: string
                          status:
                            type: string
                          isCustomerInitiated:
                            type: boolean
                          interest:
                            type: string
                          principal:
                            type: string
                          beforeBalance:
                            type: string
                          afterBalance:
                            type: string
                        required:
                        - id
                        - amount
                        - date
                        - type
                        - status
                        - isCustomerInitiated
                        - interest
                        - principal
                        - afterBalance
                    count:
                      type: integer
                      example: 1
                    loanpro_loan_id:
                      type: integer
                      example: 54321
                  required:
                  - payments
                  - count
                  - loanpro_loan_id
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                type: object
                properties:
                  statusCode:
                    type: integer
                    example: 401
                  error:
                    type: string
                    example: Unauthorized
                  message:
                    type: string
                    example: Invalid authorization
                required:
                - statusCode
                - error
                - message
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                type: object
                properties:
                  statusCode:
                    type: integer
                    example: 500
                  error:
                    type: string
                    example: Internal Server Error
                  message:
                    type: string
                    example: Internal Server Error
                required:
                - statusCode
                - error
                - message
  "/api/servicing/loan/upcoming-payments/cancel/{id}":
    put:
      summary: Cancels upcoming autopay
      tags:
      - Cancel Upcoming Autopay
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
      responses:
        '200':
          description: Successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                required:
                - success
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                type: object
                properties:
                  statusCode:
                    type: integer
                    example: 400
                  error:
                    type: string
                    example: Bad Request
                  message:
                    type: string
                    example: Autopay entity not found
                required:
                - statusCode
                - error
                - message
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                type: object
                properties:
                  statusCode:
                    type: integer
                    example: 401
                  error:
                    type: string
                    example: Unauthorized
                  message:
                    type: string
                    example: Invalid authorization
                required:
                - statusCode
                - error
                - message
  "/api/servicing/loan/upcoming-payments":
    get:
      summary: Retrieves upcoming payments
      tags:
      - Upcoming Payments
      responses:
        '200':
          description: Successful
          content:
            application/json:
              schema:
                type: array
                items:
                  type: string
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                type: object
                properties:
                  statusCode:
                    type: integer
                    example: 400
                  error:
                    type: string
                    example: Bad Request
                  message:
                    type: string
                    example: Borrower not found
                required:
                - statusCode
                - error
                - message
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                type: object
                properties:
                  statusCode:
                    type: integer
                    example: 401
                  error:
                    type: string
                    example: Unauthorized
                  message:
                    type: string
                    example: Invalid authorization
                required:
                - statusCode
                - error
                - message
  "/servicing/loan/payment_profiles":
    get:
      summary: Retrieve Payment Profiles
      tags:
      - Payment Profiles
      responses:
        '200':
          description: All Payment Profiles retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  paymentProfiles:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          example: 11723
                        isPrimary:
                          type: integer
                          example: 0
                        isSecondary:
                          type: integer
                          example: 0
                        title:
                          type: string
                          example: Debit Card Ending 1234 for 'Christina'
                        type:
                          type: string
                          example: paymentAccount.type.credit
                        checkingAccountId:
                          type: integer
                          example: 0
                          nullable: true
                        active:
                          type: integer
                          example: 1
                        visible:
                          type: integer
                          example: 1
                        bankName:
                          type: string
                          example: string
                        accountNumber:
                          type: integer
                          example: string
                        routingNumber:
                          type: string
                          example: '*********'
                      required:
                      - id
                      - isPrimary
                      - isSecondary
                      - title
                      - type
                      - active
                      - visible
                required:
                - paymentProfiles
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                type: object
                properties:
                  statusCode:
                    type: integer
                    example: 401
                  error:
                    type: string
                    example: Unauthorized
                  message:
                    type: string
                    example: Invalid authorization
                required:
                - statusCode
                - error
                - message
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                type: object
                properties:
                  statusCode:
                    type: integer
                    example: 400
                  error:
                    type: string
                    example: Bad Request
                  message:
                    type: string
                    example: Boom!
                required:
                - statusCode
                - error
                - message
  "/api/servicing/loan_transactions":
    get:
      summary: Retrieves transactions for a loan
      tags:
      - Loan Transactions
      responses:
        '200':
          description: Successful
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    transactionId:
                      type: integer
                    transactionDate:
                      type: string
                      format: date
                    paymentAmount:
                      type: string
                    paymentInterest:
                      type: string
                    paymentPrincipal:
                      type: string
                    paymentDiscount:
                      type: string
                    paymentFees:
                      type: string
                    principalBalance:
                      type: string
                  required:
                  - transactionId
                  - transactionDate
                  - paymentAmount
                  - paymentInterest
                  - paymentPrincipal
                  - paymentDiscount
                  - paymentFees
                  - principalBalance
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                type: object
                properties:
                  statusCode:
                    type: integer
                    example: 401
                  error:
                    type: string
                    example: Unauthorized
                  message:
                    type: string
                    example: Invalid authorization
                required:
                - statusCode
                - error
                - message
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                type: object
                properties:
                  statusCode:
                    type: integer
                    example: 500
                  error:
                    type: string
                    example: Internal Server Error
                  message:
                    type: string
                    example: Internal Server Error
                required:
                - statusCode
                - error
                - message
  "/servicing/payment_profiles/payment_profiles":
    get:
      summary: Retrieve Payment Profiles
      tags:
      - Payment Profiles
      responses:
        '200':
          description: All Payment Profiles retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  paymentProfiles:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          example: 11723
                        isPrimary:
                          type: integer
                          example: 0
                        isSecondary:
                          type: integer
                          example: 0
                        title:
                          type: string
                          example: Debit Card Ending 1234 for 'Christina'
                        type:
                          type: string
                          example: paymentAccount.type.credit
                        checkingAccountId:
                          type: integer
                          example: 0
                          nullable: true
                        active:
                          type: integer
                          example: 1
                        visible:
                          type: integer
                          example: 1
                        bankName:
                          type: string
                          example: string
                        accountNumber:
                          type: integer
                          example: string
                        routingNumber:
                          type: string
                          example: '*********'
                      required:
                      - id
                      - isPrimary
                      - isSecondary
                      - title
                      - type
                      - active
                      - visible
                required:
                - paymentProfiles
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                type: object
                properties:
                  statusCode:
                    type: integer
                    example: 401
                  error:
                    type: string
                    example: Unauthorized
                  message:
                    type: string
                    example: Invalid authorization
                required:
                - statusCode
                - error
                - message
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                type: object
                properties:
                  statusCode:
                    type: integer
                    example: 400
                  error:
                    type: string
                    example: Bad Request
                  message:
                    type: string
                    example: Boom!
                required:
                - statusCode
                - error
                - message
